# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  product: BosonService
  # fullname defines name of boson-broker and references every where.
  fullname: "boson-broker"
  # bosonBrokerImage defines boson-broker image and tag.
  bosonBrokerImage: registry.sensetime.com/sensecore-boson/boson-broker:v1.7.0-7-g08b557d-20231020085153
  # hook image
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:v1.8.0-1-g2b3dabe-20231116213725
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # enableServiceMonitor 定义是否生成对接Diamond Prometheus 的服务的ServiceMonitor资源的yaml
  enableServiceMonitor: true
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName: network-service.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
    - key: diamond.sensetime.com/role-business-app
      values:
        - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
    - effect: NoSchedule
      key: diamond.sensetime.com/role-business-app
      operator: Equal
      value: sensecore
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-resource-prp
      operator: Equal
      value: cn-sh-01a-control
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-ns-layer
      operator: Equal
      value: iaas
    - effect: NoExecute
      key: diamond.sensetime.com/role-k8s-master
      operator: Equal
      value: enabled
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  rocketmqLogLevel: warn
  resource:
    cpuLimits: 1
    memLimits: 1Gi
  # reginal的pgsql连接信息
  pgsql:
    host: xxx
    port: 12345
    user: xxx
    db: boson_service_v2
    password: ""
    show_sql: false
  # rocketmq
  rocketmq:
    default:
      nameServers:
      - "*************:9876"
      - "***********:9876"
      dcplConsumerGroupName: sensetime-core-network-v1-kk-consumer
      brokerRMProducerGroupName: sensetime-core-network-v1-kk-producer-rm
      brokerBossProducerGroupName: sensetime-core-network-v1-kk-producer-boss
      brokerNoticeProducerGroupName: sensetime-core-network-v1-kk-producer-notice
      retryInterval: 5
      dcplTopic:  sensetime-core-network-dc-v1-cn-sh-01z
      noticeTopic: sensetime-core-message-engine-msg
      rmAckTopic: sensetime-core-rm-resource-state-sync
      bossAckTopic: sensetime-core-resource-operation-result
      instanceName: boson-kk-instance
      accessKey: rocketmq-ak-boson-provider
      secretKey: e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce
    cloudAudit:
      nameServers:
        - "xx.xx.xx.xx:9876"
        - "xx.xx.xx.xx:9876"
      instanceName: "sensetime-core-networkCloudAudit-v1-cn-sh-01z"
      topics:
        vpc:
          topic: "sensetime-core-trail-vpc-operation"
          producerGroupName: "sensetime-core-trail-vpc-producer"
          accessKey: "xxx"
          secretKey: "xxx"
  # dns
  dns:
    name_server:
      #cn-sh-01a: http://network-internal.cn-sh-01.sensecoreapi.dev
  # business
  business:
    iam_auth_addr: https://iam-internal.sensecoreapi.dev
  ovn:
    # format is tcp:ip1:port,tcp:ip2:port,tcp:ip3:port
    ovn_ic: "tcp:ip1:6645,tcp:ip2:6645,tcp:ip3:6645"
    ts_subnet_cidr: "***********/16"
  # bosonBroker variables
  bosonBroker:
    # replicas defines number of pods running in cluster.
    replicas: 3
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 1
    # memLimits defines memory limit used by pods.
    memLimits: 1Gi
    # 是否在 deployment 中增加当前时间的 annotation，以便在部署是可以更新deployment 出发 rolling update
    generate_deployment_annotations_timestamp: false
    defaults:
      vpc_default_region: "dev-cn-sh-01z"
      pg_lock_timeout: 30
      cloud_audit_enable: true
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000
    # service defines ports for service.
    service:
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "51080"
        # grpc port number for container, service, ingress.
        grpc: "51090"
        # metrics port number for container, service, service monitor.
        metrics: "51030"
