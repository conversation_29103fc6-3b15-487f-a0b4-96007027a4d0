{{- $grpcPort := .Values.global.bosonBroker.service.ports.grpc -}}
{{- $httpPort := .Values.global.bosonBroker.service.ports.http -}}
{{- $metricsPort := .Values.global.bosonBroker.service.ports.metrics -}}
apiVersion: v1
data:
  boson-broker.yaml: |-
    env: {{ .Values.global.envName }}
    pg:
      host: {{ .Values.global.pgsql.host }}
      port: {{ .Values.global.pgsql.port | quote }}
      user: {{ .Values.global.pgsql.user }}
      password: {{ .Values.global.pgsql.password }}
      db: {{ .Values.global.pgsql.db }}
      show_sql: {{ .Values.global.pgsql.show_sql }}
    rocketmq:
      default:
        nameServer:
        {{- range .Values.global.rocketmq.default.nameServers }}
        - {{ . | quote }}
        {{- end }}
        dcplConsumerGroupName: {{ .Values.global.rocketmq.default.dcplConsumerGroupName }}
        brokerRMProducerGroupName: {{ .Values.global.rocketmq.default.brokerRMProducerGroupName }}
        brokerBossProducerGroupName: {{ .Values.global.rocketmq.default.brokerBossProducerGroupName }}
        brokerNoticeProducerGroupName: {{ .Values.global.rocketmq.default.brokerNoticeProducerGroupName }}
        retryInterval: {{ .Values.global.rocketmq.default.retryInterval }}
        dcplTopic:  {{ .Values.global.rocketmq.default.dcplTopic }}
        noticeTopic: {{ .Values.global.rocketmq.default.noticeTopic }}
        rmAckTopic: {{ .Values.global.rocketmq.default.rmAckTopic }}
        bossAckTopic: {{ .Values.global.rocketmq.default.bossAckTopic }}
        instanceName: {{ .Values.global.rocketmq.default.instanceName }}
        accessKey: {{ .Values.global.rocketmq.default.accessKey }}
        secretKey: {{ .Values.global.rocketmq.default.secretKey }}
      cloudAudit:
        nameServer:
        {{- range .Values.global.rocketmq.cloudAudit.nameServers }}
          - {{ . | quote }}
        {{- end }}
        instanceName: {{ .Values.global.rocketmq.cloudAudit.instanceName }}
        topics:
          vpc:
            topic: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.topic }}
            producerGroupName: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.producerGroupName }}
            accessKey: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.accessKey }}
            secretKey: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.secretKey }}
    dns:
      name_server:
      {{- range $key, $value := .Values.global.dns.name_server }}
        {{ $key }}: {{ $value }}
      {{- end }}
    business:
      iam_auth_addr: {{ .Values.global.business.iam_auth_addr }}
    ovn:
      ovn_ic: {{ .Values.global.ovn.ovn_ic | quote }}
      ts_subnet_cidr: {{ .Values.global.ovn.ts_subnet_cidr }}
    # Provider的默认参数
    {{- with .Values.global.bosonBroker.defaults }}
    boson_default:
      vpc_default_region: {{ .vpc_default_region }}
      pg_lock_timeout: {{ .pg_lock_timeout }}
      cloud_audit_enable: {{ .cloud_audit_enable }}
      grpc_port: {{ $grpcPort }}
      http_port: {{ $httpPort }}
      metrics_port: {{ $metricsPort }}
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: {{ .slow_ops_threshold_milli_seconds_config.http_request_rt }}
        mq_request_rt: {{ .slow_ops_threshold_milli_seconds_config.mq_request_rt }}
        db_request_rt: {{ .slow_ops_threshold_milli_seconds_config.db_request_rt }}
        k8s_request_rt: {{ .slow_ops_threshold_milli_seconds_config.k8s_request_rt }}
    {{- end }}

kind: ConfigMap
metadata:
  name: {{ .Values.global.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonBroker.labels" . | nindent 4 }}
