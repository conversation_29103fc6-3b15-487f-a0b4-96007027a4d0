---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.fullname }}-service
    {{- include "bosonBroker.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    boson-broker-master: "true"
  ports:
  - name: http
    port: {{ .Values.global.bosonBroker.service.ports.http}}
    protocol: TCP
  - name: grpc
    port: {{ .Values.global.bosonBroker.service.ports.grpc}}
    protocol: TCP
  - name: metrics
    port: {{ .Values.global.bosonBroker.service.ports.metrics}}
    protocol: TCP
