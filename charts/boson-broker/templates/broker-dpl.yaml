---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.fullname }}
    {{- include "bosonBroker.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.fullname }}-service
  replicas: {{ int .Values.global.bosonBroker.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.fullname }}-service
    spec:
      priorityClassName: system-cluster-critical
      serviceAccount: {{ .Values.global.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.fullname }}-service
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range $index, $nodeSelectorTerms := . }}
            - matchExpressions:
            {{- if $nodeSelectorTerms.matchExpressions }}
              {{- range $expressions := $nodeSelectorTerms.matchExpressions }}
              - key: {{ $expressions.key | quote }}
                operator: In
                values:
                {{- range $expressions.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- else }}
              - key: {{ $nodeSelectorTerms.key | quote }}
                operator: In
                values:
                {{- range $nodeSelectorTerms.values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.fullname }}
        image: "{{ .Values.global.bosonBrokerImage }}"
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: {{ .Values.global.bosonBroker.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ .Values.global.bosonBroker.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ .Values.global.bosonBroker.service.ports.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonBroker.cpuLimits }}
            memory: {{ .Values.global.bosonBroker.memLimits }}
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: ROCKETMQ_GO_LOG_LEVEL
          value: {{ .Values.global.rocketmqLogLevel }}
        - name: BOSON_BROKER_VAR_NAME
          value: "{{ .Values.global.envName }}"
        - name: BOSON_BROKER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_BROKER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_BROKER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_BROKER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /metrics
            port: {{ .Values.global.bosonBroker.service.ports.metrics }}
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /metrics
            port: {{ .Values.global.bosonBroker.service.ports.metrics }}
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: config
          mountPath: /{{ .Values.global.fullname }}.yaml
          subPath: path/to/{{ .Values.global.fullname }}.yaml
      volumes:
      - configMap:
          name: {{ .Values.global.fullname }}-config
          items:
          - key: {{ .Values.global.fullname }}.yaml
            path: path/to/{{ .Values.global.fullname }}.yaml
        name: config
