# Boson Broker

<!-- TOC -->

- [Boson Broker](#boson-broker)
  - [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
      - [需要特别注意的参数](#需要特别注意的参数)
  - [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
  - [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

Boson Broker 作为boson的*KK*服务，承载boson的总的控制面流量。

## 准备工作

boson-broker 组件需要部署到 *Diamond KK* 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的数据库 `boson_service_v2` 已经创建。如未创建，请参考[PostgreSql DB 创建](../../deployment/deployment-guide.md#postgresql-db-%E5%88%9B%E5%BB%BA)
- Boson 服务对应的 RocketMQ topic 对接已经创建。如未创建，请参考[RocketMQ topic 初始化](../../deployment/deployment-guide.md#rocketmq-topic-%E5%88%9D%E5%A7%8B%E5%8C%96)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-broker
# or pull specific chart version
# helm3 pull boson/boson-broker --version x.y.z

tar -xzvf boson-broker-x.y.z.tgz

```

### 配置部署参数

不同环境的部署参数分别放在以下位置：



#### 需要特别注意的参数

## 服务部署

### 部署服务

```bash
helm3 install boson-broker boson-broker -n plat-boson-service -f path/to/env/boson-broker/values.yaml
```

### 更新服务

```bash
# 更新服务所用的crd
tar -xzvf boson-broker-x.y.z.tgz
kubectl apply -f boson-broker-x.y.z/crds/

# 更新服务的helm chart
helm3 upgrade boson-broker boson-broker -n plat-boson-service -f path/to/env/boson-broker/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-broker -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME             READY   UP-TO-DATE   AVAILABLE   AGE
# boson-broker   1/1     1            1           1m
```

### 查看运行的Pod


```bash
kubectl -n plat-boson-service get pod
# NAME                            READY   STATUS      RESTARTS   AGE
# boson-broker-64464584-h5scz   1/1     Running     0          1m

```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-broker-64464584-h5scz

```
