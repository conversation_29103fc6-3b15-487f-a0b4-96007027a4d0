# Boson NDC

<!-- TOC -->

- [Boson NDC](#boson-ndc)
  - [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
      - [需要特别注意的参数](#需要特别注意的参数)
  - [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
  - [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

Boson NDC 提供服务控制交换机、防火墙等。

## 准备工作

boson-ndc 组件目前需要部署到 Diamond KK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond KK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-kk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的数据库 `boson_service_v2` 已经创建。如未创建，请参考[PostgreSql DB 创建](../../deployment/deployment-guide.md#postgresql-db-%E5%88%9B%E5%BB%BA)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-ndc
# or pull specific chart version
# helm3 pull boson/boson-ndc --version x.y.z

tar -xzvf boson-ndc-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-ndc/values.yaml)
- [dev11](../../envs/dev11/boson-ndc/values.yaml)
- [dev44](../../envs/dev44/boson-ndc/values.yaml)
- [集成测试](../../envs/int-test/boson-ndc/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/sensecore-values-4-tech/KK/boson/boson-ndc/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-ndc/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/sensecore-multicluster-prod/KK/boson/boson-ndc/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

#### 需要特别注意的参数

- **global.pgsql.password**: 很多环境的密码是空的或者是加密的，因此部署前需要修改环境的数据库密码

## 服务部署

### 部署服务

```bash
helm3 install boson-ndc boson-ndc -n plat-boson-service -f path/to/env/boson-ndc/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-ndc boson-ndc -n plat-boson-service -f path/to/env/boson-ndc/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-ndc -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME                   READY   UP-TO-DATE   AVAILABLE   AGE
# boson-ndc   2/2     2            2           2m38s
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-service get pod
# NAME                                    READY   STATUS      RESTARTS   AGE
# boson-ndc-7d46b8b5d4-84ph5   1/1     Running     0          2m38s
# boson-ndc-7d46b8b5d4-32ae5   1/1     Running     0          2m38s
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-ndc-7d46b8b5d4-84ph5
```
