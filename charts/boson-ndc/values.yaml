
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # bosonNdcImage defines boson-ndc image and tag.
  bosonNdcImage: registry.sensetime.com/sensecore-boson/boson-ndc:v1.0.0-609b020-20240627142801
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # enableServiceMonitor 定义是否生成对接Diamond Prometheus 的服务的ServiceMonitor资源的yaml
  enableServiceMonitor: true
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-service.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName: network-service.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-infra-ndc
    values:
    - enabled
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Equal
    value: cn-sh-01a-control
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-dps
    operator: Equal
    value: enabled
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  # reginal的pgsql连接信息
  pgsql:
    enabled: true
    host: xxx
    port: 12345
    user: xxx
    db: boson_service_v2
    password: ""
  bosonNdc:
    # fullname defines name of boson-ndc and references every where.
    fullname: "boson-ndc"
    # replicas defines number of pods running in cluster.
    replicas: "2"
    cpuLimits: 400m
    memLimits: 1024Mi
    # 是否在 deployment 中增加当前时间的 annotation，以便在部署是可以更新deployment 出发 rolling update
    generate_deployment_annotations_timestamp: false
    # controller leader-elect, set false when replicas is 1
    leaderElect: "true"
    # envs defines environment related values.
    envs:
      # varName defines variable used for Boson Service
      varName: "boson-ndc"
    config:
      historyBackupcount: 0
      netconf:
        port: 830
        user: sensecore
        password: gAAAAABmVtcPIftgD3Fm5itkMqEpsA3LY8egonASH3J6LjH9KbV17rub_cl6zSKjcSSDsTINKi3dc2HiDad0uxsmgGHTByxQIwcrFUkCadu5pI6J7rhxenk=
      ssh:
        port: 22
        user: sensecore
        password: gAAAAABmVtcPIftgD3Fm5itkMqEpsA3LY8egonASH3J6LjH9KbV17rub_cl6zSKjcSSDsTINKi3dc2HiDad0uxsmgGHTByxQIwcrFUkCadu5pI6J7rhxenk=
    # service defines ports for service.
    service:
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "56080"
        # grpc port number for container, service, ingress.
        grpc: "56090"
        # metrics port number for container, service, service monitor.
        metrics: "56030"
