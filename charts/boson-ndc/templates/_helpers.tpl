{{/*
Expand the name of the chart.
*/}}
{{- define "bosonNdc.name" -}}
{{- default .Values.global.bosonNdc.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonNdc.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bosonNdc.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonNdc.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bosonNdc.labels" -}}
helm.sh/chart: {{ include "bosonNdc.chart" . }}
{{ include "bosonNdc.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonNdc.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}

{{/*
Service template for k8s.
*/}}
{{- define "bosonNdc.svc" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonNdc.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonNdc.fullname }}-service
    {{- include "bosonNdc.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.bosonNdc.fullname }}
  ports:
{{- end -}}

{{/*
Service monitor template for k8s.
*/}}
{{- define "bosonNdc.servicemonitor" -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.bosonNdc.fullname }}-servicemonitor
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "bosonNdc.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonNdc.fullname }}-service
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
{{- end }}
