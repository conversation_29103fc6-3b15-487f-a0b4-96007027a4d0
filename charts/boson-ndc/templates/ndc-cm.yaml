apiVersion: v1
data:
  ndc.yaml: |-
    ndc:
      http_port: {{ .Values.global.bosonNdc.service.ports.http }}
      grpc_port: {{ .Values.global.bosonNdc.service.ports.grpc }}
      metrics_port: {{ .Values.global.bosonNdc.service.ports.metrics }}
      key: PYCxYKLLDXC5BaaXyuU8bOT1d1PcStQ3sHwwHiKH9eY=
      token_key: gAAAAABk3HLDLyvZ-w3IffBfqD01l5CBLFowlLucx7RLOTAZNozmnRt11AEtyWKd0sGXJcFBKVzpIyFwd7IQIFZKMc65QVaWUA==
      debug: false
      log:
        ndc_name: ndc.log
        ndc_level: DEBUG
        ndc_backupcount: 0
        history_name: config_history.log
        history_backupcount: 0
      netconf:
        port: {{ .Values.global.bosonNdc.config.netconf.port }}
        user: {{ .Values.global.bosonNdc.config.netconf.user }}
        password: {{ .Values.global.bosonNdc.config.netconf.password }}
      ssh:
        port: {{ .Values.global.bosonNdc.config.ssh.port }}
        user: {{ .Values.global.bosonNdc.config.ssh.user }}
        password: {{ .Values.global.bosonNdc.config.ssh.password }}
      database:
        enabled: {{ .Values.global.pgsql.enabled }}
        password_text: text
        dbname: {{ .Values.global.pgsql.db }}
        host: {{ .Values.global.pgsql.host }}
        port: {{ .Values.global.pgsql.port }}
        username: {{ .Values.global.pgsql.user }}
        password: {{ .Values.global.pgsql.password }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonNdc.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonNdc.labels" . | nindent 4 }}
