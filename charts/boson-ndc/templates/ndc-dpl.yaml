apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonNdc.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonNdc.fullname }}
    {{- include "bosonNdc.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonNdc.fullname }}
  replicas: {{ int .Values.global.bosonNdc.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonNdc.fullname }}
{{- if .Values.global.bosonNdc.generate_deployment_annotations_timestamp }}
      annotations:
        timestamp: {{ now | date "2006-01-02T15:04:05" | quote }}
{{- end }}
    spec:
      serviceAccount: {{ .Values.global.bosonNdc.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonNdc.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations:
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        value: {{ .value | quote }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonNdc.fullname }}
        image: "{{ .Values.global.bosonNdcImage }}"
        command: [ "/usr/bin/python3", "main.py" ]
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: {{ int .Values.global.bosonNdc.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ int .Values.global.bosonNdc.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ int .Values.global.bosonNdc.service.ports.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonNdc.cpuLimits }}
            memory: {{ .Values.global.bosonNdc.memLimits }}
          requests:
            cpu: 100m
            memory: 200Mi
        env:
        - name: BOSON_NDC_VAR_NAME
          value: "{{ .Values.global.bosonNdc.envs.varName }}"
        - name: CONFIG
          value: "/root/{{ .Values.global.bosonNdc.fullname }}/conf/ndc.yaml"
        - name: ENVNAME
          value: {{ .Values.global.envName }}
        - name: BOSON_NDC_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NDC_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NDC_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NDC_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: {{ .Values.global.bosonNdc.fullname }}-config
          mountPath: /root/{{ .Values.global.bosonNdc.fullname }}/conf/ndc.yaml
          subPath: ndc.yaml
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ int .Values.global.bosonNdc.service.ports.http }}
          periodSeconds: 30
          initialDelaySeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: {{ int .Values.global.bosonNdc.service.ports.http }}
          periodSeconds: 10
          initialDelaySeconds: 10
      volumes:
      - name: {{ .Values.global.bosonNdc.fullname }}-config
        configMap:
          defaultMode: 420
          name: {{ .Values.global.bosonNdc.fullname }}-config
