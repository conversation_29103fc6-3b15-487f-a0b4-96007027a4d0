apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.fullname }}-service
    {{- include "bosonFrr.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.fullname }}-service
  ports:
  - name: http
    port: 8080
    protocol: TCP
  - name: grpc
    port: 8090
    protocol: TCP
