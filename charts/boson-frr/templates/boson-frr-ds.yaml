apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ .Values.global.fullname }}
  namespace: {{ .Values.global.namespace }}
spec:
  selector:
    matchLabels:
      app-name: {{ .Values.global.fullname }}
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.fullname }}
    spec:
      hostNetwork: true
      containers:
      - name: {{ .Values.global.fullname }}
        image: {{ .Values.global.serviceInfo.bosonFrrImage }}
        imagePullPolicy: IfNotPresent
        livenessProbe:
          exec:
            command:
            - cat
            - /etc/frr/frr.conf
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 179
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 10
        env:
        - name: K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          limits:
            cpu: {{ .Values.global.serviceInfo.cpuLimits }}
            memory: {{ .Values.global.serviceInfo.memLimits }}
          requests:
            cpu: 100m
            memory: 100Mi
        securityContext:
          capabilities:
            add:
            - NET_BIND_SERVICE
            - NET_ADMIN
            - NET_RAW
            - SYS_ADMIN
        volumeMounts:
        - name: frrconf
          mountPath: /mnt
          readOnly: true
      volumes:
      - name: frrconf
        configMap:
          name: {{ .Values.global.configmap.name }}
      nodeSelector:
        diamond.sensetime.com/role-infra-slb-platform-dedicated: enabled
      tolerations:
        - key: "diamond.sensetime.com/role-infra-slb-platform-dedicated"
          operator: "Equal"
          value: "enabled"
          effect: "NoExecute"
      dnsPolicy: ClusterFirst
{{- with .Values.global.serviceInfo.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      serviceAccount: default
      serviceAccountName: default
      priorityClassName: system-cluster-critical
