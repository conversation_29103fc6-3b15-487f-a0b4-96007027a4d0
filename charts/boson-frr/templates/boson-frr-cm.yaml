apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.global.configmap.name }}
  namespace: {{ .Values.global.namespace }}
data:
  {{- range .Values.instance }}
  {{ .host }}_frr-frr.conf: |-
    !
    ! Zebra configuration saved from vty
    !   2024/03/26 12:26:44
    !
    frr version 9.0.1_git
    frr defaults traditional
    !
    hostname {{ .frr_host_name }}
    !
    {{- range .nics }}
    interface {{ .nic }}.kni
     ip address {{ .local_kni_ip }}
    exit
    !
    {{- end }}
    interface lo
    {{- range .oss_vips }}
    {{- if eq (.vport|toString) "80" }}
     ip address {{ .vip }}/32
    {{- end}}
    {{- end }}
     ip address {{ .lvs_rs_hc_ip }}
    exit
    !
    router bgp {{ .frr_bgp_as }}
    bgp router-id {{ .frr_bgp_router_id }}
    no bgp ebgp-requires-policy
    bgp bestpath as-path multipath-relax
    {{- range .nics }}
    neighbor {{ .peer_ip }} remote-as {{ .peer_bgp_as }}
    neighbor {{ .peer_ip }} interface {{ .nic }}.kni
    neighbor {{ .peer_ip }} advertisement-interval 0
    {{- end }}
    !
    address-family ipv4 unicast
      {{- range .oss_vips }}
      {{- if eq (.vport|toString) "80" }}
      network {{ .vip }}/32
      {{- end}}
      {{- end }}
      network {{ .lvs_rs_hc_ip }}
      aggregate-address {{ .oss_vip_cidr }}
      aggregate-address {{ .lvs_local_ip }}
      {{- range .nics }}
      neighbor {{ .peer_ip }} soft-reconfiguration inbound
      neighbor {{ .peer_ip }} prefix-list {{ .route_policy_export }} out
      neighbor {{ .peer_ip }} prefix-list {{ .route_policy_import }} in
      {{- end }}
      maximum-paths 8
    exit-address-family
    !
    exit
    !
    ip prefix-list public_vip_valid_export seq 5 permit {{ .oss_vip_cidr }}
    ip prefix-list public_vip_valid_import seq 5 deny {{ .oss_vip_cidr }}
    ip prefix-list private_localip_valid_export seq 5 permit {{ .lvs_local_ip }}
    ip prefix-list private_localip_valid_import seq 5 deny {{ .lvs_local_ip }}
    !
    !
    !
    !
    !

  {{- end }}
