# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-infra
  # 注意node的selector和这里是对应的
  shortname: "boson-frr"
  fullname: "boson-frr"
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  serviceInfo:
    # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
    envName: devxx
    region: "cn-sh-01"
    az: "cn-sh-01a"
    prp: "cn-sh-01a-prp01"
    bosonFrrImage: registry.sensetime.com/sensecore-boson/quay.io/frrouting/frr:9.0.1_installed_tools_01
    # 从镜像站下载镜像的账号，需要提前创建docker pull secret
    imagePullSecret: sensecore-boson
    dryRun: False
    enablePprof: True
    independentNetns: false
    initialDelaySeconds: 60
    log_level: info
    replayDirInPod: /boson/replay-dir
    replayDirInHost: /var/lib/boson/boson-frr/replay-dir
    # used in rolling update
    maxUnavailable: 5
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 200m
    # memLimits defines memory limit used by pods.
    memLimits: 500Mi
    restartPolicy: Always
    serviceAccount: default
    serviceAccountName: default
    priorityClassName: system-cluster-critical
    # nodeSelectorTerms 定义服务部署所需要的node label标识
    nodeSelectorTerms:
    - key: diamond.sensetime.com/role-infra-slb-platform-dedicated
      values:
      - enabled
    tolerations:
    - effect: NoExecute
      key: "diamond.sensetime.com/role-infra-slb-platform-dedicated"
      operator: "Equal"
      value: "enabled"
