

<!-- TOC -->

    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 daemonset](#查看-daemonset)
    - [查看运行的Pod](#查看运行的pod)

<!-- /TOC -->

amd + roce 由之前的sriov方案替换为pf+vlan, 所以需要boson-roce-cni服务作为cni插件， 为训练容器提供设置vlan id等服务。



### 部署的前提条件

- Boson 服务在 namespace `plat-boson-infra` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-roce-cni
# or pull specific chart version
# helm3 pull boson/boson-roce-cni --version x.y.z

tar -xzvf boson-roce-cni_v1.0.0.tgz

```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev44](../../envs/dev44/boson-roce-cni/values.yaml)
- [集成测试](../../envs/int-test/boson-roce-cni/values.yaml)
    - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-roce-cni/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/prod/boson-roce-cni/values.yaml)
    - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-prod/TK/boson/boson-roce-cni/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。


## 服务部署

### 部署服务

```bash
helm3 install boson-roce-cni boson-roce-cni -n plat-boson-infra -f path/to/env/boson-roce-cni/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-roce-cni boson-roce-cni -n plat-boson-infra -f path/to/env/boson-roce-cni/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-roce-cni -n plat-boson-infra
```

## 检查服务状态

### 查看 daemonset

有多少台带有 Roce 网卡的机器，daemonset就应该有多少个pod。

```bash
kubectl -n plat-boson-infra get daemonset
AME                             DESIRED   CURRENT   READY   UP-TO-DATE   AVAILABLE   NODE SELECTOR             AGE
boson-roce-cni               4         4         4       4            4           <none>                    4m
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-infra get pod | grep boson-roce-cni
# NAME                            READY   STATUS      RESTARTS   AGE
boson-roce-cni-9wddv          1/1     Running            0   3d23h
boson-roce-cni-jm5hd          1/1     Running            0   3d23h
boson-roce-cni-qwjt2          1/1     Running            0   3d22h
boson-roce-cni-wnk8l          1/1     Running            0   3d22h
```
