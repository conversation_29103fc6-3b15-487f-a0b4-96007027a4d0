
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # product:
  product: BosonService
  fullname: "boson-roce-cni"
  name: boson-roce-cni
  # namespace defines the namespace for boson roce cni daemon set to deploy
  namespace: plat-boson-infra
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  # enable vf ip, for sensecore 2.0
  enableVFIP: false
  enableCrossNic: true
  # nodeSelectorTerms defines node labels for all nodes that contain rdma device
  nodeSelectorTerms:
  - matchExpressions:
    - key: diamond.sensetime.com/nic-access-mode
      values:
      - PF
    - key: diamond.sensetime.com/nic-training-protocol
      values:
      - RoCE
  # tolerations defines supported taints in nodes
  tolerations:
    - effect: NoExecute
      key: diamond.sensetime.com/belong-resource-prp
      operator: Exists
    - effect: NoExecute
      key: diamond.sensetime.com/role-business-acp
      operator: Equal
      value: enabled
  # imagePullSecret: 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # bosonRoceCniImage defines boson-roce-cni image and tag.
  bosonRoceCniImage: registry.sensetime.com/sensecore-boson/boson-roce-cni:v1.1.3
  imagePullPolicy: Always
  # cpuLimits defines cpu limit used by pods.
  cpuLimits: 1
  # memLimits defines memory limit used by pods.
  memLimits: 1Gi
