{{/*
Expand the name of the chart.
*/}}
{{- define "bosonRoceCni.name" -}}
{{- default .Values.global.name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonRoceCni.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bosonRoceCni.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonRoceCni.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bosonRoceCni.labels" -}}
helm.sh/chart: {{ include "bosonRoceCni.chart" . }}
{{ include "bosonRoceCni.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.name }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}
