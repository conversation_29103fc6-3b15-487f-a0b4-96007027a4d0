apiVersion: v1
kind: ConfigMap
metadata:
  name: ecn-optimize-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonRoceCni.labels" . | nindent 4 }}
data:
  config.json: |
    {
        "configList": [
            {
            "series": "ROCE-400G",
                "match_bash": "lspci -nn | grep 15b3:1021",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 100,
                "rpg_byte_reset": 16384,
                "rpg_min_dec_fac": 1,
                "rpg_time_reset": 50
            },
            {
                "series": "V100+RoCE+MSN4600+100G",
                "match_bash": "lspci -nn |grep 10de:1df2",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 5,
                "rpg_byte_reset": 32767,
                "rpg_min_dec_fac": 100,
                "rpg_time_reset": 50
            },
            {
                "series": "dcu",
                "match_bash": "lspci | grep Z100",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 5,
                "rpg_byte_reset": 32767,
                "rpg_min_dec_fac": 100,
                "rpg_time_reset": 50
            },
            {
                "series": "default",
                "match_bash": "echo default",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 100,
                "rpg_byte_reset": 16384,
                "rpg_min_dec_fac": 1,
                "rpg_time_reset": 50
            }
        ]
    }
