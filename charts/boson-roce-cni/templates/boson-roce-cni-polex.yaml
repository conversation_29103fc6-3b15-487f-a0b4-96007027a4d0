apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.fullname }}-host-namespaces
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.fullname }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.fullname }}-host-path
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.fullname }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.fullname }}-privileged-containers
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.fullname }}-*
        namespaces:
        - {{ .Values.global.namespace }}
