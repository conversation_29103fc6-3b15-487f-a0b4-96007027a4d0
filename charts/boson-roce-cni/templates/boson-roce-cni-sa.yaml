apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.name }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.name }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.name }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.name }}
  namespace: {{ .Values.global.namespace }}
