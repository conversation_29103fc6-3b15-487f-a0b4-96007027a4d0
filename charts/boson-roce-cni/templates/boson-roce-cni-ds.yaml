apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    kubernetes.io/description: |
      This daemon set launches the boson-roce-cni daemon.
  name: {{ .Values.global.name }}
  namespace: {{ .Values.global.namespace }}
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: {{ .Values.global.name }}
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: {{ .Values.global.name }}
        component: network
        type: infra
    spec:
      serviceAccount: {{ .Values.global.name }}
      affinity:
      {{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range $index, $nodeSelectorTerms := . }}
            - matchExpressions:
            {{- if $nodeSelectorTerms.matchExpressions }}
              {{- range $expressions := $nodeSelectorTerms.matchExpressions }}
              - key: {{ $expressions.key | quote }}
                operator: In
                values:
                {{- range $expressions.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- else }}
              - key: {{ $nodeSelectorTerms.key | quote }}
                operator: In
                values:
                {{- range $nodeSelectorTerms.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- end }}
      {{- end }}
      containers:
        - args:
            - --debug
          command:
            - bash
            - /opt/boson-roce-cni/start-daemon.sh
            {{- if .Values.global.enableVFIP }}
            - --enable-vf-ip=true
            {{- end }}
            {{- if .Values.global.enableCrossNic }}
            - --enable-cross-nic=true
            {{- end }}
          env:
            - name: DBUS_SYSTEM_BUS_ADDRESS
              value: unix:path=/host/var/run/dbus/system_bus_socket
          image: {{ .Values.global.bosonRoceCniImage }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy }}
          name: cni-server
          livenessProbe:
            exec:
              command:
              - /usr/bin/roce-ctl
              - health_check
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          readinessProbe:
            exec:
              command:
              - /usr/bin/roce-ctl
              - health_check
            failureThreshold: 3
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: {{ .Values.global.cpuLimits | quote }}
              memory: {{ .Values.global.memLimits }}
            requests:
              cpu: 200m
              memory: 200Mi
          securityContext:
            privileged: true
            runAsUser: 0
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /lib/modules
              name: host-modules
              readOnly: true
            - mountPath: /etc/host-network-scripts
              name: host-network-scripts
              readOnly: true
            - mountPath: /etc/roce-cni
              name: host-etc
            - mountPath: /run/roce-cni
              name: host-run
            - mountPath: /var/run/netns
              mountPropagation: HostToContainer
              name: host-ns
            - mountPath: /var/log/roce-cni
              name: host-log
            - mountPath: /host/var/run/dbus
              name: host-dbus
            - mountPath: /etc/localtime
              name: localtime
            - name: ecn-optimize-config-volume
              mountPath: /etc/ecn-config
      dnsPolicy: ClusterFirst
      hostNetwork: true
      hostPID: true
      imagePullSecrets:
        - name: {{ .Values.global.imagePullSecret }}
      initContainers:
        - command:
            - bash
            - /opt/boson-roce-cni/install-cni.sh
          image: {{ .Values.global.bosonRoceCniImage }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy }}
          name: install-cni
          resources: {}
          securityContext:
            privileged: true
            runAsUser: 0
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /opt/cni/bin
              name: cni-bin
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
      volumes:
        - hostPath:
            path: /opt/cni/bin
            type: ""
          name: cni-bin
        - hostPath:
            path: /lib/modules
            type: ""
          name: host-modules
        - hostPath:
            path: /etc/sysconfig/network-scripts
            type: ""
          name: host-network-scripts
        - hostPath:
            path: /etc/roce-cni
            type: ""
          name: host-etc
        - hostPath:
            path: /run/roce-cni
            type: ""
          name: host-run
        - hostPath:
            path: /var/run/netns
            type: ""
          name: host-ns
        - hostPath:
            path: /var/log/roce-cni
            type: ""
          name: host-log
        - hostPath:
            path: /var/run/dbus
            type: ""
          name: host-dbus
        - hostPath:
            path: /etc/localtime
            type: ""
          name: localtime
        - name: ecn-optimize-config-volume
          configMap:
            name: ecn-optimize-config
            items:
              - key: config.json
                path: ecn-optimize-config
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
