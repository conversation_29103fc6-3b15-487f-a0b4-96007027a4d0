apiVersion: v1
data:
  boson-provider.yaml: |-
    env: {{ .Values.global.envName }}
    pg:
      host: {{ .Values.global.pgsql.host }}
      port: {{ .Values.global.pgsql.port | quote }}
      user: {{ .Values.global.pgsql.user }}
      password: {{ .Values.global.pgsql.password }}
      db: {{ .Values.global.pgsql.db }}

    # Provider的初始化地址池参数
    {{- with .Values.global.bosonProvider.init_pool }}
    init_pool:
      nat_gateways:
        cidr: {{ .nat_gateways.cidr }}
        gateway: {{ .nat_gateways.gateway }}
        ips:
        {{- range .nat_gateways.ips }}
        - {{ . | quote }}
        {{- end }}
      slbs:
        cidr: {{ .slbs.cidr }}
        gateway: {{ .slbs.gateway }}
        ips:
        {{- range .slbs.ips }}
        - {{ . | quote }}
        {{- end }}
      eips:
        cidr: {{ .eips.cidr }}
        gateway: {{ .eips.gateway }}
        ips:
        {{- range .eips.ips }}
        - {{ . | quote }}
        {{- end }}
        {{- if .eips.sku }}
        sku: {{ .eips.sku }}
        {{- end }}
      cidr_pools:
        ib:
        {{- range .cidr_pools.ib }}
        - cidr: {{ .cidr }}
          gateway: {{ .gateway }}
          zone_prp: {{ .zone_prp }}
          vni: {{ .vni }}
          scope: {{ .scope }}
          reserved: {{ .reserved }}
        {{- end }}
        vlan:
        {{- range .cidr_pools.vlan }}
        - cidr: {{ .cidr }}
          gateway: {{ .gateway }}
          zone_prp: {{ .zone_prp }}
          vni: {{ .vni }}
          scope: {{ .scope }}
          reserved: {{ .reserved }}
        {{- end }}
    {{- end }}

    # Provider的默认参数
    {{- with .Values.global.bosonProvider.defaults }}
    boson_default:
      vpc_default_az: {{ .zone_name_for_az_resource }}
      vpc_default_region: {{ .zone_name_for_region_resource }}
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
    {{- end }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-job-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonProvider.labels" . | nindent 4 }}
