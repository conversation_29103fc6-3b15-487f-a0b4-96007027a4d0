{{/*
Expand the name of the chart.
*/}}
{{- define "bosonProvider.name" -}}
{{- default .Values.global.bosonProvider.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonProvider.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bosonProvider.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonProvider.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bosonProvider.labels" -}}
helm.sh/chart: {{ include "bosonProvider.chart" . }}
{{ include "bosonProvider.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonProvider.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}
