apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-job
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonProvider.fullname }}-job
    {{- include "bosonProvider.labels" . | nindent 4 }}
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
{{- if .Values.global.bosonProvider.generate_deployment_annotations_timestamp }}
      annotations:
        timestamp: {{ now | date "2006-01-02T15:04:05" | quote }}
{{- end }}
    spec:
      serviceAccount: {{ .Values.global.bosonProvider.fullname }}
      volumes:
      - configMap:
          name: {{ .Values.global.bosonProvider.fullname }}-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - {{ .Values.global.bosonProvider.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Never
      containers:
      - name: {{ .Values.global.bosonProvider.fullname }}
        image: "{{ .Values.global.bosonInitPoolImage }}"
        command:
        - sh
        - -c
        - "{{ .Values.global.bosonProvider.enablePoolCmd }}"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
