
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  #product: Boson 服务产品名
  product: BosonService
  # bosonInitPoolImage defines init boson service job image and tag.
  bosonInitPoolImage: registry.sensetime.com/sensecore-boson/boson-toolbox:v1.7.0-7-g08b557d-20231026114413
  # hook image, use boson-assistant
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:v1.8.0-1-g2b3dabe-20231116213725
  # imagePullSecret: 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  # reginal的pgsql连接信息
  pgsql:
    host: xxx
    port: 12345
    user: xxx
    db: boson_service_v2
    password: ""

  # bosonProvider variables
  bosonProvider:
    # fullname defines name of boson-provider and references every where.
    fullname: "boson-init-pool"
    # 是否在 deployment 中增加当前时间的 annotation，以便在部署是可以更新deployment 出发 rolling update
    generate_deployment_annotations_timestamp: false
    enablePoolCmd: "./syncTables && ./initPools"
    init_pool:
      nat_gateways:
        cidr: 0.0.0.0/24
        gateway: *******
        ips: []
      slbs:
        cidr: 0.0.0.0/24
        gateway: *******
        ips: []
      eips:
        cidr: 0.0.0.0/24
        gateway: *******
        ips: []
      cidr_pools:
        ib: []
        vlan: []
    defaults:
      zone_name_for_az_resource: "cn-sh-01a"
      zone_name_for_region_resource: "cn-sh-01z"
