apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.bosonNetController.fullname }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.bosonNetController.fullname }}
  namespace: {{ .Values.global.namespace }}
