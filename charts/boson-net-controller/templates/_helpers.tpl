{{/*
Expand the name of the chart.
*/}}
{{- define "bosonNetController.name" -}}
{{- default .Values.global.bosonNetController.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonNetController.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bosonNetController.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonNetController.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bosonNetController.labels" -}}
helm.sh/chart: {{ include "bosonNetController.chart" . }}
{{ include "bosonNetController.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonNetController.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}

{{/*
Service template for k8s.
*/}}
{{- define "bosonNetController.svc" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonNetController.fullname }}-service
    {{- include "bosonNetController.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  clusterIP: {{ .Values.global.bosonNetController.service.clusterIP }}
  selector:
    app-name: {{ .Values.global.bosonNetController.fullname }}
  ports:
{{- end -}}

{{/*
Service monitor template for k8s.
*/}}
{{- define "bosonNetController.servicemonitor" -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}-servicemonitor
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "bosonNetController.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonNetController.fullname }}-service
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
{{- end }}
