apiVersion: v1
data:
  config.yaml: |-
    boson_default:
      vpc_default_az: {{ .Values.global.boson_default.zone_name_for_az_resource }}
      vpc_default_region: {{ .Values.global.boson_default.zone_name_for_region_resource }}

    {{- with .Values.global.bosonNetController.config.disableFeatures }}
    # Disable features: dnat, eip, ipa, shadownode, snat, subnet, vpcnatgateway, bms-lazy-attach
    disableFeatures:
    {{- range . }}
      - {{.}}
    {{- end }}
    {{- end }}
    pg:
      host: {{ .Values.global.pgsql.host }}
      port: {{ .Values.global.pgsql.port | quote }}
      user: {{ .Values.global.pgsql.user }}
      password: {{ .Values.global.pgsql.password }}
      db: {{ .Values.global.pgsql.db }}
    ibManager:
      enable: {{.Values.global.bosonNetController.config.ibManagerEnable}}
    netDevice:
      enable: {{.Values.global.bosonNetController.config.netDeviceEnable}}
      serverURL: {{.Values.global.bosonNetController.config.netDeviceServerURL}}
      accessKey: {{.Values.global.bosonNetController.config.netDeviceAccessKey}}

    dataNetworkSwitch: {{.Values.global.bosonNetController.config.dataNetworkSwitch}}

    bot:
      url: {{ .Values.global.wxURL }}

    fw:
      globalSnatRuleName: {{.Values.global.bosonNetController.config.globalSnatRuleName}}
      globalSnatQosPolicyName: {{.Values.global.bosonNetController.config.globalSnatQosPolicyName}}
      globalDnatQosPolicyName: {{.Values.global.bosonNetController.config.globalDsnatQosPolicyName}}
      sourceZone: {{.Values.global.bosonNetController.config.sourceZone}}
      destinationZone: {{.Values.global.bosonNetController.config.destinationZone}}
      defaultSecurityPolicyRuleName: {{.Values.global.bosonNetController.config.defaultSecurityPolicyRuleName}}
      defaultInnerSecurityPolicyRuleName: {{.Values.global.bosonNetController.config.defaultInnerSecurityPolicyRuleName}}
      minNatAddressGroupID: {{.Values.global.bosonNetController.config.minNatAddressGroupID}}
      maxNatAddressGroupID: {{.Values.global.bosonNetController.config.maxNatAddressGroupID}}
    gc:
      gcIntervalSec: {{ int .Values.global.bosonNetController.config.gcIntervalSec }}
      GCPauseSec: {{ int .Values.global.bosonNetController.config.gcPauseSec }}
      enable: {{ .Values.global.bosonNetController.config.gcEnable }}
    slb:
      enable: {{ .Values.global.bosonNetController.config.slb.enable }}
      deployment:
        image: {{ .Values.global.bosonNetController.config.slb.deployment.image }}
        {{- with .Values.global.bosonNetController.config.slb.deployment.cmd }}
        cmd:
          {{- range . }}
          - {{ . }}
          {{- end }}
        {{- end }}
        {{- with .Values.global.bosonNetController.config.slb.deployment.args }}
        args:
          {{- range . }}
          - {{ . }}
          {{- end }}
        {{- end }}
        privileged: {{ .Values.global.bosonNetController.config.slb.deployment.privileged }}
        allowPrivilegeEscalation: {{ .Values.global.bosonNetController.config.slb.deployment.allowPrivilegeEscalation }}
        tolerationsConfig: |-
          {{- .Values.global.bosonNetController.config.slb.deployment.tolerationsConfig | nindent 10 }}
        affinityConfig: |-
          {{- .Values.global.bosonNetController.config.slb.deployment.affinityConfig | nindent 10 }}
        readinessProbeConfig: |-
          {{- .Values.global.bosonNetController.config.slb.deployment.readinessProbeConfig | nindent 10 }}
        termTime: {{ .Values.global.bosonNetController.config.slb.deployment.termTime }}
        basicNetworkNamespace: {{ .Values.global.bosonNetController.config.slb.deployment.basicNetworkNamespace }}
        basicNetworkNAD: {{ .Values.global.bosonNetController.config.slb.deployment.basicNetworkNAD }}
        replica: {{ int .Values.global.bosonNetController.config.slb.deployment.replica }}
        resource:
          limits:
            cpu: {{ .Values.global.bosonNetController.config.slb.deployment.resource.limits.cpu }}
            memory: {{ .Values.global.bosonNetController.config.slb.deployment.resource.limits.memory }}
          requests:
            cpu: {{ .Values.global.bosonNetController.config.slb.deployment.resource.requests.cpu }}
            memory: {{ .Values.global.bosonNetController.config.slb.deployment.resource.requests.memory }}
        agent:
          report_server_address: {{ .Values.global.bosonNetController.config.slb.xDSControllerServiceHost }}
          report_server_port: {{ int .Values.global.bosonNetController.service.ports.slbGrpc }}
          # grpc, http, mertic 和 envoy 启动在同一个pod的同一个ip地址上，需要和 envoy 的端口做好规划
          # envoy 的端口：envoyAdminPort
          admin_http_port: {{ int .Values.global.bosonNetController.config.slb.deployment.agent.admin_http_port }}
          admin_grpc_port: {{ int .Values.global.bosonNetController.config.slb.deployment.agent.admin_grpc_port }}
          admin_metrics_port: {{ int .Values.global.bosonNetController.config.slb.deployment.agent.admin_metrics_port }}
          image: {{ .Values.global.bosonNetController.config.slb.deployment.agent.image }}
          {{- with .Values.global.bosonNetController.config.slb.deployment.agent.cmd }}
          cmd:
            {{- range . }}
            - {{ . }}
            {{- end }}
          {{- end }}
          {{- with .Values.global.bosonNetController.config.slb.deployment.agent.args }}
          args:
            {{- range . }}
            - {{ . }}
            {{- end }}
          {{- end }}
          readinessProbeConfig: |-
            {{- .Values.global.bosonNetController.config.slb.deployment.agent.readinessProbeConfig | nindent 12 }}
      bgpEnable: {{ .Values.global.bosonNetController.config.slb.bgpEnable }}
      agentEnable: {{ .Values.global.bosonNetController.config.slb.agentEnable }}
      namespace: {{ .Values.global.bosonNetController.config.slb.namespace }}
      xDSControllerServiceHost: {{ .Values.global.bosonNetController.config.slb.xDSControllerServiceHost }}
      xDSControllerServicePort: {{ int .Values.global.bosonNetController.service.ports.xds }}
      xDSServerGrpcKeepaliveTime: {{ int .Values.global.bosonNetController.config.slb.xDSServerGrpcKeepaliveTime }}
      xDSServerGrpcKeepaliveTimeout: {{ int .Values.global.bosonNetController.config.slb.xDSServerGrpcKeepaliveTimeout }}
      xDSServerGrpcKeepaliveMinTime: {{ int .Values.global.bosonNetController.config.slb.xDSServerGrpcKeepaliveMinTime }}
      xDSServerGrpcMaxConcurrentStreams: {{ int .Values.global.bosonNetController.config.slb.xDSServerGrpcMaxConcurrentStreams }}
      envoyAdminPort: {{ int .Values.global.bosonNetController.config.slb.envoyAdminPort }}
      envoyConfigTemplatePath: {{ .Values.global.bosonNetController.config.slb.envoyConfigTemplatePath }}
      grpc_port: {{ int .Values.global.bosonNetController.service.ports.slbGrpc }}
      http_port: {{ int .Values.global.bosonNetController.service.ports.slbHttp }}
      {{- with .Values.global.bosonNetController.config.slb.upgrade }}
      upgrade:
        {{- range . }}
        - {{ . }}
        {{- end }}
      {{- end }}
      imagePullSecrets:
      - name: {{ toYaml .Values.global.imagePullSecret }}
    pprof:
      pprofEnable: {{ .Values.global.bosonNetController.config.pprof.pprofEnable }}
      pprofPort: {{ int .Values.global.bosonNetController.service.ports.pprof }}
      metricsPath: {{ .Values.global.bosonNetController.config.pprof.metricsPath }}
      pprofPath: {{ .Values.global.bosonNetController.config.pprof.pprofPath }}

    # Net device config templates
    # define the firewall and switch config
    netDeviceConfig:
      fw__default__set_eip: |-
        # CR: eip/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # TenantID: {{`{{.TenantID}}`}}
        # SrSourceIP: {{`{{.SrSourceIP}}`}}
        # AssociationType: {{`{{.AssociationType}}`}}
        # SubnetNetAddr: {{`{{.SubnetNetAddr}}`}}
        # SubnetNetMask: {{`{{.SubnetNetMask}}`}}
        # UpStreamMaxBandwidth: {{`{{.UpStreamEIPMaxBandwidth}}`}}
        # DownStreamMaxBandwidth: {{`{{.DownStreamEIPMaxBandwidth}}`}}
        # MaxConns: {{`{{.EIPMaxConns}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        nat address-group {{`{{.NatAgID}}`}} name bsn_eip_ag_{{`{{.EIP}}`}}
            address {{`{{.EIP}}`}} {{`{{.EIP}}`}}

        object-group ip address bsn_snat_og_{{`{{.EIP}}`}}
            security-zone {{`{{.SourceZone}}`}}
        {{`{{- if or (eq .AssociationType "NATGW") (eq .AssociationType "NATGW_AND_BM") }}`}}
            0 network subnet {{`{{.SrSourceIP}}`}} 32
        {{`{{- end}}`}}
        {{`{{- if or (eq .AssociationType "BM") (eq .AssociationType "NATGW_AND_BM") }}`}}
            1 network subnet {{`{{.SubnetNetAddr}}`}} {{`{{.SubnetNetMask}}`}}
        {{`{{- end}}`}}

        nat global-policy
        rule name bsn_eip_sr_{{`{{.EIP}}`}}
            source-ip bsn_snat_og_{{`{{.EIP}}`}}
            source-zone {{`{{.SourceZone}}`}}
            destination-zone {{`{{.DestinationZone}}`}}
            action snat address-group name bsn_eip_ag_{{`{{.EIP}}`}}
            counting enable

        {{`{{- if eq .DefaultSnatRule "enabled" }}`}}
        rule move bsn_eip_sr_{{`{{.EIP}}`}} before {{`{{.GlobalSnatRuleName}}`}}
        {{`{{- else}}`}}
        rule move bsn_eip_sr_{{`{{.EIP}}`}} after {{`{{.GlobalSnatRuleName}}`}}
        {{`{{- end}}`}}

        {{`{{- if or (and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "")) (and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "")) (and (ne .EIPMaxConns "0") (ne .EIPMaxConns "")) }}`}}

        object-group ip address bsn_sqos_og_{{`{{.EIP}}`}}
        {{`{{- if or (eq .AssociationType "NATGW") (eq .AssociationType "NATGW_AND_BM") }}`}}
          0 network host address {{`{{.SrSourceIP}}`}}
        {{`{{- end}}`}}
        {{`{{- if or (eq .AssociationType "BM") (eq .AssociationType "NATGW_AND_BM") }}`}}
          1 network subnet {{`{{.SubnetNetAddr}}`}} {{`{{.SubnetNetMask}}`}}
        {{`{{- end}}`}}

        object-group ip address bsn_dqos_og_{{`{{.EIP}}`}}
          0 network host address {{`{{.EIP}}`}}

        traffic-policy
        profile name bsn_nattp_pf_{{`{{.EIP}}`}}
        {{`{{- if and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "") }}`}}
            bandwidth upstream maximum {{`{{.UpStreamEIPMaxBandwidth}}`}}
        {{`{{- end}}`}}
        {{`{{- if and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "") }}`}}
            bandwidth downstream maximum {{`{{.DownStreamEIPMaxBandwidth}}`}}
        {{`{{- end}}`}}
        {{`{{- if and (ne .EIPMaxConns "0") (ne .EIPMaxConns "") }}`}}
            connection-limit count per-rule {{`{{.EIPMaxConns}}`}}
        {{`{{- end}}`}}

        rule name bsn_sqos_rule_{{`{{.EIP}}`}}
            source-zone {{`{{.SourceZone}}`}}
            destination-zone {{`{{.DestinationZone}}`}}
            source-address address-set bsn_sqos_og_{{`{{.EIP}}`}}
            action qos profile bsn_nattp_pf_{{`{{.EIP}}`}}

        rule name bsn_dqos_rule_{{`{{.EIP}}`}}
            source-zone {{`{{.DestinationZone}}`}}
            destination-zone {{`{{.SourceZone}}`}}
            destination-address address-set bsn_dqos_og_{{`{{.EIP}}`}}
            action qos profile bsn_nattp_pf_{{`{{.EIP}}`}}

        {{`{{- if eq .DefaultSnatRule "enabled" }}`}}
        rule move bsn_sqos_rule_{{`{{.EIP}}`}} before {{`{{.GlobalSnatQosPolicyName}}`}}
        rule move bsn_dqos_rule_{{`{{.EIP}}`}} before {{`{{.GlobalDnatQosPolicyName}}`}}
        {{`{{- else}}`}}
        rule move bsn_sqos_rule_{{`{{.EIP}}`}} after {{`{{.GlobalSnatQosPolicyName}}`}}
        rule move bsn_dqos_rule_{{`{{.EIP}}`}} after {{`{{.GlobalDnatQosPolicyName}}`}}
        {{`{{- end}}`}}

        {{`{{- end}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type eip

      fw__default__unset_eip: |-
        # CR: eip/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # TenantID: {{`{{.TenantID}}`}}
        # SrSourceIP: {{`{{.SrSourceIP}}`}}
        # AssociationType: {{`{{.AssociationType}}`}}
        # SubnetNetAddr: {{`{{.SubnetNetAddr}}`}}
        # SubnetNetMask: {{`{{.SubnetNetMask}}`}}
        # UpStreamMaxBandwidth: {{`{{.UpStreamEIPMaxBandwidth}}`}}
        # DownStreamMaxBandwidth: {{`{{.DownStreamEIPMaxBandwidth}}`}}
        # MaxConns: {{`{{.EIPMaxConns}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        nat global-policy
        undo rule name bsn_eip_sr_{{`{{.EIP}}`}}
        undo nat address-group {{`{{.NatAgID}}`}}
        undo object-group ip address bsn_snat_og_{{`{{.EIP}}`}}

        {{`{{- if or (and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "")) (and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "")) (and (ne .EIPMaxConns "0") (ne .EIPMaxConns "")) }}`}}
        traffic-policy
        undo rule name bsn_sqos_rule_{{`{{.EIP}}`}}
        undo rule name bsn_dqos_rule_{{`{{.EIP}}`}}
        undo profile name bsn_nattp_pf_{{`{{.EIP}}`}}
        undo object-group ip address bsn_dqos_og_{{`{{.EIP}}`}}
        undo object-group ip address bsn_sqos_og_{{`{{.EIP}}`}}
        {{`{{- end}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status DELETED -type eip

      fw__default__expire_stop_eip: |-
        # CR: eip/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # TenantID: {{`{{.TenantID}}`}}
        # SrSourceIP: {{`{{.SrSourceIP}}`}}
        # AssociationType: {{`{{.AssociationType}}`}}
        # UpStreamMaxBandwidth: {{`{{.UpStreamEIPMaxBandwidth}}`}}
        # DownStreamMaxBandwidth: {{`{{.DownStreamEIPMaxBandwidth}}`}}
        # MaxConns: {{`{{.EIPMaxConns}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        expire stop eip {{`{{.EIP}}`}}

        # ./boson-tool -name {{`{{.CR}}`}} --status EXPIRESTOPPED -type eip

      fw__default__renew_start_eip: |-
        # CR: eip/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # TenantID: {{`{{.TenantID}}`}}
        # SrSourceIP: {{`{{.SrSourceIP}}`}}
        # AssociationType: {{`{{.AssociationType}}`}}
        # UpStreamMaxBandwidth: {{`{{.UpStreamEIPMaxBandwidth}}`}}
        # DownStreamMaxBandwidth: {{`{{.DownStreamEIPMaxBandwidth}}`}}
        # MaxConns: {{`{{.EIPMaxConns}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        renew start eip {{`{{.EIP}}`}}

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type eip

      fw__default__set_dnat: |-
        # CR: dnat/{{`{{.CR}}`}}
        # Protocol: {{`{{.Protocol}}`}}
        # Port: {{`{{.DnatPort}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
            0 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
            1 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- end}}`}}

        nat global-policy
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          description boson_dnat_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone {{`{{.DestinationZone}}`}}
          destination-ip host {{`{{.EIP}}`}}
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.InternalPort}}`}}
          counting enable

        rule name bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone Trust
          destination-ip host {{`{{.EIP}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.InternalPort}}`}}
          counting enable

        rule move bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before  {{`{{.DefaultInnerSecurityPolicyRuleName}}`}}

        security-policy ip
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action pass
          counting enable
          source-zone {{`{{.DestinationZone}}`}}
          destination-zone {{`{{.SourceZone}}`}}
          destination-ip-host {{`{{.DnatIP}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.DnatPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.DnatPort}}`}}
        {{`{{- else }}`}}
          service-port tcp destination eq {{`{{.DnatPort}}`}}
          service-port udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- end}}`}}

        move rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before name {{`{{.DefaultSecurityPolicyRuleName}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type dnat

      fw__default__unset_dnat: |-
        # CR: dnat/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # Port: {{`{{.DnatPort}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        undo rule name bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        undo object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        security-policy ip
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status DELETED -type dnat

      fw__default__bm_set_dnat: |-
        # CR: dnat/{{`{{.CR}}`}}
        # Protocol: {{`{{.Protocol}}`}}
        # Port: {{`{{.DnatPort}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        undo object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        security-policy ip
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
            0 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
            1 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- end}}`}}

        nat global-policy
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          description boson_dnat_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone {{`{{.DestinationZone}}`}}
          destination-ip host {{`{{.EIP}}`}}
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.InternalPort}}`}}
          counting enable

        rule name bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone Trust
          destination-ip host {{`{{.EIP}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.InternalPort}}`}}
          counting enable

        rule move bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before  {{`{{.DefaultInnerSecurityPolicyRuleName}}`}}

        security-policy ip
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action pass
          counting enable
          source-zone {{`{{.DestinationZone}}`}}
          destination-zone {{`{{.SourceZone}}`}}
          destination-ip-host {{`{{.DnatIP}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.InternalPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.InternalPort}}`}}
        {{`{{- else }}`}}
          service-port tcp destination eq {{`{{.InternalPort}}`}}
          service-port udp destination eq {{`{{.InternalPort}}`}}
        {{`{{- end}}`}}

        move rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before name {{`{{.DefaultSecurityPolicyRuleName}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type dnat

      fw__default__bm_unset_dnat: |-
        # CR: dnat/{{`{{.CR}}`}}
        # EIP: {{`{{.EIP}}`}}
        # Port: {{`{{.DnatPort}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        undo object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        security-policy ip
        undo rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}

        object-group service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
            0 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- else }}`}}
            0 service tcp destination eq {{`{{.DnatPort}}`}}
            1 service udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- end}}`}}

        nat global-policy
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          description boson_dnat_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone {{`{{.DestinationZone}}`}}
          destination-ip host {{`{{.EIP}}`}}
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.DnatPort}}`}}
          counting enable

        rule name bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          source-zone Trust
          destination-ip host {{`{{.EIP}}`}}
          service bsn_og_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{`{{.DnatIP}}`}} local-port {{`{{.DnatPort}}`}}
          counting enable

        rule move bsn_idr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before  {{`{{.DefaultInnerSecurityPolicyRuleName}}`}}

        security-policy ip
        rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}}
          action pass
          counting enable
          source-zone {{`{{.DestinationZone}}`}}
          destination-zone {{`{{.SourceZone}}`}}
          destination-ip-host {{`{{.DnatIP}}`}}
        {{`{{- if eq .Protocol "t" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.DnatPort}}`}}
        {{`{{- else if eq .Protocol "u" }}`}}
          service-port {{`{{.Protocol}}`}} destination eq {{`{{.DnatPort}}`}}
        {{`{{- else }}`}}
          service-port tcp destination eq {{`{{.DnatPort}}`}}
          service-port udp destination eq {{`{{.DnatPort}}`}}
        {{`{{- end}}`}}

        move rule name bsn_dr_{{`{{.Protocol}}`}}_{{`{{.EIP}}`}}_{{`{{.DnatPort}}`}} before name {{`{{.DefaultSecurityPolicyRuleName}}`}}

        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type dnat

      tor__default__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VlanID: {{`{{.VNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}
        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        disp current-configuration interface {{`{{.NetPortName}}`}}
        vlan {{`{{.VNI}}`}}
        description {{`{{.SubnetName}}`}}
        interface {{`{{.NetPortName}}`}}
        port link-type access
        port access vlan {{`{{.VNI}}`}}
        save force

        {{`{{- if eq .Operate "BIND" }}`}}
        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa
        {{`{{- end}}`}}
        {{`{{- if eq .Operate "UNBIND" }}`}}
        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa
        {{`{{- end}}`}}

      roce_leaf__msn3420__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        net show interface {{`{{.NetPortName}}`}} json
        {{`{{- if HasPrefix .NetPortName "bond" }}`}}
        net add bond {{`{{.NetPortName}}`}} bridge access {{`{{.VNI}}`}}
        {{`{{- else}}`}}
        net add interface {{`{{.NetPortName}}`}} bridge access {{`{{.VNI}}`}}
        {{`{{- end}}`}}
        net commit

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa

      roce_leaf__msn3420__del_netport_from_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        net show interface {{`{{.NetPortName}}`}} json
        {{`{{- if HasPrefix .NetPortName "bond" }}`}}
        net del bond {{`{{.NetPortName}}`}} bridge access {{`{{.VNI}}`}}
        {{`{{- else}}`}}
        net del interface {{`{{.NetPortName}}`}} bridge access {{`{{.VNI}}`}}
        {{`{{- end}}`}}
        net commit

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa

      roce_leaf__s6850__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}
          port link-type access
          service-instance {{`{{.DefaultVNI }}`}}
          undo encapsulation
          service-instance {{`{{.VNI}}`}}
          encapsulation untagged
          xconnect vsi vni_{{`{{.VNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa

      roce_leaf__s6850__del_netport_from_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}
          port link-type access
          service-instance {{`{{.VNI}}`}}
          undo encapsulation
          service-instance {{`{{.DefaultVNI}}`}}
          encapsulation untagged
          xconnect vsi vni_{{`{{.DefaultVNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa

      roce_leaf__s9855-24b8d__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}
          port link-type access
          service-instance {{`{{.DefaultVNI }}`}}
          undo encapsulation
          service-instance {{`{{.VNI}}`}}
          encapsulation untagged
          xconnect vsi vni_{{`{{.VNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa

      roce_leaf__s9855-24b8d__del_netport_from_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}
          port link-type access
          service-instance {{`{{.VNI}}`}}
          undo encapsulation
          service-instance {{`{{.DefaultVNI}}`}}
          encapsulation untagged
          xconnect vsi vni_{{`{{.DefaultVNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa

      roce_leaf__ce8851__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}.{{`{{.DefaultVNI}}`}} mode l2
        undo encapsulation untag
        commit
        interface {{`{{.NetPortName}}`}}.{{`{{.VNI}}`}} mode l2
        undo encapsulation dot1q vid {{`{{.VNI}}`}}
        commit
        encapsulation untag
        commit

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa

      roce_leaf__ce8851__del_netport_from_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        interface {{`{{.NetPortName}}`}}.{{`{{.VNI}}`}} mode l2
        undo encapsulation untag
        commit
        encapsulation dot1q vid {{`{{.VNI}}`}}
        commit
        interface {{`{{.NetPortName}}`}}.{{`{{.DefaultVNI}}`}} mode l2
        encapsulation untag
        commit

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa

      roce_leaf__s9827-128dh__add_netport_to_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        disp current-configuration interface {{`{{.NetPortName}}`}}
        interface {{`{{.NetPortName}}`}}
          port link-type access
          port access vlan {{`{{.VNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status ACTIVE -type ipa

      roce_leaf__s9827-128dh__del_netport_from_vlan: |-
        # CR: ipa/{{`{{.CR}}`}}
        # NetDeviceID: {{`{{.NetDeviceID}}`}}
        # NetDeviceName: {{`{{.NetDeviceName}}`}}
        # MGMTIP: {{`{{.MGMTIP}}`}}
        # VNI: {{`{{.VNI}}`}}
        # DefaultVNI: {{`{{.DefaultVNI}}`}}
        # SubnetName: {{`{{.SubnetName}}`}}
        # NetPortName: {{`{{.NetPortName}}`}}

        # ssh username@{{`{{.MGMTIP}}`}}

        system-view
        disp current-configuration interface {{`{{.NetPortName}}`}}
        interface {{`{{.NetPortName}}`}}
          port link-type access
          port access vlan {{`{{.DefaultVNI}}`}}
        save force

        # ./boson-tool -name {{`{{.CR}}`}} --status CREATED -type ipa

kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonNetController.labels" . | nindent 4 }}
