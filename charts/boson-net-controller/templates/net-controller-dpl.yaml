apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonNetController.fullname }}
    {{- include "bosonNetController.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonNetController.fullname }}
  replicas: {{ int .Values.global.bosonNetController.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonNetController.fullname }}
{{- if .Values.global.bosonNetController.generate_deployment_annotations_timestamp }}
      annotations:
        timestamp: {{ now | date "2006-01-02T15:04:05" | quote }}
{{- end }}
    spec:
      serviceAccount: {{ .Values.global.bosonNetController.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonNetController.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonNetController.fullname }}
        image: "{{ .Values.global.bosonNetControllerImage }}"
        command: [ "/{{ .Values.global.bosonNetController.fullname }}", "--conf=/etc/{{ .Values.global.bosonNetController.fullname }}/config.yaml", "--env={{ .Values.global.envName }}", "--leader-elect={{ .Values.global.bosonNetController.leaderElect }}" ]
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: {{ int .Values.global.bosonNetController.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ int .Values.global.bosonNetController.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ int .Values.global.bosonNetController.service.ports.metrics }}
          protocol: TCP
{{- if .Values.global.bosonNetController.config.slb.enable }}
        - name: xds
          containerPort: {{ int .Values.global.bosonNetController.service.ports.xds }}
          protocol: TCP
{{- end }}
        resources:
          limits:
            cpu: {{ .Values.global.bosonNetController.cpuLimits }}
            memory: {{ .Values.global.bosonNetController.memLimits }}
          requests:
            cpu: 100m
            memory: 200Mi
        env:
        - name: BOSON_NET_CONTROLLER_VAR_NAME
          value: "{{ .Values.global.bosonNetController.envs.varName }}"
        - name: CONFIG
          value: "/etc/{{ .Values.global.bosonNetController.fullname }}/config.yaml"
        - name: ENVNAME
          value: {{ .Values.global.envName }}
        - name: BOSON_NET_CONTROLLER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NET_CONTROLLER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NET_CONTROLLER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NET_CONTROLLER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: {{ .Values.global.bosonNetController.fullname }}-config
          mountPath: /etc/{{ .Values.global.bosonNetController.fullname }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ int .Values.global.bosonNetController.service.ports.http }}
          periodSeconds: 30
          initialDelaySeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: {{ int .Values.global.bosonNetController.service.ports.http }}
          periodSeconds: 10
          initialDelaySeconds: 10
      volumes:
      - name: {{ .Values.global.bosonNetController.fullname }}-config
        configMap:
          defaultMode: 420
          name: {{ .Values.global.bosonNetController.fullname }}-config
