apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}-pre-hook
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonNetController.fullname }}-pre-hook
    {{- include "bosonNetController.labels" . | nindent 4 }}
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: {{ .Values.global.bosonNetController.fullname }}-pre-hook
    spec:
      serviceAccount: {{ .Values.global.bosonNetController.fullname }}-hook
      affinity:
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Never
      containers:
      - name: {{ .Values.global.bosonNetController.fullname }}-pre-hook
        image: "{{ .Values.global.hookImage }}"
        command:
        - /bin/bash
        - -c
        - |
          {{- if .Values.global.bosonNetController.config.netDeviceEnable }}
          ndcUrl={{ .Values.global.bosonNetController.config.netDeviceServerURL }}
          echo "start NDC service check"

          response_code=$(curl -s -o /dev/null -w "%{http_code}" $ndcUrl)
          if [[ $response_code -eq 200 ]]; then
            echo "NDC service check successful"
          else
            echo "Error: NDC service check failed"
            exit 1
          fi
          {{- end }}

          echo "PG DB connection check"
          PGPASSWORD={{ .Values.global.pgsql.password }} psql -h {{ .Values.global.pgsql.host }} -p {{ .Values.global.pgsql.port }} -U {{ .Values.global.pgsql.user }} -d {{ .Values.global.pgsql.db }} -c "select count(*) from vpcs;" > /dev/null;
          if [[ $? -eq 0 ]]; then
            echo "psql connection successful"
          else
            echo "Error: psql connection failed"
            exit 1
          fi

          if kubectl get svc -n {{ .Values.global.namespace }} {{ .Values.global.bosonNetController.fullname }}-service &> /dev/null; then
                  clusterIP=$(kubectl get svc -n {{ .Values.global.namespace }} {{ .Values.global.bosonNetController.fullname }}-service -o=jsonpath='{.spec.clusterIP}')

                  if [[ "$clusterIP" != "{{ .Values.global.bosonNetController.service.clusterIP }}" ]]; then
                          echo "update service {{ .Values.global.bosonNetController.fullname }}-service, delete old"
                          kubectl delete svc -n {{ .Values.global.namespace }} {{ .Values.global.bosonNetController.fullname }}-service
                  else
                          echo "service {{ .Values.global.bosonNetController.fullname }}-service check successful"
                  fi
          else
                  echo "service {{ .Values.global.bosonNetController.fullname }}-service not exist, check skip"
          fi
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
