apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-slb-privileged-containers
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - slb-*
        namespaces:
        - {{ .Values.global.bosonNetController.config.slb.namespace }}
