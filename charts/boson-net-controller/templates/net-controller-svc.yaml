{{- template "bosonNetController.svc" . }}
  - name: http
    port: {{ int .Values.global.bosonNetController.service.ports.http }}
    protocol: TCP
  - name: grpc
    port: {{ int .Values.global.bosonNetController.service.ports.grpc }}
    protocol: TCP
  - name: metrics
    port: {{ int .Values.global.bosonNetController.service.ports.metrics }}
    protocol: TCP
{{- if .Values.global.bosonNetController.config.slb.enable }}
  - name: xds
    port: {{ int .Values.global.bosonNetController.service.ports.xds }}
    protocol: TCP
  - name: slb-http
    port: {{ int .Values.global.bosonNetController.service.ports.slbHttp }}
    protocol: TCP
  - name: slb-grpc
    port: {{ int .Values.global.bosonNetController.service.ports.slbGrpc }}
    protocol: TCP
{{- end }}
