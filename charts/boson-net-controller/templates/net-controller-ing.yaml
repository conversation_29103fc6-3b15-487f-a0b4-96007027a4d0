apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.bosonNetController.fullname }}-service-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    {{- include "bosonNetController.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.domainName }}
    http:
      paths:
      - path: /network/{{ .Values.global.bosonNetController.fullname }}(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.bosonNetController.fullname }}-service
            port:
              number: {{ int .Values.global.bosonNetController.service.ports.http }}
  tls:
  - hosts:
    - {{ .Values.global.domainName }}
    secretName: {{ .Values.global.sslSecretName }}
