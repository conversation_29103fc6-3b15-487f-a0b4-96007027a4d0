
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # bosonNetControllerImage defines boson-net-controller image and tag.
  bosonNetControllerImage: registry.sensetime.com/sensecore-boson/boson-net-controller:v1.7.0-0-gf61cc51-20231019160454
  # hook image, use boson-assistant
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:v1.8.0-1-g2b3dabe-20231116213725

  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # enableServiceMonitor 定义是否生成对接Diamond Prometheus 的服务的ServiceMonitor资源的yaml
  enableServiceMonitor: true
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName: network-internal.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  # wxURL 定义微信通知URL
  wxURL: "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=51e84518-fcf6-4654-9fe4-9186779a7df3"
  # reginal的pgsql连接信息
  pgsql:
    host: xxx
    port: 12345
    user: xxx
    db: boson_service_v2
    password: ""
  # bosonNetController variables
  boson_default:
    zone_name_for_az_resource: cn-sh-01a
    zone_name_for_region_resource: cn-sh-01z
  bosonNetController:
    # fullname defines name of boson-net-controller and references every where.
    fullname: "boson-net-controller"
    # replicas defines number of pods running in cluster.
    replicas: 3
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 400m
    # memLimits defines memory limit used by pods.
    memLimits: 1024Mi
    # 是否在 deployment 中增加当前时间的 annotation，以便在部署是可以更新deployment 出发 rolling update
    generate_deployment_annotations_timestamp: false
    # controller leader-elect, set false when replicas is 1
    leaderElect: "true"
    # envs defines environment related values.
    envs:
      # varName defines variable used for Boson Service
      varName: "boson-net-controller"
    config:
      # Disable features: dnat, eip, ipa, shadownode, snat, subnet, vpcnatgateway, bms-lazy-attach
      disableFeatures:
      - "snat"
      - "vpcnatgateway"
      dataNetworkSwitch: msn3420
      ibManagerEnable: true
      netDeviceEnable: false
      netDeviceServerURL: http://************:5050
      netDeviceAccessKey: WTNsM1VuWnpSa3R4UW1oNlpXNVBDZz09Cg==
      globalSnatRuleName: sensecore_devxx_vpc_default_snat_rule_cn_sh_01
      globalSnatQosPolicyName: sensecore_prod_default_tenant_snat_qos_policy
      globalDsnatQosPolicyName: sensecore_prod_default_tenant_dnat_qos_policy
      sourceZone: Trust
      destinationZone: Untrust
      defaultSecurityPolicyRuleName: Default_policy
      defaultInnerSecurityPolicyRuleName: Sensecore_prod_inter_nat
      minNatAddressGroupID: 10000
      maxNatAddressGroupID: 65535
      # gc 的时间间隔
      gcIntervalSec: 86400
      # gc 一个subnet的时间间隔
      gcPauseSec: 60
      gcEnable: false
      slb:
        enable: false
        bgpEnable: false
        agentEnable: true
        namespace: plat-boson-infra
        envoyAdminPort: 19000
        deployment:
          resource:
            limits:
              cpu: 1000m
              memory: 1Gi
            requests:
              cpu: 500m
              memory: 100Mi
          agent:
            # 和 envoy 启动在同一个pod, 需要岔开 port; envoy参数: envoyAdminPort
            admin_http_port: 19500
            admin_grpc_port: 19501
            admin_metrics_port: 19502
            image: registry.sensetime.com/sensecore-boson/boson-envoy-dataplane:v1.27.0-6-g0a49c3e4e6-20240327202832
            cmd:
              - sh
            args:
              - -c
              - while true; do sleep 1; done
            readinessProbeConfig: |-
              exec:
                command:
                - sh
                - -c
                - ls
              failureThreshold: 3
              periodSeconds: 5
              successThreshold: 1
              timeoutSeconds: 2
          image: registry.sensetime.com/sensecore-boson/boson-envoy-dataplane:v0.0.1
          cmd:
            - sh
          args:
            - -c
            - while true; do sleep 1; done
          privileged: true
          allowPrivilegeEscalation: true
          tolerationsConfig: |-
            - effect: NoExecute
              key: diamond.sensetime.com/role-k8s-master
              operator: Equal
              value: enabled
            - effect: NoSchedule
              key: diamond.sensetime.com/role-business-app
              operator: Equal
              value: sensecore
            - effect: NoSchedule
              key: diamond.sensetime.com/belong-resource-prp
              operator: Exists
          affinityConfig: |-
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                - matchExpressions:
                  - key: diamond.sensetime.com/role-infra-vpc-gw
                    operator: In
                    values:
                    - enabled
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: ovn.kubernetes.io/vpc-nat-gw
                      operator: Exists
                  topologyKey: kubernetes.io/hostname
                weight: 100
          termTime: 0
          readinessProbeConfig: |-
            exec:
              command:
              - sh
              - -c
              - ls
            failureThreshold: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          basicNetworkNamespace: kube-system
          basicNetworkNAD: ovn-vpc-external-network
          replica: 1
        envoyConfigTemplatePath: /etc/envoy/envoy.yaml
        # 使用 defaultVpcNatGw 对外 slb 的 pod 暴露，net-controller 的服务, 所以这个地址是 defaultVpcNatGw 的 net1的vip
        xDSControllerServiceHost: "0.0.0.0"
        xDSServerGrpcKeepaliveTime: 3
        xDSServerGrpcKeepaliveTimeout: 3
        xDSServerGrpcKeepaliveMinTime: 3
        xDSServerGrpcMaxConcurrentStreams: 1000000
        upgrade:
        - "730"
      pprof:
        pprofEnable: false
        metricsPath: /metrics
        pprofPath: /debug/pprof/


    # service defines ports for service.
    service:
      clusterIP: "**********"
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "52080"
        # grpc port number for container, service, ingress.
        grpc: "52100"
        # metrics port number for container, service, service monitor.
        metrics: "52030"
        # xds port number
        xds: "18000"
        # http server port
        slbHttp: "52040"
        # grpc port number for container, service, ingress.
        slbGrpc: "52090"
        # pprof port
        pprof: "52050"
