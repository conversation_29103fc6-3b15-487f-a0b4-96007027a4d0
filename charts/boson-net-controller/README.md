# Boson Net Controller

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
        - [需要特别注意的参数](#需要特别注意的参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

Boson Net Controller 作为 Boson 服务中南向 API 接口程序，配合北向 Boson Provider 的资源操作，配置南向细节的网络资源，如交换机、防火墙、物理机等配置。

## 准备工作

boson-net-controller 组件需要部署到 Diamond TK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的数据库 `boson_service_v2` 已经创建。如未创建，请参考[PostgreSql DB 创建](../../deployment/deployment-guide.md#postgresql-db-%E5%88%9B%E5%BB%BA)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-net-controller
# or pull specific chart version
# helm3 pull boson/boson-net-controller --version x.y.z

tar -xzvf boson-net-controller-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-net-controller/values.yaml)
- [dev11](../../envs/dev11/boson-net-controller/values.yaml)
- [dev44](../../envs/dev44/boson-net-controller/values.yaml)
- [集成测试](../../envs/int-test/boson-net-controller/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-net-controller/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-net-controller/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-net-controller/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

#### 需要特别注意的参数

- **global.pgsql.password**: 很多环境的密码是空的或者是加密的，因此部署前需要修改环境的数据库密码

## 服务部署

### 部署服务

```bash
helm3 install boson-net-controller boson-net-controller -n plat-boson-service -f path/to/env/boson-net-controller/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-net-controller boson-net-controller -n plat-boson-service -f path/to/env/boson-net-controller/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-net-controller -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME                   READY   UP-TO-DATE   AVAILABLE   AGE
# boson-net-controller   1/1     1            1           2m38s
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-service get pod
# NAME                                    READY   STATUS      RESTARTS   AGE
# boson-net-controller-7d46b8b5d4-84ph5   1/1     Running     0          2m38s
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-net-controller-64464584-h5scz
# 2022-09-29T15:31:42.067	info	setup	/usr/local/go/src/runtime/proc.go:255	runtime.main	boson net controller config	{.........................................}
# 2022-09-29T15:31:42.190	info	setup	/usr/local/go/src/runtime/proc.go:255	runtime.main	IB manager enabled
# I0929 15:31:43.241191       1 request.go:665] Waited for 1.04182472s due to client-side throttling, not priority and fairness, request: GET:https://10.96.0.1:443/apis/flowcontrol.apiserver.k8s.io/v1beta1?timeout=32s
# 2022-09-29T15:31:43.693	info	controller-runtime.metrics	/go/pkg/mod/sigs.k8s.io/controller-runtime@v0.11.0/pkg/manager/manager.go:354	sigs.k8s.io/controller-runtime/pkg/manager.New	Metrics server is starting to listen	{"addr": ":52030"}
# 2022-09-29T15:31:43.693	info	setup	/usr/local/go/src/runtime/proc.go:255	runtime.main	starting manager
# 2022-09-29 15:31:43.694152623 +0000 UTC Logger.check error: failed to get caller
# 2022-09-29T15:31:43.694	info	Starting server	{"path": "/metrics", "kind": "metrics", "addr": "[::]:52030"}
# 2022-09-29 15:31:43.69424031 +0000 UTC Logger.check error: failed to get caller
# 2022-09-29T15:31:43.694	info	Starting server	{"kind": "health probe", "addr": "[::]:52080"}
# 2022-09-29T15:31:43.694	info	controller.dnat	/go/pkg/mod/sigs.k8s.io/controller-runtime@v0.11.0/pkg/manager/runnable_group.go:218	sigs.k8s.io/controller-runtime/pkg/manager.(*runnableGroup).reconcile.fu```
