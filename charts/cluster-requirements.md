


# 环境前提定义文件


## namespace

- KK 的 namespace：`plat-boson-service`
- TK 的 namespace：`plat-boson-service`、`plat-boson-infra`

## imagePullSecret

- sensecore-boson: 需要提前确定这个 secret 使用的账号对于 boson 的镜像仓是否有访问权限。对于私有化场景，也需要确认私有化镜像仓的访问权限。

## Domain 和 TLS 证书



## 数据库需求定义

提前创建数据库并创建具备数据库完整操作权限的账号。

- 数据库名称：`boson_service_v2`
- 用户名：`boson`

系统提供数据：

- 数据库链接地址：IP地址，或者k8s 内部服务svc域名
- 数据库端口号：每个集群都不一样
- 数据库密码：
  - 生产环境无需密码，配置“xxxxxxxx”即可。需要通过sensecore-values的App.yaml 配置 secretPatches 的 patch。
  - 对于测试环境，可以获取密码配置values

## RocketMQ 的topic和consumer group权限定义


作为RP需要给云管RM和Boss系统发送资源操作结果的消息，需要定义topic和访问权限。secretKey 需要通过sensecore-values的App.yaml 配置 secretPatches 的 patch。

- 接收topic消息是SUB，不仅要定义topic，还需要定义consumerGroup的SUB权限。
- 发消息的topic是PUB，不需要申请groupPerms，比如给RM、BOSS、云管发消息。

### KK 的配置和权限

```yaml
- accessKey: rocketmq-ak-boson-provider
  secretKey: aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeffff
  topicPerms:
  # 给 RM 系统发送操作成功失败的 topic，由资源操作消息触发的返回消息
  - sensetime-core-rm-resource-state-sync=PUB
  # 给 BOSS 系统发送操作成功失败的 topic，由资源操作消息触发的返回消息
  - sensetime-core-resource-operation-result=PUB
  # 给云管消息中心发送消息的topic，按需发送
  - sensetime-core-message-engine-msg=PUB
  # 接收RM发送 专线 资源操作消息的topic
  - sensetime-core-network-dc-v1-{{ region }}z=SUB
  groupPerms:
  # 从 专线 的Topic消费消息的consumerGroup权限
  - sensetime-core-network-v1-kk-consumer=SUB
```
### TK 的配置和权限

```yaml
- accessKey: rocketmq-ak-boson-provider
  secretKey: aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeffff
  topicPerms:
  # 给 RM 系统发送操作成功失败的 topic，由资源操作消息触发的返回消息
  - sensetime-core-rm-resource-state-sync=PUB
  # 给 BOSS 系统发送操作成功失败的 topic，由资源操作消息触发的返回消息
  - sensetime-core-resource-operation-result=PUB
  # 给云管消息中心发送消息的topic，按需发送
  - sensetime-core-message-engine-msg=PUB
  # 接收RM发送 VPC、EIP、SLB 资源操作消息的topic
  - sensetime-core-network-vpc-v1-{{ region }}z=SUB
  - sensetime-core-network-eip-v1-{{ region }}a=SUB
  - sensetime-core-network-slb-v1-{{ region }}a=SUB
  groupPerms:
  # 从 VPC、EIP、SLB 的Topic消费消息的consumerGroup权限
  - sensetime-core-network-v1-{{ region }}a-consumer=SUB
  - sensetime-core-network-v1-{{ region }}a-consumer-eip=SUB
  - sensetime-core-network-v1-{{ region }}a-consumer-slb=SUB
```

### MQ 配置示例

```yaml
- accessKey: rocketmq-ak-boson-provider
  topicPerms:
  - sensetime-core-rm-resource-state-sync=PUB
  - sensetime-core-resource-operation-result=PUB
  - sensetime-core-message-engine-msg=PUB
  - sensetime-core-network-vpc-v1-sensecore-stack-01z=SUB
  - sensetime-core-network-eip-v1-sensecore-stack-01a=SUB
  - sensetime-core-network-slb-v1-sensecore-stack-01a=SUB
  groupPerms:
  - sensetime-core-network-v1-sensecore-stack-01a-consumer=SUB
  - sensetime-core-network-v1-sensecore-stack-01a-consumer-eip=SUB
  - sensetime-core-network-v1-sensecore-stack-01a-consumer-slb=SUB
```
