# Internal DNS

<!-- TOC -->

- [配置信息组成](#配置信息组成)
- [Internal DNS 的部署](#internal-dns-的部署)
- [Internal DNS 的回滚](#internal-dns-的回滚)
- [生成 Internal DNS 配置文件](#生成-internal-dns-配置文件)
- [MISC](#misc)
  - [`[安装包]/nslookup_xxxx.sh` 校验解析服务器](#安装包nslookup_xxxxsh-校验解析服务器)
  - [生成新建View的Secret](#生成新建view的secret)

<!-- /TOC -->

## 配置信息组成

查看 Internal DNS 的配置文件，需要在 `./envs/xxxx-01z/internal-dns` 目录下查看。比如：`cn-sh-01z`、`tech-01z`、`dev44-01z`。`internal-dns` 目录包含了用于部署 Internal DNS 服务的所有配置文件，具体内容如下：

- `./named-settings.yaml`：用于生成 Internal DNS 所有配置文件的配置信息。其中，包含 master/slave 服务器的 IP 地址，上游 DNS 服务器，各个分区 view、acl、zone 的域名和解析 IP 的配置。通过修改 `named-settings.yaml` 的内容可以重新生成所有配置文件。具体方法见：[生成 Internal DNS 配置文件](#生成-internal-dns-配置文件)
- `./master/etc/internal-dns-xx.xx.xx.xx.named.conf`：master 节点的 `named.conf` 配置文件。文件名中的 IP 地址为 master 节点的服务器 IP 地址。配置文件应位于服务器 `/etc/named.conf`。
- `./slave/etc/internal-dns-xx.xx.xx.xx.named.conf`slave 节点的 `named.conf` 配置文件。文件名中的 IP 地址为 slave 节点的服务器 IP 地址。配置文件位于服务器 `/etc/named.conf`。
- `./var/named/zones/*.db`：所有分区 view 下面配置的 zone 的解析文件。文件名命名格式为 `[view.name]-[view.domains.domain].db`。这些文件被 `named.conf` 文件中的 zone 所引用。解析配置文件应位于服务器 `/var/named/zones` 目录中。
- `[安装包]/named-check.sh`：修复检查配置文件权限并检查配置文件的命令行。其中，重新加载配置的命令被注释掉了，以免误操作运行。只有在所有配置文件检查完成之后，才可以重新加载配置。
- `[安装包]/nslookup_xxxx.sh`：根据 `named-settings.yaml` 的配置生成在各区检查域名解析的测试脚本，分别指定 master/slave 两个 DNS 服务器进行测试。注：脚本只检查了配置的域名解析，实际测试时，还需要测试不在 Internal DNS 配置中的域名解析功能是否正常。

## Internal DNS 的部署

找到要部署环境下面 named.conf 在master和slave节点上的配置，以及zones的配置信息。

- [上海公有云 cn-sh-01z](../../envs/cn-sh-01z/internal-dns/)
- [tech测试 tech-01z](../../envs/tech-01z/internal-dns/)
- [dev环境 dev44-01z](../../envs/dev44-01z/internal-dns/)

**准备工作**：由于 Internal DNS 的部署要将多个文件复制到 master 和 slave 的服务器上，因此，在开始部署之前请事先将部署文档复制到服务器的某个目录，在部署时使用。

登录 master 的服务器，切换到 `root` 权限下运行一下部署操作。

1. 将Internal dns 配置的 VIP 从当前 master 节点切到 slave 节点去。

   ```bash
   # 查看服务器运行的 KeepAlived 的容器，应该可以看到 dns-dps-keepalived 的实例
   nerdctl ps | grep keepalived

   # 查看当前 InternalDNS 的VIP配置
   ip a show bond0

   # 由于dns和dps公用keepalived的实例，暂时没有好方法进行单独 VIP 的切换，
   # 需要将一台服务器的 keepalived 的服务停掉，以便所有 VIP 都切换到另一台服务器上
   nerdctl pause dns-dps-keepalived

   # 确认 DNS 的 VIP 飘到 slave 节点
   ip a show bond0
   ```

2. 备份原有配置：在 master 机器运行以下命令行。

   ```bash
   backup_tag=$(date +'%Y-%m-%d_%H-%M-%S')
   cp /etc/named.conf /etc/named-old-"${backup_tag}".conf
   echo "Backup config file '/etc/named.conf' to '/etc/named-old-${backup_tag}.conf'"
   if [[ -d /var/named/zones/ ]]; then
      cp -r /var/named/zones /var/named/zones-old-"${backup_tag}"
      echo "Backup zone files '/var/named/zones' to '/var/named/zones-old-${backup_tag}'"
   fi
   ```

3. 将 `./master/etc/internal-dns-xx.xx.xx.xx.named.conf` 内容复制到 master 机器的 `/etc/named.conf` 文件中。
4. 将 `./var/named/zones/*.db` 的所有文件，复制到 master 机器的 `/var/named/zones` 目录中。
5. 在 master 机器上，使用 `[安装包]/named-check.sh` 中的检查命令进行配置检查。
6. **当确认配置检查无误后**，在 master 机器上运行 `systemctl restart named` 加载新的配置使之生效。
7. 分别登录各个区的服务器，使用 `[安装包]/nslookup_xxxx.sh` 中的命令行进行域名解析 IP 地址的验证。注意，执行脚本前，先参照[修改脚本解析服务器](#安装包nslookup_xxxxsh-修改解析服务器) 调整解析服务器 IP 地址。

   ```bash
   # 校验 master 服务器的域名解析，以公有云 A 区为例
   bash nslookup_prod01a.sh master

   # 校验 slave 服务器的域名解析，以公有云 A 区为例
   bash nslookup_prod01a.sh slave

   # 校验 master 和 slave 服务器的域名解析，以公有云 A 区为例
   bash nslookup_prod01a.sh all
   ```

8. 将Internal dns 配置的 VIP 恢复到当前 master 节点。

   ```bash
   # 由于dns和dps公用keepalived的实例，暂时没有好方法进行单独 VIP 的切换，
   nerdctl start dns-dps-keepalived
   ```

9. **当域名解析测试验证通过后**，在 slave 的服务器上，重复执行 1~8 步，完成配置和验证。

## Internal DNS 的回滚

登录 master 的服务器，切换到 `root` 权限下运行一下部署操作。

1. 将Internal dns 配置的 VIP 从当前 master 节点切到 slave 节点去。

   ```bash
   # 由于dns和dps公用keepalived的实例，暂时没有好方法进行单独 VIP 的切换，
   # 需要将一台服务器的 keepalived 的服务停掉，以便所有 VIP 都切换到另一台服务器上
   nerdctl pause dns-dps-keepalived
   ```

2. 还原备份配置：分别在 master 机器运行以下命令行。

   ```bash
   backup_tag=$(date +'%Y-%m-%d_%H-%M-%S')
   mv /etc/named.conf /etc/named-new-"${backup_tag}".conf
   # 此处的 YYYY-MM-DD_HH-MI-SS 需要替换成备份时候的时间戳
   mv /etc/named-old-YYYY-MM-DD_HH-MI-SS.conf /etc/named.conf
   if [[ -d /var/named/zones/ ]]; then
      mv /var/named/zones /var/named/zones-new-"${backup_tag}"
      # 此处的 YYYY-MM-DD_HH-MI-SS 需要替换成备份时候的时间戳
      mv /var/named/zones-old-YYYY-MM-DD_HH-MI-SS /var/named/zones
   fi
   ```

3. 在 master 机器上，使用 `[安装包]/named-check.sh` 中的检查命令进行配置检查。
4. **当确认配置检查无误后**，在 master 机器上运行 `systemctl restart named` 加载新的配置使之生效。
5. 将Internal dns 配置的 VIP 恢复到当前 master 节点。

   ```bash
   # 由于dns和dps公用keepalived的实例，暂时没有好方法进行单独 VIP 的切换，
   nerdctl start dns-dps-keepalived
   ```

6. **当域名解析测试验证通过后**，在 slave 的服务器上，重复执行 1~5 步，完成配置和验证。

## 生成 Internal DNS 配置文件

运行一下命令可以生成 Internal DNS 的配置文件。

```bash
# 生成指定 region 的 Internal-dns 配置
python3 "tools/env-gen/env_values.py" internal-dns cn-sh-01z
python3 "tools/env-gen/env_values.py" internal-dns tech-01z
python3 "tools/env-gen/env_values.py" internal-dns dev44-01z

# 生成多个 region 的配置
python3 "tools/env-gen/env_values.py" internal-dns cn-sh-01z tech-01z dev44-01z
```

## MISC

以下内容无用，纯调试使用.

```bash
cd /var/named/zones
ls | xargs -I {} sed -i "s/10.118./110.118./g" {}
ls | xargs -I {} sed -i "s/110.118./10.118./g" {}
```

### `[安装包]/nslookup_xxxx.sh` 校验解析服务器

由于 InternalDNS 的 master 和 slave 服务器有自己的 IP 以及对应的 VIP，因此在做域名解析检查时，需要根据变更步骤调整域名服务器 IP。

安装包检查脚本的 `dns_servers` 会包含对应 master IP+VIP、对应 slave IP+VIP、master 和 slave 的 IP+VIP 的地址配置，用于单独检验 master 变更、slave 变更、最终完成变更的域名解析结果。请根据实际情况使用不同的命令行参数，以便校验不同的域名解析服务器。

```bash
if [[ "${1}" == "master" ]]; then
    # master dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ ***********"
elif [[ "${1}" == "slave" ]]; then
    # slave dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ ***********"
elif [[ "${1}" == "all" ]]; then
    # master and slave dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ *********** ************ ***********"
fi
```

### 生成新建View的Secret

参考工具：<https://manpages.debian.org/testing/bind9/tsig-keygen.8.en.html>

```bash
# 以cn-sh-01f为例
$ tsig-keygen cn-sh-01f
key "cn-sh-01f" {
	algorithm hmac-sha256;
	secret "9rc4XCB9Q9pntil9CrxbJpj4hVbbxbs5pmInjZKym0k=";
};
```
