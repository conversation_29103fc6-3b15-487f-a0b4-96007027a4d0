{{/*
Expand the name of the chart.
*/}}
{{- define "bosonOMS.name" -}}
{{- default .Values.global.bosonOMS.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonOMS.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "bosonOMS.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonOMS.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "bosonOMS.labels" -}}
helm.sh/chart: {{ include "bosonOMS.chart" . }}
{{ include "bosonOMS.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonOMS.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}



{{/*
Expand the name of the chart.
*/}}
{{- define "omsDashboard.name" -}}
{{- default .Values.global.bosonOMS.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}-dashboard
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "omsDashboard.chart" -}}
{{- printf "%s-dashboard-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
omsDashboard Selector labels
*/}}
{{- define "omsDashboard.selectorLabels" -}}
app.kubernetes.io/name: {{ include "omsDashboard.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "omsDashboard.labels" -}}
helm.sh/chart: {{ include "omsDashboard.chart" . }}
{{ include "omsDashboard.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonOMS.fullname }}-dashboard
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}
