---
apiVersion: v1
data:
  {{- if .Values.global.omsDashboardImage }}
  boson-oms-dashboard.yaml: |-
    region: {{ .Values.global.region }}
    http_port: {{ .Values.global.omsDashboard.http }}

    south_db_pg:
    {{- with .Values.global.bosonOMS.omsConfig.southDB }}
      host: {{ .host }}
      port: {{ .port }}
      user: {{ .user }}
      password: {{ .password }}
      db: {{ .db }}
    {{- end }}

    click_house:
    {{- with .Values.global.omsDashboard.clickHouse }}
      host: {{ .host }}
      port: {{ .port }}
      user: {{ .user }}
      password: {{ .password }}
      database: {{ .database }}
    {{- end }}
  {{- end }}
  boson-oms.yaml: |-
    region: {{ .Values.global.region }}
    oms_server:
      grpc_port: {{ .Values.global.bosonOMS.service.ports.grpc}}
      http_port: {{ .Values.global.bosonOMS.service.ports.http}}
      metrics_port: {{ .Values.global.bosonOMS.service.ports.metrics}}
      #log_file: "./log/boson_oms.log"
    # ndc server url and access key
    ndc:
      server_url: {{ .Values.global.bosonOMS.omsConfig.ndcServer.serverURL }}
      access_key: {{ .Values.global.bosonOMS.omsConfig.ndcServer.accessKey }}
    # ip query configuration
    k8sInfo:
      enabled: {{ .Values.global.bosonOMS.k8sInfo.enabled }}
      env: {{ .Values.global.bosonOMS.k8sInfo.env }}
      region: {{ .Values.global.bosonOMS.k8sInfo.region }}
      dryrun: {{ .Values.global.bosonOMS.k8sInfo.dryrun }}
      kubeConfigs:
      {{- if .Values.global.bosonOMS.k8sInfo.enabled }}
      {{- with .Values.global.bosonOMS.k8sInfo.kubeConfigs }}
        {{- range . }}
        - zone: {{ .zone }}
          kubeConfigFile: {{ .kubeConfigFile }}
          enabled: {{ .enabled }}
          region: {{ .region }}
        {{- end }}
      {{- end }}
      {{- end }}
      kubectlTemplateStrs:
        vpc_nat_gw_ip: |-
          ip={{ `{{ .IP }}` }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          for hagw in $(kubectl get hanatgws.network.sensecore.cn | grep hagw | awk  '{print $1}')
          do
            {
              kubectl get hanatgws.network.sensecore.cn $hagw -o json | jq .spec.extendInfos | grep -q -w $ip
            } && {
              # 如果在hanatgw中拿到了这个信息，则返回该hanatgw所在的vpc的信息
              kubectl get hanatgws.network.sensecore.cn $hagw -o json | jq '.metadata.ownerReferences | .[0].name' | tr -d '"\n'
              find=1
              break
            };
          done
          if [ $find -eq 0 ]; then
            echo "not found hagw with ip $ip"
            exit 0
          fi
        vpc_pod_ip: |-
          ip={{ `{{ .IP }}` }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          tenant_regex="^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$"
          tenantId={{ `{{ .TENANT_ID }}` }}
          { echo "$tenantId" | grep -Eq "$tenant_regex"; } || {
            echo "tenantId $tenantId is not valid.";
            exit 1;
          }
          nsList=$(kubectl get vpcs.kubeovn.io | grep $tenantId | grep ns | awk '{print $4}' | sort -u | tr -d '["]')
          find=0
          for ns in $nsList
          do
            if [ "$ns" == "" ]; then
              ns="default"
              vpc=ovn-cluster
            else
              vpc=$(kubectl get vpcs.kubeovn.io | grep $ns | awk '{print $1}')
            fi
            pods=$(kubectl get pod -oname -n $ns | awk -F '/' '{print $2}')
            for pod in $pods
            do
              kubectl get pod $pod -owide -n $ns | grep -q -w $ip
              if [ "$?" == "0" ]; then
                # 如果在ns中的pod中查到了ip信息，则返回该pod的简要信息
                #kubectl get pod $pod -n $ns -ojson
                echo "$vpc;$pod" | tr -d '"'
                find=1
                break
              fi
            done
          done
          if [ $find -eq 0 ]; then
            echo "not found pod with $ip"
            exit 0
          fi
        vpc_bms_ip: |-
          ip={{ `{{ .IP }}` }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          subnets=$(kubectl get ipas.network.sensecore.cn | grep -w "$ip" | grep -o "sn-.*-.*")
          for subnet in $subnets
          do
            vpc=$(kubectl get subnets.network.sensecore.cn $subnet -ojson |  jq '.spec.vpc')
            if [ "$?" == "0" ]; then
              echo $vpc | tr -d '"\n'
              find=1
              break
            fi
          done
          if [ $find -eq 0 ]; then
            echo "not found bms with $ip"
            exit 0
          fi
        eip: |-
          ip={{ `{{ .IP }}` }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          eip=$(kubectl get eips.network.sensecore.cn | grep -w $ip | awk '{print $1}')
          if [ "$eip" != "" ]; then
            vpc=$(kubectl get eips.network.sensecore.cn $eip -ojson | jq '.metadata.ownerReferences | .[0].name');
            if [ "$vpc" != "" ]; then
              echo $vpc | tr -d '"\n'
              find=1
            fi
          fi
          if [ $find -eq 0 ]; then
            echo "not found eip with $ip"
            exit 0
          fi
        service_ip: |-
          ip={{ `{{ .IP }}` }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          service=$(kubectl get services -A | grep -w $ip);
          if [ "$service" != "" ]; then
          {
            ns=$(echo $service | awk '{print $1}')
            if [ "$ns" != "" ]; then
              vpc=$(kubectl get vpcs.kubeovn.io | grep $ns | awk '{print $1}')
              if [ "$vpc" != "" ]; then
                echo $vpc | tr -d '"\n'
                find=1
              else
                echo "no vpc found, but find service $ip in ns $ns"
                find=1
              fi
            fi
          }
          fi
          if [ $find -eq 0 ]; then
            echo "not found service with $ip"
            exit 0
          fi
    # zone name to env map
    zone_to_env:
    {{- with .Values.global.bosonOMS.omsConfig.zoneToEnv }}
      {{- range . }}
      - zone: {{ .zone }}
        env: {{ .env }}
      {{- end }}
    {{- end }}
    south_db_pg:
    {{- with .Values.global.bosonOMS.omsConfig.southDB }}
      host: {{ .host }}
      port: {{ .port | quote }}
      user: {{ .user }}
      password: {{ .password }}
      db: {{ .db }}
    {{- end }}
    oms_db_pg:
    {{- with .Values.global.bosonOMS.omsConfig.omsDB }}
      host: {{ .host }}
      port: {{ .port | quote }}
      user: {{ .user }}
      password: {{ .password }}
      db: {{ .db }}
    {{- end }}
    dcos_server:
      domain: {{ .Values.global.bosonOMS.omsConfig.dcosServer | quote }}
      default_key: "4f68b359-3935-4724-81de-d8c495e133d7"
      apis:
        - name: "list_network_device"
          ak: "ada5b7ad-3b9b-4e3e-b919-dc9ec7064db6"
        - name: "get_server_by_ip"
          ak: "securityServerWyeq1One"
        - name: "list_sensecore_servers"
          ak: "4f68b359-3935-4724-81de-d8c495e133d7"
        - name: "list_physical_servers"
          ak: "db6b890e-a8e7-4def-bae0-e177f538a3a0"
        - name: "get_tor"
          ak: "torQueryList"
        - name: "switch_to_switch"
          ak: "lineQueryList"
    dcos_tracker:
      # dcos tacker interval in hours
      interval: 24
    provider:
      provider_addrs:
      {{- with .Values.global.bosonOMS.omsConfig.providerAddr }}
        {{- range . }}
        - zone: {{ .zone }}
          addr: {{ .host }}
        {{- end }}
      {{- end }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonOMS.labels" . | nindent 4 }}
