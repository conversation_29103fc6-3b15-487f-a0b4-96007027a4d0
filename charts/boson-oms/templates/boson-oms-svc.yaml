apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonOMS.fullname }}-service
    {{- include "bosonOMS.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.bosonOMS.fullname }}
  ports:
  - name: http
    port: {{ int .Values.global.bosonOMS.service.ports.http }}
    protocol: TCP
  - name: grpc
    port: {{ int .Values.global.bosonOMS.service.ports.grpc }}
    protocol: TCP
  - name: metrics
    port: {{ int .Values.global.bosonOMS.service.ports.metrics }}
    protocol: TCP
