# HTTP ingress host and path couldn't be same to GRPC ingress, and GRPC ingress path must be /, so this path is /network.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Chart.Name }}-service-ingress-grpc
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/ssl-redirect: "True"
  labels:
    {{- include "bosonOMS.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.domainName }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ .Chart.Name }}-service
            port:
              number: {{ int .Values.global.bosonOMS.service.ports.grpc }}
  tls:
  - hosts:
    - {{ .Values.global.domainName }}
    secretName: {{ .Values.global.sslSecretName }}
