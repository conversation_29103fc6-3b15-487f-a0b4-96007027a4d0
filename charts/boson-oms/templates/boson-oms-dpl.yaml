apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonOMS.fullname }}
    {{- include "bosonOMS.labels" . | nindent 4}}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonOMS.fullname }}
  replicas: {{ int .Values.global.bosonOMS.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonOMS.fullname }}
    spec:
      serviceAccount: {{ .Values.global.bosonOMS.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonOMS.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonOMS.fullname }}
        image: "{{ .Values.global.bosonOmsImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-oms
        - -c
        - /boson/conf/boson-oms.yaml
        ports:
        - name: http
          containerPort: {{ int .Values.global.bosonOMS.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ int .Values.global.bosonOMS.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ int .Values.global.bosonOMS.service.ports.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonOMS.cpuLimits }}
            memory: {{ .Values.global.bosonOMS.memLimits }}
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: BOSON_OMS_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_OMS_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_OMS_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_OMS_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: {{ int .Values.global.bosonOMS.service.ports.http }}
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: {{ int .Values.global.bosonOMS.service.ports.http }}
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/{{ .Values.global.bosonOMS.fullname }}.yaml
          subPath: path/to/{{ .Values.global.bosonOMS.fullname }}.yaml
        {{- if .Values.global.bosonOMS.k8sInfo.enabled }}
        {{- with .Values.global.bosonOMS.k8sInfo.kubeConfigs }}
        {{- range . }}
        - name: {{ .zone }}-kubeconfig
          mountPath: {{ .kubeConfigFile }}
        {{- end }}
        {{- end }}
        {{- end }}
      volumes:
      - name: config
        configMap:
          name: {{ .Values.global.bosonOMS.fullname }}-config
          items:
          - key: {{ .Values.global.bosonOMS.fullname }}.yaml
            path: path/to/{{ .Values.global.bosonOMS.fullname }}.yaml
      {{- if .Values.global.bosonOMS.k8sInfo.enabled }}
      {{- with .Values.global.bosonOMS.k8sInfo.kubeConfigs }}
      {{- range . }}
      - name: {{ .zone }}-kubeconfig
        configMap:
          name: {{ $.Values.global.bosonOMS.fullname }}-{{ .zone }}-kubeconfig
          items:
          - key: kubeconfig
            path: kubeconfig
      {{- end }}
      {{- end }}
      {{- end }}


---
{{- if .Values.global.omsDashboardImage }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.omsDashboard.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.omsDashboard.fullname }}
    {{- include "omsDashboard.labels" . | nindent 4}}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.omsDashboard.fullname }}
  replicas: {{ int .Values.global.omsDashboard.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.omsDashboard.fullname }}
    spec:
      serviceAccount: {{ .Values.global.omsDashboard.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.omsDashboard.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.omsDashboard.fullname }}
        image: "{{ .Values.global.omsDashboardImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/oms-dashboard
        - -c
        - /boson/conf/boson-oms-dashboard.yaml
        resources:
          limits:
            cpu: 300m
            memory: 300Mi
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: BOSON_OMS_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_OMS_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_OMS_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_OMS_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: {{ int .Values.global.omsDashboard.http }}
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: {{ int .Values.global.omsDashboard.http }}
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/{{ .Values.global.omsDashboard.fullname }}.yaml
          subPath: path/to/{{ .Values.global.omsDashboard.fullname }}.yaml
      volumes:
      - name: config
        configMap:
          name: {{ .Values.global.bosonOMS.fullname }}-config
          items:
          - key: {{ .Values.global.omsDashboard.fullname }}.yaml
            path: path/to/{{ .Values.global.omsDashboard.fullname }}.yaml
{{- end }}
