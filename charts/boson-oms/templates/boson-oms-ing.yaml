apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}-service-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
  labels:
    {{- include "bosonOMS.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.domainName }}
    http:
      paths:
      - path: /v1
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.bosonOMS.fullname }}-service
            port:
              number: {{ int .Values.global.bosonOMS.service.ports.http }}
  tls:
  - hosts:
    - {{ .Values.global.domainName }}
    secretName: {{ .Values.global.sslSecretName }}
