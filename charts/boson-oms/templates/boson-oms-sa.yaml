apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.bosonOMS.fullname }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.bosonOMS.fullname }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.bosonOMS.fullname }}
  namespace: {{ .Values.global.namespace }}

# OMS Dashboard service account
{{- if .Values.global.omsDashboardImage }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.omsDashboard.fullname }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.omsDashboard.fullname }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.omsDashboard.fullname }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.omsDashboard.fullname }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.omsDashboard.fullname }}
  namespace: {{ .Values.global.namespace }}
{{- end }}
