
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  region: cn-sh-01
  zone: cn-sh-01a
  prp: cn-sh-01a-prp01
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-infra
  # bosonOmsImage defines boson-oms image and tag.
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.0.0
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # 如果配置了omsDashboardImage，则会生成omsDashboard的deployment. 这么做主要是为了使oms平滑过渡到kk
  # omsDashboardImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.0.0
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName:  boson-oms-internal.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  # bosonAssistant variables
  bosonOMS:
    # region
    region: "cn-sh-01"
    # fullname defines name of boson-assistant and references every where.
    fullname: "boson-oms"
    # replicas defines number of pods running in cluster.
    replicas: 1
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 300m
    # memLimits defines memory limit used by pods.
    memLimits: 300Mi
    # service defines ports for service.
    service:
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "55980"
        # grpc port number for container, service, ingress.
        grpc: "55990"
        # metric port number for container
        metrics: "55982"
    k8sInfo:
      enabled: true
      dryrun: False
      env: dev44
      kubeConfigs:
      - zone: cn-sh-01b
        region: cn-sh-01
        env: dev44
        enabled: True
        kubeConfigFile: /boson/cn-sh-01b/bosonOmsOp.kubeconfig
      - zone: cn-sh-01p
        region: cn-sh-01
        env: tech
        enabled: True
        kubeConfigFile: /boson/cn-sh-01p/bosonOmsOp.kubeconfig
      - zone: cn-cq-01a
        region: cn-cq-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-cq-01a/bosonOmsOp.kubeconfig
      - zone: cn-fj-01a
        region: cn-fj-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-fj-01a/bosonOmsOp.kubeconfig
      - zone: cn-sz-01a
        region: cn-sz-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sz-01a/bosonOmsOp.kubeconfig
      - zone: cn-sh-01c
        region: cn-sh-01
        env: tech
        enabled: True
        kubeConfigFile: /boson/cn-sh-01c/bosonOmsOp.kubeconfig
      - zone: cn-sh-01a
        region: cn-sh-01
        env: tech
        enabled: True
        kubeConfigFile: /boson/cn-sh-01a/bosonOmsOp.kubeconfig
      - zone: cn-sh-01b
        region: cn-sh-01
        env: tech
        enabled: True
        kubeConfigFile: /boson/cn-sh-01b/bosonOmsOp.kubeconfig
      - zone: cn-sh-01a
        region: cn-sh-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sh-01a/bosonOmsOp.kubeconfig
      - zone: cn-sh-01p
        region: cn-sh-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sh-01p/bosonOmsOp.kubeconfig
      - zone: cn-sh-01b
        region: cn-sh-01
        enabled: True
        env: prod
        kubeConfigFile: /boson/cn-sh-01b/bosonOmsOp.kubeconfig
      - zone: cn-sh-01d
        region: cn-sh-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sh-01d/bosonOmsOp.kubeconfig
      - zone: cn-gz-01a
        region: cn-gz-01
        enabled: True
        env: prod
        kubeConfigFile: /boson/cn-gz-01a/bosonOmsOp.kubeconfig
      - zone: st-sh-01a
        region: st-sh-01
        enabled: True
        env: prod
        kubeConfigFile: /boson/st-sh-01a/bosonOmsOp.kubeconfig
      - zone: cn-stage-01a
        region: cn-stage-01
        enabled: True
        env: stage
        kubeConfigFile: /boson/cn-stage-01a/bosonOmsOp.kubeconfig
      - zone: ms-sc-01a
        region: ms-sc-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/ms-sc-01a/bosonOmsOp.kubeconfig
      - zone: cn-sh-01a
        region: cn-sh-01
        enabled: True
        env: dev44
        kubeConfigFile: /boson/cn-sh-01a/bosonOmsOp.kubeconfig
      - zone: cn-sh-01p
        region: cn-sh-01
        env: dev44
        enabled: True
        kubeConfigFile: /boson/cn-sh-01p/bosonOmsOp.kubeconfig
      - zone: cn-jn-01a
        region: cn-jn-01
        enabled: True
        env: prod
        kubeConfigFile: /boson/cn-jn-01a/bosonOmsOp.kubeconfig
    omsConfig:
      dcosServer: dcos-api.sensetime.com
      ndcServer:
        serverURL: http://10.53.69.204:5053
        accessKey: WTNsM1VuWnpSa3R4UW1oNlpXNVBDZz09Cg==
      zoneToEnv:
        - zone: dev44
          env: dev44
        - zone: cn-tech-01a
          env: test
        - zone: cn-stage-01a
          env: prod-cn-stage-01
        - zone: st-sh-01a
          env: prod-st-sh-01
        - zone: cn-sh-01a
          env: prod
        - zone: cn-sh-01b
          env: prod-cn-sh-01b
        - zone: cn-sh-01c
          env: prod-cn-sh-01c
        - zone: cn-sh-01d
          env: prod-cn-sh-01d
        - zone: cn-gz-01a
          env: prod-gz
        - zone: cn-cq-01a
          env: prod-cn-cq-01a
        - zone: cn-sz-01a
          env: prod-cn-sz-01a
        - zone: ms-sc-01a
          env: prod-ms-sc-01a
        - zone: cn-fj-01a
          env: prod-cn-fj-01a
      southDB:
        host: "XX.XX.XX.XX"
        port: "XXXX"
        user: "boson"
        password: "XXXX"
        db: "XXXX"
      omsDB:
        host: "XX.XX.XX.XX"
        port: "XXXX"
        user: "boson"
        password: "XXXX"
        db: "XXXX"
      providerAddr:
        - zone: cn-sh-01a
          host: network-internal.cn-sh-01.sensecoreapi.dev
  omsDashboard:
    fullname: "boson-oms-dashboard"
    replicas: 1
    http: "55983"
    clickHouse:
      host: "XX.XX.XX.XX"
      port: XXXX
      user: "boson"
      password: "XXXX"
      database: "XXXX"
