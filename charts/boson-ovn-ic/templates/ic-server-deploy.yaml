{{- $splitNodeIps := split "," .Values.global.node_ips -}}
{{- $replicas := len $splitNodeIps -}}
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ovn-ic-server
  namespace: {{ .Values.global.namespace }}
  annotations:
    kubernetes.io/description: |
      OVN IC Server
  labels:
    name: ovn-ic-server
    {{- include "boson-ovn-ic.labels" . | nindent 4 }}
spec:
  replicas: {{ $replicas }}
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  selector:
    matchLabels:
      app: ovn-ic-server
  template:
    metadata:
      labels:
        app: ovn-ic-server
        component: network
        type: infra
    spec:
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: ovn-ic-server
              topologyKey: kubernetes.io/hostname
      priorityClassName: system-cluster-critical
      serviceAccountName: {{ .Values.global.fullname }}
      hostNetwork: true
      imagePullSecrets:
        - name: sensecore-boson
      containers:
        - name: ovn-ic-server
          image: {{ .Values.global.ovn_image }}
          imagePullPolicy: {{ .Values.global.image_pull_policy }}
          command:
            - bash
            - /kube-ovn/start-ic-db.sh
          securityContext:
            capabilities:
              add: ["SYS_NICE"]
          env:
            - name: ENABLE_SSL
              value: "false"
            - name: TS_NUM
              value: {{ .Values.global.ts_num | quote }}
            - name: NODE_IPS
              value: {{ .Values.global.node_ips }}
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: OVN_LEADER_PROBE_INTERVAL
              value: {{ .Values.global.ovn_leader_probe_interval | quote }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IPS
              valueFrom:
                fieldRef:
                  fieldPath: status.podIPs
          resources:
            requests:
              cpu: 300m
              memory: 200Mi
            limits:
              cpu: 3
              memory: 1Gi
          volumeMounts:
            - mountPath: /var/run/ovn
              name: host-run-ovn
            - mountPath: /etc/ovn
              name: host-config-ovn
            - mountPath: /var/log/ovn
              name: host-log-ovn
            - mountPath: /etc/localtime
              name: localtime
          readinessProbe:
            exec:
              command:
                - bash
                - /kube-ovn/ovn-ic-healthcheck.sh
            periodSeconds: 15
            timeoutSeconds: 45
          livenessProbe:
            exec:
              command:
                - bash
                - /kube-ovn/ovn-ic-healthcheck.sh
            initialDelaySeconds: 30
            periodSeconds: 15
            failureThreshold: 5
            timeoutSeconds: 4
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-boson-ovn-ic-db: enabled
      volumes:
        - name: host-run-ovn
          hostPath:
            path: /run/ovn
        - name: host-config-ovn
          hostPath:
            path: /etc/origin/ovn
        - name: host-log-ovn
          hostPath:
            path: /var/log/ovn
        - name: localtime
          hostPath:
            path: /etc/localtime
