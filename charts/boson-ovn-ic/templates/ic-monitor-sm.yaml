apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  annotations:
  name: ovn-ic-monitor
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "boson-ovn-ic.labels" . | nindent 4 }}
spec:
  endpoints:
  - interval: 15s
    port: metrics
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  selector:
    matchLabels:
      app: ovn-ic-monitor
