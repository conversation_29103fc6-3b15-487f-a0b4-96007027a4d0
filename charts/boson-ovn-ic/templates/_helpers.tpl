{{/*
Expand the name of the chart.
*/}}
{{- define "boson-ovn-ic.name" -}}
{{- default .Values.global.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "boson-ovn-ic.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "boson-ovn-ic.selectorLabels" -}}
app.kubernetes.io/name: {{ include "boson-ovn-ic.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "boson-ovn-ic.labels" -}}
helm.sh/chart: {{ include "boson-ovn-ic.chart" . }}
{{ include "boson-ovn-ic.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}
