{{- $splitNodeIps := split "," .Values.global.node_ips -}}
{{- $replicas := len $splitNodeIps -}}
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ovn-ic-monitor
  namespace: {{ .Values.global.namespace }}
  labels:
    name: ovn-ic-monitor
    {{- include "boson-ovn-ic.labels" . | nindent 4 }}
spec:
  replicas: {{ $replicas }}
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  selector:
    matchLabels:
      app: ovn-ic-monitor
  template:
    metadata:
      labels:
        app: ovn-ic-monitor
        component: network
        type: infra
    spec:
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: ovn-ic-monitor
              topologyKey: kubernetes.io/hostname
      priorityClassName: system-cluster-critical
      serviceAccountName: {{ .Values.global.fullname }}
      hostNetwork: true
      imagePullSecrets:
       - name: sensecore-boson
      containers:
        - name: ovn-ic-monitor
          image: {{ .Values.global.ovn_image }}
          imagePullPolicy: {{ .Values.global.image_pull_policy }}
          command: ["/kube-ovn/kube-ovn-ic-monitor"]
          securityContext:
            capabilities:
              add: ["SYS_NICE"]
          env:
            - name: NODE_IPS
              value: {{ .Values.global.node_ips }}
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            limits:
              cpu: {{ .Values.global.cpuLimits }}
              memory: {{ .Values.global.memLimits }}
            requests:
              cpu: 300m
              memory: 300Mi
          volumeMounts:
            - mountPath: /var/run/openvswitch
              name: host-run-ovs
            - mountPath: /var/run/ovn
              name: host-run-ovn
            - mountPath: /sys
              name: host-sys
              readOnly: true
            - mountPath: /etc/openvswitch
              name: host-config-openvswitch
            - mountPath: /etc/ovn
              name: host-config-ovn
            - mountPath: /var/log/openvswitch
              name: host-log-ovs
            - mountPath: /var/log/ovn
              name: host-log-ovn
            - mountPath: /etc/localtime
              name: localtime
          readinessProbe:
            exec:
              command:
              - cat
              - /var/run/ovn/ovn_ic_nb_db.pid
            periodSeconds: 10
            timeoutSeconds: 45
          livenessProbe:
            exec:
              command:
              - cat
              - /var/run/ovn/ovn_ic_nb_db.pid
            initialDelaySeconds: 30
            periodSeconds: 10
            failureThreshold: 5
            timeoutSeconds: 45
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-boson-ovn-ic-db: enabled
      volumes:
        - name: host-run-ovs
          hostPath:
            path: /run/openvswitch
        - name: host-run-ovn
          hostPath:
            path: /run/ovn
        - name: host-sys
          hostPath:
            path: /sys
        - name: host-config-openvswitch
          hostPath:
            path: /etc/origin/openvswitch
        - name: host-config-ovn
          hostPath:
            path: /etc/origin/ovn
        - name: host-log-ovs
          hostPath:
            path: /var/log/openvswitch
        - name: host-log-ovn
          hostPath:
            path: /var/log/ovn
        - name: localtime
          hostPath:
            path: /etc/localtime
