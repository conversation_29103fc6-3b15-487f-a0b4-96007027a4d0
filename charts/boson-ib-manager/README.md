# Boson IB Manager

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [部署集群中已有 IB 集群的分区配置数据](#部署集群中已有-ib-集群的分区配置数据)
    - [配置部署参数](#配置部署参数)
- [服务部署](#服务部署)
    - [备份数据](#备份数据)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)
    - [检查opensm](#检查opensm)
- [升级部署](#升级部署)

<!-- /TOC -->

该版本做的重大更新是，一套chart可以部署多套ibmanager服务，一套ibmanager纳管一套ib集群。
其中一套ibmanager包含如下：

```bash

独立的deployment
独立的service
独立的ing
独立的configmap
独立的leaselock
```

共享的有如下
```bash
service account
```

> 特别注意：不同于普通的业务服务，Boson IB Manager 是用来替代 IB 集群中的 OpeSM 组件对整个 IB 网络进行服务化管理的组件。因此，该服务的部署需要 **系统SRE** 同学一起配合，关闭集群上原有的 OpenSM 服务，而切换到 Boson IB Manager 服务对集群进行管理。

## 准备工作

boson-ib-manager 组件需要部署到 Diamond TK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

- 对当前TK需要部署几套集群已经很清楚，且已经更新了相关values文件

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-ib-manager
# or pull specific chart version
# helm3 pull boson/boson-ib-manager --version x.y.z

tar -xzvf boson-ib-manager-x.y.z.tgz
```

### 部署集群中已有 IB 集群的分区配置数据

由于 IB-Manager 和 IB 集群交换机必须一一对应，因此，有几个configmap配置必须根据交换机集群配置。部署服务之前，需要备份相关的configmap 信息，如下，以备回滚使用。其中 `0X` 为 IB 集群定义，如 `ib-cluster-01`、 `ib-cluster-02`，在每个环境中编号必须严格定义。

- boson-ibm-ib-cluster-0X-opensm-config
- boson-ibm-ib-cluster-0X-root-guid-config
- partitions-ib-cluster-0X.conf

```bash
TIME_PREFIX=$(date "+%Y-%m-%d_%H-%M-%S")
for i in $(seq 0 10); do
  ib_num=$(printf "%02d\n" $i)
  if kubectl -n plat-boson-service get configmap |  grep "boson-ibm-ib-cluster-${ib_num}"; then
    echo "backup ib-manager configmap for boson-ibm-ib-cluster-${ib_num}"
    kubectl -n plat-boson-service get configmap "boson-ibm-ib-cluster-${ib_num}-opensm-config" -oyaml > "${TIME_PREFIX}-boson-ibm-ib-cluster-${ib_num}-opensm-config".yaml
    kubectl -n plat-boson-service get configmap "boson-ibm-ib-cluster-${ib_num}-root-guid-config" -oyaml > "${TIME_PREFIX}-boson-ibm-ib-cluster-${ib_num}-root-guid-config".yaml
    kubectl -n plat-boson-service get configmap "partitions-ib-cluster-${ib_num}.conf" -oyaml > "${TIME_PREFIX}-partitions-ib-cluster-${ib_num}".yaml
  fi
done
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-ib-manager/values.yaml)
- [dev11](../../envs/dev11/boson-ib-manager/values.yaml)
- [dev44](../../envs/dev44/boson-ib-manager/values.yaml)
- [集成测试](../../envs/int-test/boson-ib-manager/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-ib-manager/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-ib-manager/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-ib-manager/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

## 服务部署

### 备份数据


```bash


```

### 部署服务

例子中以ib-cluster-00为例子，实际部署的时候起替换成真是的ib集群的名字。

```bash
helm3 install boson-ib-manager-ib-cluster-00 boson-ib-manager -n plat-boson-service -f path/to/env/boson-ib-manager/ib-cluster-00/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-ib-manager-ib-cluster-00 boson-ib-manager -n plat-boson-service -f path/to/env/boson-ib-manager/ib-cluster-00/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-ib-manager-ib-cluster-00 -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
# root @ HOST-10-111-38-44 in ~/litianqing/deploy/boson-charts on git:feat/multi-ib-refactor x [17:54:45]
$ kubectl -n plat-boson-service get deployment | grep ibm
boson-ibm-ib-cluster-00           1/1     1            1           3d22h
```

### 查看运行的Pod

```bash
# root @ HOST-10-111-38-44 in ~/litianqing/deploy/boson-charts on git:feat/multi-ib-refactor x [17:56:51]
$ kubectl -n plat-boson-service get pod | grep ibm
boson-ibm-ib-cluster-00-7587498ffc-bz64x           1/1     Running     0          3d22h
```

### 检查服务启动日志

```bash
# root @ HOST-10-111-38-44 in ~/litianqing/deploy/boson-charts on git:feat/multi-ib-refactor x [17:56:53]
$ kubectl -n plat-boson-service logs boson-ibm-ib-cluster-00-7587498ffc-bz64x
[2023-05-17 11:40:00] [info] [init.go:51] Initialize Boson IB Manager
[2023-05-17 11:40:00] [info] [manager.go:28] Get into main, begining working.
[2023-05-17 11:40:00] [info] [ibConfig.go:94] configPath is /boson/boson-ib-manager.yaml
[2023-05-17 11:40:00] [info] [partitionManager.go:56] log level is info
W0517 11:40:00.168116       6 client_config.go:617] Neither --kubeconfig nor --master was specified.  Using the inClusterConfig.  This might not work.
[2023-05-17 11:40:00] [info] [manager.go:48] begin http health server at 53800
W0517 11:40:00.168568       6 client_config.go:617] Neither --kubeconfig nor --master was specified.  Using the inClusterConfig.  This might not work.
I0517 11:40:00.168781       6 leaderelection.go:248] attempting to acquire leader lease plat-boson-service/boson-ib-manager-ib-cluster-00...
I0517 11:40:00.277741       6 leaderelection.go:258] successfully acquired lease plat-boson-service/boson-ib-manager-ib-cluster-00
[2023-05-17 11:40:00] [info] [manager.go:89] ***********:53900 start leading
[2023-05-17 11:40:00] [info] [server.go:76] close the grpc server
[2023-05-17 11:40:00] [info] [manager.go:107] the new leader: ***********:53900
[2023-05-17 11:40:00] [info] [manager.go:113] I'm the new leader(***********:53900)
[2023-05-17 11:40:00] [info] [partitionManager.go:105] pack to file management=0x7fff,ipoib,defmember=full : ALL,ALL_SWITCHES=full,SELF=full;
[2023-05-17 11:40:00] [info] [osmProcessManager.go:42] exec cmd /etc/init.d/opensmd start
[2023-05-17 11:40:01] [info] [osmProcessManager.go:65] get opensm pid from file /var/run/opensm.pid is 30
[2023-05-17 11:40:01] [info] [osmProcessManager.go:87] opensm pid is 30
[2023-05-17 11:40:01] [info] [manager.go:41] Manager init done
[2023-05-17 11:40:01] [info] [metrics.go:33] Exporter serve to work.
[2023-05-17 11:40:01] [info] [manager.go:44] exporter begin serve
[2023-05-17 11:40:01] [info] [metrics.go:48] Metrics Server start :53300
[2023-05-17 11:40:01] [info] [server.go:25] I'm the leader, start grpc server on port 53900
[2023-05-17 11:40:01] [info] [server.go:68] grpc server start at port :53900
```


### 检查opensm

在服务部署完ibmanager之后，需要检查subnet的管理权(opensm)是否已经转移到ibmanager所在的节点。
检查当前subnet是sm通过如下命令可以获取，如果和ibmanager在同一个节点则说明，该IB网络的
opensm已经托管至ibmanager。如果不是，则需要进一步排查解决为什么没有托管。

命令如下：

> smpquery NodeDesc `sminfo | awk '{print $4}'`

例子:

```bash
root at HOST-10-111-41-11 in /home/<USER>
 ○ smpquery NodeDesc `sminfo | awk '{print $4}'`
Node Description:........HOST-10-111-41-13 mlx4_0
```


## 升级部署
**重要**
备份分区数据，分区数据目前放在configmap中，所以在升级之前对其进行备份。

partition的configmmap的名字是partitions.conf
```bash
kubectl get cm/partitions.conf -o yaml -n plat-boson-service > partitions.conf.original
```

根据集群name不同，创建不同的configmap，并将上一步备份的数据放到文件partitions-new-01.conf

```bash
---
apiVersion: v1
data:
  partitions.conf: 'management=0x7fff,ipoib,defmember=full : ALL,ALL_SWITCHES=full,SELF=full;
  备份的数据 <--------
'
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
  name: partitions-ib-cluster-03.conf
  namespace: plat-boson-service
```

请注意name的命名是有规则的

```
clusterName=ib-cluster-03
name: partitions-${clusterName}.conf
```
