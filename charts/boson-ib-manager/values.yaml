# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  # 注意node的selector和这里是对应的
  clusterName: "ib-cluster-00"
  shortname: "boson-ibm"
  fullname: "boson-ib-manager"
  serviceInfo:
    enableSharp: false
    SharpCheckFileList: "/var/log/opensm-smdb.dump"
    SharpInitialDelaySeconds: 5
    SharpCheckFileListRetryIntervalSecs: 3
    SharpCheckFileListRetryTimes: 1
    enableOpensmLog: true
    opensmLogRatelimitEnabled: true
    opensmLogRateLimitMinute: 1000
    enableIBExporter: true
    region: "cn-sh-01"
    az: "cn-sh-01a"
    prp: "cn-sh-01a-prp01"
    httpPortbase: 53800
    grpcPortbase: 53900
    metricsPortbase: 53300
    exporterPortBase: 53400
    # value is one of opensm, ufm, or dummy
    ibClusterType: opensm
    useSystemTimeZone: true
    opensmConfPath: /boson/config/opensm/
    # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
    envName: devxx
    # 从镜像站下载镜像的账号，需要提前创建docker pull secret
    imagePullSecret: sensecore-boson
    bosonIBManagerImage: registry.sensetime.com/sensecore-boson/boson-ib-manager:v1.0.0-2-gdf2c0b8-20230327164447
    bosonIBExporterImage: registry.sensetime.com/sensecore-boson/treydock/infiniband_exporter:v0.4.2
    # enableServiceMonitor 定义是否生成对接Diamond Prometheus 的服务的ServiceMonitor资源的yaml
    enableServiceMonitor: true
    # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
    sslSecretName: tls-cnsh01-api
    log_level: info
    ctrl_name: opensm
    slow_ops_threshold_milli_seconds_config:
      http_request_rt: 1000
      k8s_request_rt: 1000
    sm_priority: 15
    master_sm_priority: 15
    opensm_ar:
      routing_engine: "ar_updn"
      root_guid_file: "/etc/opensm/boson/root_guid.conf"
      scatter_ports: 8
      routing_threads_num: 0
      use_ucast_cache: "TRUE"
      ar_mode: 3
      shield_mode: 3
      ar_sl_mask: "0xFFFF"
      adv_routing_engine: auto
      ar_transport_mask: "0xA"
      enable_ar_by_device_cap: "TRUE"
    # opensm_m_key 必须得带双引号，不然会被当做一个大数，导致最后配置文件中是一个科学计数法记录的数字
    opensm_m_key: "0xf8b5fb2f6e93386f"
    health_check_method: sensetime.core.network.ibmanager.v1.IBManager.HealthCheck
    # fullname defines name of boson-ib-manager and references every where.
    fullname: "boson-ib-manager"
    shortname: "boson-ibm"
    # replicas defines number of pods running in cluster.
    replicas: 2
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 2
    # memLimits defines memory limit used by pods.
    memLimits: 4Gi
    # cpuLimits defines cpu limit used by exporter pods.
    exporter_cpuLimits: 1
    # memLimits defines memory limit used by exporter pods.
    exporter_memLimits: 1Gi
    # envs defines environment related values.
    envs:
      # varName defines variable used for Boson Service
      varName: "boson-ib-manager"
    # domainName 定义了 boson ib manager ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
    domainName: ib-manager-internal.cn-sh-01.sensecoreapi.dev
    # nodeSelectorTerms 定义服务部署所需要的node label标识
    nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
    # key 和matchExpressions之间是逻辑或
    - key: diamond.sensetime.com/role-business-app
      values:
      - sensecore
    # relations in one matchExpression are logical 'and'
    - matchExpressions:
      - key: diamond.sensetime.com/belong-resource-training
        values:
        - ib-cluster-00
      - key: diamond.sensetime.com/role-infra-vpc-ibm
        values:
        - enabled
    # tolerations 定义支持运行机器的node taint
    tolerations:
    - effect: NoExecute
      key: diamond.sensetime.com/role-k8s-master
      operator: Equal
      value: enabled
    - effect: NoSchedule
      key: diamond.sensetime.com/role-business-app
      operator: Equal
      value: sensecore
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-resource-prp
      operator: Exists
