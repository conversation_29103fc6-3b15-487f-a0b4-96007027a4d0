# Boson IB Manager

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)
    - [检查opensm](#检查opensm)
- [更新部署](#更新部署)

<!-- /TOC -->

Boson IB Manager 作为 Boson 服务中管理 IB 网络的关键组件，部署在拥有IB网卡的服务器上，为所用 IB 网络的算力池服务和裸金属实例提供租户隔离的能力。

> 特别注意：不同于普通的业务服务，Boson IB Manager 是用来替代 IB 集群中的 OpeSM 组件对整个 IB 网络进行服务化管理的组件。因此，该服务的部署需要 **系统SRE** 同学一起配合，关闭集群上原有的 OpenSM 服务，而切换到 Boson IB Manager 服务对集群进行管理。

## 准备工作

boson-ib-manager 组件需要部署到 Diamond TK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-ib-manager
# or pull specific chart version
# helm3 pull boson/boson-ib-manager --version x.y.z

tar -xzvf boson-ib-manager-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-ib-manager/values.yaml)
- [dev11](../../envs/dev11/boson-ib-manager/values.yaml)
- [dev44](../../envs/dev44/boson-ib-manager/values.yaml)
- [集成测试](../../envs/int-test/boson-ib-manager/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-ib-manager/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-ib-manager/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-ib-manager/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

## 服务部署

### 部署服务

```bash
helm3 install boson-ib-manager boson-ib-manager -n plat-boson-service -f path/to/env/boson-ib-manager/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-ib-manager boson-ib-manager -n plat-boson-service -f path/to/env/boson-ib-manager/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-ib-manager -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME               READY   UP-TO-DATE   AVAILABLE   AGE
# boson-ib-manager   1/1     1            1           6s
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-service get pod
# NAME                                READY   STATUS      RESTARTS   AGE
# boson-ib-manager-58fc68b7c9-db8gh   1/1     Running     0          6s
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-ib-manager-58fc68b7c9-db8gh
# [2022-09-29 15:23:11] [info] [init.go:51] Initialize Boson IB Manager
# [2022-09-29 15:23:11] [info] [ibConfig.go:60] configPath is /boson/boson-ib-manager.yaml
# [2022-09-29 15:23:11] [info] [ibConfig.go:43] get here
# [2022-09-29 15:23:11] [info] [partitionManager.go:125] initial config map is &ConfigMap{ObjectMeta:{partitions.conf  plat-boson-service /api/v1/namespaces/plat-boson-service/configmaps/partitions.conf d4a612dc-64f7-48d1-af2e-7b06d446a883 31176734 0 2022-09-28 09:10:49 +0000 UTC <nil> <nil> map[app.kubernetes.io/component:boson-ib-manager app.kubernetes.io/instance:boson-ib-manager-v0.3.1 app.kubernetes.io/managed-by:Helm app.kubernetes.io/name:boson-ib-manager app.kubernetes.io/part-of:BosonService app.kubernetes.io/version:0.3.0 helm.sh/chart:boson-ib-manager-0.3.0] map[meta.helm.sh/release-name:boson-ib-manager-v0.3.1 meta.helm.sh/release-namespace:plat-boson-service] [] []  [{Go-http-client Update v1 2022-09-28 09:10:49 +0000 UTC FieldsV1 {"f:data":{},"f:metadata":{"f:annotations":{".":{},"f:meta.helm.sh/release-name":{},"f:meta.helm.sh/release-namespace":{}},"f:labels":{".":{},"f:app.kubernetes.io/component":{},"f:app.kubernetes.io/instance":{},"f:app.kubernetes.io/managed-by":{},"f:app.kubernetes.io/name":{},"f:app.kubernetes.io/part-of":{},"f:app.kubernetes.io/version":{},"f:helm.sh/chart":{}}}} } {boson-ib-manager Update v1 2022-09-28 13:59:29 +0000 UTC FieldsV1 {"f:data":{"f:partitions.conf":{}}} }]},Data:map[string]string{partitions.conf: management=0x7fff,ipoib, defmember=full : ALL, ALL_SWITCHES=full,SELF=full;
# ,},BinaryData:map[string][]byte{},Immutable:nil,}
# [2022-09-29 15:23:11] [info] [osmProcessManager.go:27] exec cmd /etc/init.d/opensmd start
# [2022-09-29 15:23:12] [info] [server.go:44] grpc server start at port :53090
```


### 检查opensm

在服务部署完ibmanager之后，需要检查subnet的管理权(opensm)是否已经转移到ibmanager所在的节点。
检查当前subnet是sm通过如下命令可以获取，如果和ibmanager在同一个节点则说明，该IB网络的
opensm已经托管至ibmanager。如果不是，则需要进一步排查解决为什么没有托管。

命令如下：

> smpquery NodeDesc `sminfo | awk '{print $4}'`

例子:

```bash
root at HOST-10-111-41-11 in /home/<USER>
 ○ smpquery NodeDesc `sminfo | awk '{print $4}'`
Node Description:........HOST-10-111-41-13 mlx4_0
```


## 更新部署

**重要**

在执行upgrade之前为了保证运行时数据不被删除，需要做cm进行配置保持。
执行命令如下：
```bash

kubectl annotate cm/partitions.conf helm.sh/resource-policy=keep -n plat-boson-service
```

为了保证万无一失，请在upgrade之前备份一下partitions.conf文件内容。

```bash

kubectl get cm/partitions.conf -o yaml -n plat-boson-service > partitions.conf.original
```

upgrade之后请检查paritions.conf是否被删除或者重置，如果没有则不需要恢复，
如果有，则需要手动将数据恢复。

恢复方式：

将partitions.conf.original中的data部分加入到现在的partitions.conf中
