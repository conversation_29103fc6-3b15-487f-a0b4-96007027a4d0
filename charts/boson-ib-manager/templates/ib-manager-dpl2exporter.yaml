{{- if and .Values.global.serviceInfo.enableIBExporter (ne .Values.global.serviceInfo.ibClusterType "dummy") }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
  replicas: 1
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
    spec:
      priorityClassName: system-cluster-critical
      hostNetwork: true
      serviceAccount: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.serviceInfo.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range $index, $nodeSelectorTerms := . }}
            - matchExpressions:
            {{- if $nodeSelectorTerms.matchExpressions }}
              {{- range $expressions := $nodeSelectorTerms.matchExpressions }}
              - key: {{ $expressions.key | quote }}
                operator: In
                values:
                {{- range $expressions.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- else }}
              - key: {{ $nodeSelectorTerms.key | quote }}
                operator: In
                values:
                {{- range $nodeSelectorTerms.values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
        image: "{{ .Values.global.serviceInfo.bosonIBExporterImage }}"
        imagePullPolicy: IfNotPresent
        args:
          - --web.listen-address=:{{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.exporterPortBase }}
        securityContext:
          privileged: true
          capabilities:
            add:
            - IPC_LOCK
        ports:
        - name: exporter
          containerPort: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.exporterPortBase }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.serviceInfo.exporter_cpuLimits }}
            memory: {{ .Values.global.serviceInfo.exporter_memLimits }}
            rdma/hca: "1"
          requests:
            cpu: 50m
            memory: 100Mi
            rdma/hca: "1"
        env:
        - name: BOSON_IB_MANAGER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_IB_MANAGER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_IB_MANAGER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_IB_MANAGER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/"
            port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.exporterPortBase }}
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: "/"
            port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.exporterPortBase }}
          initialDelaySeconds: 10
          periodSeconds: 30
          timeoutSeconds: 5
{{- end }}
