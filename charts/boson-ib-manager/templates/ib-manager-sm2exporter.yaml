{{- if and .Values.global.serviceInfo.enableIBExporter (ne .Values.global.serviceInfo.ibClusterType "dummy") }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.shortname}}-{{ .Values.global.clusterName }}-exporter
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: exporter
    interval: 30s
    honorLabels: true
    scrapeTimeout: 30s
{{- end }}
