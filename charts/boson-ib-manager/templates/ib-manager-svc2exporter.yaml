{{- if and .Values.global.serviceInfo.enableIBExporter (ne .Values.global.serviceInfo.ibClusterType "dummy") }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-exporter
  ports:
  - name: exporter
    port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.exporterPortBase  }}
    protocol: TCP
{{- end }}
