{{/*
Expand the name of the chart.
*/}}
{{- define "bosonIBManager.name" -}}
{{- default .Values.global.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonIBManager.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "bosonIBManager.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonIBManager.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "bosonIBManager.labels" -}}
helm.sh/chart: {{ include "bosonIBManager.chart" . }}
{{ include "bosonIBManager.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}

{{/*
get index from clusterName
usage:
bosonIBManager.getClusterIndex clusterName
*/}}
{{- define "bosonIBManager.getClusterIndex" -}}
{{ add (printf "%s" .Values.global.clusterName | trunc -1 | int) (mul (printf "%s" .Values.global.clusterName | trunc -2 | trunc 1| int ) 10) }}
{{- end -}}

{{/*
get service yaml
usage:
bosonIBManager.getService $ clusterName
*/}}
{{- define "bosonIBManager.getService" }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
  ports:
  - name: http
    port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.httpPortbase  }}
    protocol: TCP
  - name: grpc
    port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.grpcPortbase }}
    protocol: TCP
  - name: metrics
    port: {{ add (include "bosonIBManager.getClusterIndex" .) .Values.global.serviceInfo.metricsPortbase }}
    protocol: TCP
{{- end }}

{{/*
usage:
bosonIBManger.getServiceMonitor $ clusterName
*/}}
{{- define "bosonIBManager.getServiceMonitor" }}
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.shortname}}-{{ .Values.global.clusterName }}-servicemonitor
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
{{- end -}}


{{/*
usage:
bosonIBManger.getServiceAccount .
*/}}
{{- define "bosonIBManager.getServiceAccount" }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
  namespace: {{ .Values.global.namespace }}
{{- end -}}


{{/*
usage:
bosonIBManager.getIngress $ cluster
*/}}
{{- define "bosonIBManager.getIngress" }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.serviceInfo.domainName }}
    http:
      paths:
      - path: /network/{{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
            port:
              number: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.httpPortbase }}
  tls:
  - hosts:
    - {{ .Values.global.serviceInfo.domainName }}
    secretName: {{ .Values.global.serviceInfo.sslSecretName }}

{{- end -}}

{{/*
usage:
bosonIBManager.getGRPCIngress $ cluster
*/}}
{{- define "bosonIBManager.getGRPCIngress" }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-grpc-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
  labels:
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.serviceInfo.domainName }}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
            port:
              number: {{ .Values.global.clusterName | trunc -2 | int | add .Values.global.serviceInfo.grpcPortbase }}
  tls:
  - hosts:
    - {{ .Values.global.serviceInfo.domainName }}
    secretName: {{ .Values.global.serviceInfo.sslSecretName }}
{{- end }}

{{/*
usage:
bosonIBManager.getDeployment $ cluster
*/}}
{{- define "bosonIBManager.getDeployment" -}}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
    {{- include "bosonIBManager.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
  replicas: {{ int .Values.global.serviceInfo.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
    spec:
      priorityClassName: system-cluster-critical
      hostNetwork: true
      serviceAccount: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-service
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.serviceInfo.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range $index, $nodeSelectorTerms := . }}
            - matchExpressions:
            {{- if $nodeSelectorTerms.matchExpressions }}
              {{- range $expressions := $nodeSelectorTerms.matchExpressions }}
              - key: {{ $expressions.key | quote }}
                operator: In
                values:
                {{- range $expressions.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- else }}
              - key: {{ $nodeSelectorTerms.key | quote }}
                operator: In
                values:
                {{- range $nodeSelectorTerms.values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}
        image: "{{ .Values.global.serviceInfo.bosonIBManagerImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/dumb-init
        - /boson/boson-ib-manager
        securityContext:
          privileged: true
        ports:
        - name: http
          containerPort: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.httpPortbase }}
          protocol: TCP
        - name: grpc
          containerPort: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.grpcPortbase }}
          protocol: TCP
        - name: metrics
          containerPort: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.metricsPortbase }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.serviceInfo.cpuLimits }}
            memory: {{ .Values.global.serviceInfo.memLimits }}
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: BOSON_IB_MANAGER_VAR_NAME
          value: "{{ .Values.global.serviceInfo.envs.varName }}"
        - name: BOSON_IB_MANAGER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_IB_MANAGER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_IB_MANAGER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_IB_MANAGER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: BOSON_OPENSM_CONFIG_FILE_PATH
          value: "{{ .Values.global.serviceInfo.opensmConfPath }}"
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.httpPortbase }}
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          tcpSocket:
            port: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.grpcPortbase}}
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: config
          mountPath: /boson/{{ .Values.global.fullname }}.yaml
          subPath: path/to/{{ .Values.global.fullname }}.yaml
        - name: opensm-config
          mountPath: {{ .Values.global.serviceInfo.opensmConfPath }}
        {{- if .Values.global.serviceInfo.opensm_ar }}
        - mountPath: /etc/opensm/boson/
          name: root-guid-config
        {{- end }}
        {{- if .Values.global.serviceInfo.useSystemTimeZone }}
        - name: localtime-volume
          mountPath: /etc/localtime
          readOnly: true
        {{- end }}
      volumes:
      - name: config
        configMap:
          name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-config
          items:
          - key: {{ .Values.global.fullname }}.yaml
            path: path/to/{{ .Values.global.fullname }}.yaml
      - name: opensm-config
        configMap:
          name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-opensm-config
      {{- if .Values.global.serviceInfo.opensm_ar }}
      - configMap:
          name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-root-guid-config
        name: root-guid-config
      {{- end }}
      {{- if .Values.global.serviceInfo.useSystemTimeZone }}
      - configMap:
        name: localtime-volume
        hostPath:
          path: /etc/localtime
      {{- end }}
{{- end -}}

{{/*
usage:
bosonIBManager.getConfigMap $ cluster
*/}}
{{- define "bosonIBManager.getConfigMap" -}}
---
apiVersion: v1
data:
  boson-ib-manager.yaml: |-
    boson_default:
      log_level: {{ .Values.global.serviceInfo.log_level }}
      grpc_port: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.grpcPortbase}}
      http_port: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.httpPortbase}}
      metrics_port: {{ add (include "bosonIBManager.getClusterIndex" .)  .Values.global.serviceInfo.metricsPortbase}}
      env: {{ .Values.global.serviceInfo.envName }}
      region: {{ .Values.global.serviceInfo.region }}
      az: {{ .Values.global.serviceInfo.az }}
      prp: {{ .Values.global.serviceInfo.prp }}
      slow_ops_threshold_milli_seconds_config:
      {{- range $k, $v := .Values.global.serviceInfo.slow_ops_threshold_milli_seconds_config }}
        {{ $k }}: {{ $v }}
      {{- end }}
      ibClusterType: {{ .Values.global.serviceInfo.ibClusterType }}
      {{- if .Values.global.serviceInfo.enableSharp }}
      enableSharp: true
      SharpCheckFileList: {{ .Values.global.serviceInfo.SharpCheckFileList }}
      SharpInitialDelaySeconds: {{ .Values.global.serviceInfo.SharpInitialDelaySeconds }}
      SharpCheckFileListRetryIntervalSecs: {{ .Values.global.serviceInfo.SharpCheckFileListRetryIntervalSecs }}
      SharpCheckFileListRetryTimes: {{ .Values.global.serviceInfo.SharpCheckFileListRetryTimes }}
      {{- else }}
      enableSharp: false
      {{- end }}
    opensm:
      smConfigFile: /etc/opensm/opensm.conf
      pkConfigFile: /etc/opensm/partitions.conf
      k8sNs: {{ .Values.global.namespace }}
      leaseLockName: "{{ .Values.global.fullname }}-{{ .Values.global.clusterName }}"
      configMapName: "partitions-{{ .Values.global.clusterName }}.conf"
      configMapKeyName: partitions.conf
      pNamePrefix: "pk_"
      enableOpensmLog: {{ .Values.global.serviceInfo.enableOpensmLog }}
      opensmLogRatelimitEnabled: {{ .Values.global.serviceInfo.opensmLogRatelimitEnabled }}
      {{- if .Values.global.serviceInfo.opensmLogRatelimitEnabled }}
      opensmLogRateLimitMinute: {{ .Values.global.serviceInfo.opensmLogRateLimitMinute }}
      {{- end }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonIBManager.labels" . | nindent 4 }}
---
apiVersion: v1
data:
  opensm.conf: |-
    m_key {{ .Values.global.serviceInfo.opensm_m_key }}
    m_key_protection_level 1
    m_key_lookup true
    sm_key {{ .Values.global.serviceInfo.opensm_m_key}}
    allow_both_pkeys true
    sm_priority {{ .Values.global.serviceInfo.sm_priority }}
    master_sm_priority {{ .Values.global.serviceInfo.master_sm_priority }}

    {{- if .Values.global.serviceInfo.opensm_ar }}
    routing_engine {{ .Values.global.serviceInfo.opensm_ar.routing_engine }}
    root_guid_file {{ .Values.global.serviceInfo.opensm_ar.root_guid_file }}
    scatter_ports {{ .Values.global.serviceInfo.opensm_ar.scatter_ports }}
    use_ucast_cache {{ .Values.global.serviceInfo.opensm_ar.use_ucast_cache}}

    # Number of threads to be used for parallel minhop/updn/dor/dfp/AR calculations.
    # If 0, the number of threads will be equal to the number of processors.
    # Default: 0x1，Recommended value is 0x0 (all cores)
    routing_threads_num {{ .Values.global.serviceInfo.opensm_ar.routing_threads_num }}

    # AR enablement in the switches
    # 0 – AR disabled
    # 1 – enable AR
    # 2 – enable AR with notifications
    # 3 – Default, auto (SM will apply supported mode automatically based on routing engine capabilities)
    ar_mode {{ .Values.global.serviceInfo.opensm_ar.ar_mode }}

    # SHIELD mode options
    # 0 – SHIELD disabled
    # 1 – enable SHIELD
    # 2 – enable SHIELD with notifications
    # 3 - Default, auto (SHIELD support is determined by the routing engine capabilities)
    shield_mode {{ .Values.global.serviceInfo.opensm_ar.shield_mode }}

    # Bitmask of service levels (SLs) on which the AR is enabled. Least Significant Bit indicates SL0, etc. | Default: 0xFFFF
    ar_sl_mask {{ .Values.global.serviceInfo.opensm_ar.ar_sl_mask }}

    # none – no advanced routing
    # ar_lag – port groups are created out of "parallel" links. Links that connect the same pair of switches.
    # ar_tree – all the ports with minimal hops to destination are in the same group. Must run together with UPDN/FTREE routing engines.
    # auto – Default, the AR engine is selected based on routing engine. Works for ar_updn, ar_ftree, ar_torus, ar_dor engines.
    adv_routing_engine {{ .Values.global.serviceInfo.opensm_ar.adv_routing_engine }}

    # AR Transport mask - indicates which transport types are enabled
    # Bit 0 = UD; Bit 1 = RC; Bit 2 = UC; Bit 3 = DCT; Bits 4-7 are reserved; Default: 0xA
    ar_transport_mask {{ .Values.global.serviceInfo.opensm_ar.ar_transport_mask }}

    # Enable adaptive routing only for devices supporting packet reordering.
    # TRUE: Default, Set only static ARLFT entries for LIDs of the HCAs which do not support packet reordering.
    # FALSE: ARLFT remains as determined by the routing engine.
    enable_ar_by_device_cap {{ .Values.global.serviceInfo.opensm_ar.enable_ar_by_device_cap }}
    {{- end }}

kind: ConfigMap
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-opensm-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonIBManager.labels" . | nindent 4 }}
{{- end -}}
