apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-disallow-capabilities
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-capabilities
    ruleNames:
    - adding-capabilities
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-host-namespaces
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-host-path
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-host-ports
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-host-ports
    ruleNames:
    - host-ports-none
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-*
        namespaces:
        - {{ .Values.global.namespace }}
---
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-privileged-containers
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.shortname }}-{{ .Values.global.clusterName }}-*
        namespaces:
        - {{ .Values.global.namespace }}
