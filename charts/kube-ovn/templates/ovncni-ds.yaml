kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: kube-ovn-cni
  namespace: kube-system
  annotations:
    kubernetes.io/description: |
      This daemon set launches the kube-ovn cni daemon.
spec:
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 10
    type: RollingUpdate
  selector:
    matchLabels:
      app: kube-ovn-cni
  template:
    metadata:
      labels:
        app: kube-ovn-cni
        component: network
        type: infra
    spec:
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
      priorityClassName: system-cluster-critical
      serviceAccountName: ovn
      hostNetwork: true
      hostPID: true
      initContainers:
      - name: install-cni
        image: {{ .Values.global.registry.address }}/{{ .Values.global.images.kubeovn.repository }}:{{ .Values.global.images.kubeovn.cni_tag }}
        imagePullPolicy: {{ .Values.global.registry.pullPolicy }}
        command: ["/kube-ovn/install-cni.sh"]
        securityContext:
          runAsUser: 0
          privileged: true
        volumeMounts:
          - mountPath: /opt/cni/bin
            name: cni-bin
      containers:
      - name: cni-server
        image: {{ .Values.global.registry.address }}/{{ .Values.global.images.kubeovn.repository }}:{{ .Values.global.images.kubeovn.cni_tag }}
        imagePullPolicy: {{ .Values.global.registry.pullPolicy }}
        command:
          - bash
          - /kube-ovn/start-cniserver.sh
        args:
          - --enable-mirror={{- .Values.debug.ENABLE_MIRROR }}
          - --encap-checksum=true
          - --service-cluster-ip-range=
          {{- if eq .Values.networking.net_stack "dual_stack" -}}
          {{ .Values.dual_stack.SVC_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv4" -}}
          {{ .Values.ipv4.SVC_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv6" -}}
          {{ .Values.ipv6.SVC_CIDR }}
          {{- end }}
          {{- if eq .Values.networking.network_type "vlan" }}
          - --iface=
          {{- else}}
          - --iface={{- .Values.networking.IFACE }}
          {{- end }}
          - --network-type={{- .Values.networking.network_type }}
          - --default-interface-name={{- .Values.networking.vlan.VLAN_INTERFACE_NAME }}
          - --logtostderr=false
          - --alsologtostderr=true
          - --log_file=/var/log/kube-ovn/kube-ovn-cni.log
        securityContext:
          runAsUser: 0
          privileged: true
        env:
          - name: ENABLE_SSL
            value: "{{ .Values.networking.enable_ssl }}"
          - name: POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIP
          - name: KUBE_NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          - name: POD_IPS
            valueFrom:
              fieldRef:
                fieldPath: status.podIPs
        volumeMounts:
          - name: host-modules
            mountPath: /lib/modules
            readOnly: true
          - mountPath: /etc/openvswitch
            name: systemid
          - mountPath: /etc/cni/net.d
            name: cni-conf
          - mountPath: /run/openvswitch
            name: host-run-ovs
          - mountPath: /run/ovn
            name: host-run-ovn
          - mountPath: /var/run/netns
            name: host-ns
            mountPropagation: HostToContainer
          - mountPath: /var/log/kube-ovn
            name: kube-ovn-log
          - mountPath: /etc/localtime
            name: localtime
        readinessProbe:
          failureThreshold: 5
          initialDelaySeconds: 30
          periodSeconds: 7
          successThreshold: 1
          timeoutSeconds: 5
          exec:
            command:
            - nc
            - -z
            - -w3
            - 127.0.0.1
            - "10665"
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 7
          successThreshold: 1
          timeoutSeconds: 3
          exec:
            command:
            - nc
            - -z
            - -w3
            - -U
            - /run/openvswitch/kube-ovn-daemon.sock
        resources:
          requests:
            cpu: 100m
            memory: 100Mi
          limits:
            cpu: 1000m
            memory: 1Gi
      nodeSelector:
        kubernetes.io/os: "linux"
      volumes:
        - name: host-modules
          hostPath:
            path: /lib/modules
        - name: systemid
          hostPath:
            path: /etc/origin/openvswitch
        - name: host-run-ovs
          hostPath:
            path: /run/openvswitch
        - name: host-run-ovn
          hostPath:
            path: /run/ovn
        - name: cni-conf
          hostPath:
            path: {{ .Values.cni_conf.CNI_CONF_DIR }}
        - name: cni-bin
          hostPath:
            path: {{ .Values.cni_conf.CNI_BIN_DIR }}
        - name: host-ns
          hostPath:
            path: /var/run/netns
        - name: kube-ovn-log
          hostPath:
            path: /var/log/kube-ovn
        - name: localtime
          hostPath:
            path: /etc/localtime
