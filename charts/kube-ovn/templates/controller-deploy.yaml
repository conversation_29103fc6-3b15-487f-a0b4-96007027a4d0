kind: Deployment
apiVersion: apps/v1
metadata:
  name: kube-ovn-controller
  namespace: kube-system
  annotations:
    kubernetes.io/description: |
      kube-ovn controller
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: kube-ovn-controller
  strategy:
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 100%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: kube-ovn-controller
        component: network
        type: infra
    spec:
      tolerations:
        - key: diamond.sensetime.com/role-k8s-master
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
        - key: diamond.sensetime.com/belong-resource-prp
          operator: Exists
        - effect: NoExecute
          key: node.kubernetes.io/not-ready
          operator: Exists
          tolerationSeconds: 300
        - effect: NoExecute
          key: node.kubernetes.io/unreachable
          operator: Exists
          tolerationSeconds: 300
        - effect: NoSchedule
          key: node.kubernetes.io/memory-pressure
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: kube-ovn-controller
              topologyKey: kubernetes.io/hostname
      priorityClassName: system-cluster-critical
      serviceAccountName: ovn
      hostNetwork: true
      containers:
        - name: kube-ovn-controller
          image: {{ .Values.global.registry.address }}/{{ .Values.global.images.kubeovn.repository }}:{{ .Values.global.images.kubeovn.controller_tag }}
          imagePullPolicy: {{ .Values.global.registry.pullPolicy }}
          args:
          - /kube-ovn/start-controller.sh
          - --default-cidr=
          {{- if eq .Values.networking.net_stack "dual_stack" -}}
          {{ .Values.dual_stack.POD_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv4" -}}
          {{ .Values.ipv4.POD_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv6" -}}
          {{ .Values.ipv6.POD_CIDR }}
          {{- end }}
          - --default-gateway=
          {{- if eq .Values.networking.net_stack "dual_stack" -}}
          {{ .Values.dual_stack.POD_GATEWAY }}
          {{- else if eq .Values.networking.net_stack "ipv4" -}}
          {{ .Values.ipv4.POD_GATEWAY }}
          {{- else if eq .Values.networking.net_stack "ipv6" -}}
          {{ .Values.ipv6.POD_GATEWAY }}
          {{- end }}
          - --default-gateway-check={{- .Values.cni_conf.CHECK_GATEWAY }}
          - --default-logical-gateway={{- .Values.cni_conf.LOGICAL_GATEWAY }}
          - --default-exclude-ips={{- .Values.networking.EXCLUDE_IPS }}
          - --node-switch-cidr=
          {{- if eq .Values.networking.net_stack "dual_stack" -}}
          {{ .Values.dual_stack.JOIN_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv4" -}}
          {{ .Values.ipv4.JOIN_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv6" -}}
          {{ .Values.ipv6.JOIN_CIDR }}
          {{- end }}
          - --service-cluster-ip-range=
          {{- if eq .Values.networking.net_stack "dual_stack" -}}
          {{ .Values.dual_stack.SVC_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv4" -}}
          {{ .Values.ipv4.SVC_CIDR }}
          {{- else if eq .Values.networking.net_stack "ipv6" -}}
          {{ .Values.ipv6.SVC_CIDR }}
          {{- end }}
          - --network-type={{- .Values.networking.network_type }}
          - --default-interface-name={{- .Values.networking.vlan.VLAN_INTERFACE_NAME }}
          - --default-vlan-id={{- .Values.networking.vlan.VLAN_ID }}
          - --pod-nic-type={{- .Values.networking.POD_NIC_TYPE }}
          - --enable-lb={{- .Values.func.ENABLE_LB }}
          - --enable-np={{- .Values.func.ENABLE_NP }}
          - --enable-external-vpc={{- .Values.func.ENABLE_EXTERNAL_VPC }}
          - --logtostderr=false
          - --alsologtostderr=true
          - --log_file=/var/log/kube-ovn/kube-ovn-controller.log
          - --keep-vm-ip={{- .Values.func.ENABLE_KEEP_VM_IP }}
          env:
            - name: ENABLE_SSL
              value: "{{ .Values.networking.enable_ssl }}"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: KUBE_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: KUBE_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: OVN_DB_IPS
              value: "{{ .Values.cni_conf.MASTER_NODES }}"
            - name: POD_IPS
              valueFrom:
                fieldRef:
                  fieldPath: status.podIPs
            - name: ENABLE_BIND_LOCAL_IP
              value: "{{- .Values.func.ENABLE_BIND_LOCAL_IP }}"
          volumeMounts:
            - mountPath: /etc/localtime
              name: localtime
            - mountPath: /var/log/kube-ovn
              name: kube-ovn-log
            - mountPath: /var/run/tls
              name: kube-ovn-tls
          readinessProbe:
            exec:
              command:
                - /kube-ovn/kube-ovn-controller-healthcheck
            periodSeconds: 3
            timeoutSeconds: 45
          livenessProbe:
            exec:
              command:
                - /kube-ovn/kube-ovn-controller-healthcheck
            initialDelaySeconds: 300
            periodSeconds: 7
            failureThreshold: 5
            timeoutSeconds: 45
          resources:
            requests:
              cpu: 1000m
              memory: 1Gi
            limits:
              cpu: 4000m
              memory: 4Gi
      nodeSelector:
        kubernetes.io/os: "linux"
      volumes:
        - name: localtime
          hostPath:
            path: /etc/localtime
        - name: kube-ovn-log
          hostPath:
            path: /var/log/kube-ovn
        - name: kube-ovn-tls
          secret:
            optional: true
            secretName: kube-ovn-tls
