apiVersion: v1
kind: ConfigMap
metadata:
  name: ovn-ic-config
  namespace: kube-system
data:
  auto-route: "false"
  az-name: {{ .Values.ic.az_name }}
  default-vpc-ic: {{ .Values.ic.default_vpc_ic }}
  enable-ic: {{ .Values.ic.enable_ic }}
  gw-nodes: {{ .Values.ic.gw_nodes }}
  ic-db-host: {{ .Values.ic.ic_db_host }}
  ic-nb-port: {{ .Values.ic.ic_nb_port }}
  ic-sb-port: {{ .Values.ic.ic_sb_port }}
