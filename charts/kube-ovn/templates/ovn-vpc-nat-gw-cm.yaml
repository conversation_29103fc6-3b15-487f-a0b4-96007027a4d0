apiVersion: v1
kind: ConfigMap
metadata:
  name: ovn-vpc-nat-gw-config
  namespace: kube-system
data:
  affinity: |-
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: diamond.sensetime.com/role-infra-vpc-gw
            operator: In
            values:
            - enabled
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: ovn.kubernetes.io/vpc-nat-gw
              operator: Exists
          topologyKey: kubernetes.io/hostname
        weight: 100
  enable-vpc-nat-gw: "true"
  enable-gw-dp-update: {{ .Values.natgw.auto_update | quote }}
  image: {{ .Values.global.registry.address }}/{{ .Values.global.images.natgw.repository }}:{{ .Values.global.images.natgw.tag }}
{{-  if .Values.global.registry.imagePullSecrets }}
{{- range $index, $secret := .Values.global.registry.imagePullSecrets }}
{{- if $secret }}
  image-pull-secret: {{ $secret | quote }}
{{- end }}
{{- end }}
{{- end }}
  nic: {{ .Values.natgw.nic }}
