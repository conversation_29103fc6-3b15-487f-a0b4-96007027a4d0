# Kube-OVN-helm

## Pull helm chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/kubeovn --version x.y.z

tar -xzvf kubeovn-x.y.z.tgz
```

## Upgrade existed cluster

```bash
k label sa ovn app.kubernetes.io/managed-by=Helm -n kube-system
k annotate sa ovn meta.helm.sh/release-name=kubeovn -n kube-system
k annotate sa ovn meta.helm.sh/release-namespace=default -n kube-system

k label clusterrole system:ovn app.kubernetes.io/managed-by=Helm
k annotate clusterrole system:ovn meta.helm.sh/release-name=kubeovn
k annotate clusterrole system:ovn meta.helm.sh/release-namespace=default

k label clusterrolebinding ovn app.kubernetes.io/managed-by=Helm
k annotate clusterrolebinding ovn meta.helm.sh/release-name=kubeovn
k annotate clusterrolebinding ovn meta.helm.sh/release-namespace=default

for svc in kube-ovn-controller kube-ovn-monitor ovn-nb ovn-sb ovn-northd kube-ovn-cni kube-ovn-pinger
do
  k label svc $svc app.kubernetes.io/managed-by=Helm -n kube-system
  k annotate svc $svc meta.helm.sh/release-name=kubeovn -n kube-system
  k annotate svc $svc meta.helm.sh/release-namespace=default -n kube-system
done

for ds in kube-ovn-cni ovs-ovn kube-ovn-pinger
do
  k label ds $ds app.kubernetes.io/managed-by=Helm -n kube-system
  k annotate ds $ds meta.helm.sh/release-name=kubeovn -n kube-system
  k annotate ds $ds meta.helm.sh/release-namespace=default -n kube-system
done

for deploy in ovn-central kube-ovn-controller kube-ovn-monitor
do
  k label deploy $deploy app.kubernetes.io/managed-by=Helm -n kube-system
  k annotate deploy $deploy meta.helm.sh/release-name=kubeovn -n kube-system
  k annotate deploy $deploy meta.helm.sh/release-namespace=default -n kube-system
done

for cm in ovn-config ovn-ic-config ovn-nat-gw-config

k label cm ovn-config app.kubernetes.io/managed-by=Helm -n kube-system
k annotate cm ovn-config meta.helm.sh/release-name=kubeovn -n kube-system
k annotate cm ovn-config meta.helm.sh/release-namespace=default -n kube-system
```

```bash
k apply -f kubeovn/crd.yaml
helm install kubeovn kubeovn -f kubeovn/values.yaml
```

## New Installation

label nodes on new cluster:

```bash
kubectl label no -lbeta.kubernetes.io/os=linux kubernetes.io/os=linux --overwrite
kubectl label no -lnode-role.kubernetes.io/control-plane kube-ovn/role=master --overwrite
```

```bash
k apply -f kubeovn/crd.yaml
helm install kubeovn kubeovn -f kubeovn/values.yaml
# helm install kubeovn -f kubeovn/values.yaml --set cni_conf.MASTER_NODES=${Node0},${Node1},${Node2},
```

## Uninstall

```bash
helm uninstall kubeovn
```
