# Default values for kubeovn.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  registry:
    address: registry.sensetime.com/sensecore-boson/kube-ovn
    imagePullSecrets: [ "sensecore-boson" ]
    pullPolicy: IfNotPresent
  images:
    kubeovn:
      repository: kube-ovn
      db_tag: v1.9.3.14-20240429181732
      monitor_tag: v1.9.3.14-20240429181732
      controller_tag: v1.9.3.14-20240429181732
      pinger_tag: v1.9.3.14-20240429181732
      cni_tag: v1.9.3.14-20240429181732
      ovs_tag: v1.9.3.14-20240429181732
    natgw:
      repository: vpc-nat-gateway
      tag: v1.9.3.14-20240126171050

natgw:
  nic: bond0
  auto_update: "false"

ic:
  az_name: "az-xxx"
  default_vpc_ic: "false"
  enable_ic: "true"
  gw_nodes: "host-1,host-2,host-3,"
  ic_db_host: "ic-host-1,ic-host-2,ic-host-3,"
  ic_nb_port: "6645"
  ic_sb_port: "6646"

replicaCount: 3

networking:
  # net_stack could be dual_stack, ipv4, ipv6
  net_stack: ipv4
  enable_ssl: false
  # network type could be geneve or vlan
  network_type: geneve
  # tunnel type could be geneve, vxlan or stt
  tunnel_type: geneve
  # geneve underlay iface
  IFACE: ""
  DPDK_TUNNEL_IFACE: "br-phy"
  EXCLUDE_IPS: "**********,*************..*************"
  POD_NIC_TYPE: "veth-pair"
  vlan: # not used
    VLAN_INTERFACE_NAME: ""
    VLAN_NAME: "ovn-vlan"
    VLAN_ID: "100"

func:
  ENABLE_LB: true
  ENABLE_NP: true
  ENABLE_EIP_SNAT: true
  ENABLE_EXTERNAL_VPC: true
  HW_OFFLOAD: false
  ENABLE_LB_SVC: false
  ENABLE_KEEP_VM_IP: true
  # ENABLE_BIND_LOCAL_IP: true

ipv4:
  POD_CIDR: "**********/19"
  POD_GATEWAY: "**********"
  SVC_CIDR: "*********/12"
  JOIN_CIDR: "**********/16"
  PINGER_EXTERNAL_ADDRESS: "*********"
  PINGER_EXTERNAL_DOMAIN: "alauda.cn."

ipv6:
  POD_CIDR: "fd00:10:16::/64"
  POD_GATEWAY: "fd00:10:16::1"
  SVC_CIDR: "fd00:10:96::/112"
  JOIN_CIDR: "fd00:100:64::/64"
  PINGER_EXTERNAL_ADDRESS: "2400:3200::1"
  PINGER_EXTERNAL_DOMAIN: "google.com."

dual_stack:
  POD_CIDR: "*********/16,fd00:10:16::/64"
  POD_GATEWAY: "*********,fd00:10:16::1"
  SVC_CIDR: "*********/12,fd00:10:96::/112"
  JOIN_CIDR: "**********/16,fd00:100:64::/64"
  PINGER_EXTERNAL_ADDRESS: "***************,2400:3200::1"
  PINGER_EXTERNAL_DOMAIN: "google.com."

debug:
  ENABLE_MIRROR: false

cni_conf:
  # ovn db ips
  MASTER_NODES: ""
  CHECK_GATEWAY: true
  LOGICAL_GATEWAY: false
  CNI_CONF_DIR: "/etc/cni/net.d"
  CNI_BIN_DIR: "/opt/cni/bin"
