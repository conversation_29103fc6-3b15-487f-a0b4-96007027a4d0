apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.bosonAssistant.fullname }}-service-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    {{- include "bosonAssistant.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.domainName }}
    http:
      paths:
      - path: /network/{{ .Values.global.bosonAssistant.fullname }}(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.bosonAssistant.fullname }}-service
            port:
              number: {{ int .Values.global.bosonAssistant.service.ports.http }}
  tls:
  - hosts:
    - {{ .Values.global.domainName }}
    secretName: {{ .Values.global.sslSecretName }}
