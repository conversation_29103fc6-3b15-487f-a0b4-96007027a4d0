apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonAssistant.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonAssistant.fullname }}
    {{- include "bosonAssistant.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonAssistant.fullname }}
  replicas: {{ int .Values.global.bosonAssistant.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonAssistant.fullname }}
    spec:
      serviceAccount: {{ .Values.global.bosonAssistant.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonAssistant.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonAssistant.fullname }}
        image: "{{ .Values.global.bosonAssistantImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - python3
        - api-server.py
        - --serve_mode
        - --url-base
        - {{ .Values.global.bosonAssistant.runtimeConfig.apiBase | quote }}
        ports:
        - name: http
          containerPort: {{ int .Values.global.bosonAssistant.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ int .Values.global.bosonAssistant.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ int .Values.global.bosonAssistant.service.ports.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonAssistant.cpuLimits }}
            memory: {{ .Values.global.bosonAssistant.memLimits }}
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: SERVICE_RUNTIME_CONFIG
          value: "{{ .Values.global.bosonAssistant.runtimeConfig.filename }}"
        - name: BOSON_ASSISTANT_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_ASSISTANT_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_ASSISTANT_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_ASSISTANT_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "{{ .Values.global.bosonAssistant.runtimeConfig.apiBase }}/health/check"
            port: {{ int .Values.global.bosonAssistant.service.ports.http }}
          periodSeconds: 600
          initialDelaySeconds: 10
        volumeMounts:
        - name: runtime-config
          mountPath: /root/boson-assistant/runtime-config.json
          subPath: path/to/runtime-config.json
        - name: boson-provider-config
          mountPath: /root/boson-assistant/config/k8s-boson-provider.yaml
          subPath: path/to/boson-provider.yaml
      volumes:
      - name: runtime-config
        configMap:
          name: {{ .Values.global.bosonAssistant.fullname }}-config
          items:
          - key: runtime-config.json
            path: path/to/runtime-config.json
      - name: boson-provider-config
        configMap:
          name: boson-provider-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
