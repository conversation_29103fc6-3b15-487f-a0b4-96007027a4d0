{{/*
Expand the name of the chart.
*/}}
{{- define "bosonAssistant.name" -}}
{{- default .Values.global.bosonAssistant.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonAssistant.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "bosonAssistant.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonAssistant.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "bosonAssistant.labels" -}}
helm.sh/chart: {{ include "bosonAssistant.chart" . }}
{{ include "bosonAssistant.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonAssistant.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}
