---
apiVersion: v1
data:
  runtime-config.json: |-
    {
        "env_vars": {
            "auth_base": "http://localhost:55080/api/v1",
            "region": {{ .Values.global.region | quote }},
            "zone": {{ .Values.global.zone | quote }},
            "prp": {{ .Values.global.prp | quote }},
            "mailing": {
                "enable": {{ .Values.global.smtp.enable }},
                "type": {{ .Values.global.smtp.type | quote }},
                "host": {{ .Values.global.smtp.host | quote }},
                "port": {{ int .Values.global.smtp.port }},
                "username": {{ .Values.global.smtp.username | quote }},
                "password": {{ .Values.global.smtp.password | quote }},
                "sender": {{ .Values.global.smtp.sender | quote }},
                "to": {{ .Values.global.bosonAssistant.runtimeConfig.env.to | quote }}
            }
        },
        "initial_delay_seconds": {{ int .Values.global.bosonAssistant.runtimeConfig.initialDelaySeconds }},
        "case_trigger_interval": {{ int .Values.global.bosonAssistant.runtimeConfig.caseTriggerInterval }},
        "test_case_config": "test_case_config.json"
    }
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonAssistant.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonAssistant.labels" . | nindent 4 }}
