apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonAssistant.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonAssistant.fullname }}-service
    {{- include "bosonAssistant.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.bosonAssistant.fullname }}
  ports:
  - name: http
    port: {{ int .Values.global.bosonAssistant.service.ports.http }}
    protocol: TCP
  - name: grpc
    port: {{ int .Values.global.bosonAssistant.service.ports.grpc }}
    protocol: TCP
  - name: metrics
    port: {{ int .Values.global.bosonAssistant.service.ports.metrics }}
    protocol: TCP
