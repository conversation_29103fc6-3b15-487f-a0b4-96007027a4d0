# Boson Assistant

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

Boson Assistant 作为 Boson 服务中旁路组件为 Boson 服务提供主逻辑之外的辅助功能，比如，定时查询 eip 池、VPC gateway ip 池使用量的 metrics，清理租户 vpc 下所有资。

## 准备工作

boson-assistant 组件需要部署到 Diamond TK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-assistant
# or pull specific chart version
# helm3 pull boson/boson-assistant --version x.y.z

tar -xzvf boson-assistant-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-assistant/values.yaml)
- [dev11](../../envs/dev11/boson-assistant/values.yaml)
- [dev44](../../envs/dev44/boson-assistant/values.yaml)
- [集成测试](../../envs/int-test/boson-assistant/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-assistant/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-assistant/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-assistant/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

## 服务部署

### 部署服务

```bash
helm3 install boson-assistant boson-assistant -n plat-boson-service -f path/to/env/boson-assistant/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-assistant boson-assistant -n plat-boson-service -f path/to/env/boson-assistant/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-assistant -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME               READY   UP-TO-DATE   AVAILABLE   AGE
# boson-assistant    1/1     1            1           6s
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-service get pod
# NAME                                READY   STATUS      RESTARTS   AGE
# boson-assistant-7b758897c7-9tj5c    1/1     Running     0          6s
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-assistant-7b758897c7-9tj5c
# 2022-12-15T08:13:44Z>WARNING:No SERVICE_POD_NAME env specified, used default service_pod_name: service_pod_name
# 2022-12-15T08:13:44Z>INFO:Start Service-Monitor for 'k8s'
# 2022-12-15T08:13:44Z>INFO:Initializing Service-Monitor handler at port 55080, metrics at port 55030, url path at /api/v1
# 2022-12-15T08:13:44Z>INFO:Http server started in thread: Thread-1, listening at 0.0.0.0:55080
# 2022-12-15T08:13:44Z>INFO:Listening Service-Monitor at 55080, metrics at 55030, url path at /api/v1
# 2022-12-15T08:13:44Z>INFO:Delay 60 seconds before start repeatable service testing.
# 2022-12-15T08:14:44Z>INFO:Start repeatable service testing in every 900 seconds.
```
