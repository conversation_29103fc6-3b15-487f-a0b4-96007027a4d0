
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: dev-xx
  region: cn-sh-01
  zone: cn-sh-01a
  prp: cn-sh-01a-prp01
  # bosonAssistantImage defines boson-assistant image and tag.
  bosonAssistantImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.13.0-1-g95b6de0-20240522210615
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName: network-internal.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  # smtp defines mail host settings for API-Monitor.
  smtp:
    # enable defines enable mail feature or not.
    enable: "true"
    # type defines mail client type when sending mail. Supported types are 'outlook' and 'sendgrid'.
    type: "outlook"
    # host defines mail host domain.
    host: "smtp.partner.outlook.cn"
    # port defines mail server port number.
    port: "587"
    # username defines account name of mail from on host.
    username: "<EMAIL>"
    # password defines account password of mail from on host.
    password: "xxx_send_from_password_xxx"
    # username defines sender address of mail from on host.
    sender: "<EMAIL>"
  # bosonAssistant variables
  bosonAssistant:
    # fullname defines name of boson-assistant and references every where.
    fullname: "boson-assistant"
    # replicas defines number of pods running in cluster.
    replicas: 1
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 300m
    # memLimits defines memory limit used by pods.
    memLimits: 300Mi
    # runtimeConfig defines runtime config in configmap for API-Monitor service.
    runtimeConfig:
      # apiBase defines url prefix of service
      apiBase: "/api/v1"
      # filename defines path to runtime-config.json
      filename: runtime-config.json
      env:
        # to defines monitor notification mail to receiptor to receive failure case notify or license expiry notify
        to: "<EMAIL>"
      # initialDelaySeconds defines the duration between pod start and case running.
      initialDelaySeconds: "15"
      # caseTriggerInterval defines the duration between 2 rounds of test case running.
      caseTriggerInterval: "900"
    # service defines ports for service.
    service:
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "55080"
        # grpc port number for container, service, ingress.
        grpc: "55090"
        # metrics port number for container, service, service monitor.
        metrics: "55030"
