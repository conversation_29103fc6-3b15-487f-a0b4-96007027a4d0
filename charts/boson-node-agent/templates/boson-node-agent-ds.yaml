apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ .Values.global.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.fullname }}
    {{- include "bosonNodeAgent.labels" . | nindent 4 }}
spec:
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: {{ .Values.global.serviceInfo.maxUnavailable }}
    type: RollingUpdate
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app-name: {{ .Values.global.fullname }}-service
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.fullname }}-service
    spec:
      hostNetwork: true
      hostPID: true
      hostIPC: true
      serviceAccountName: {{ .Values.global.fullname }}
      priorityClassName: system-cluster-critical
      affinity:
{{- with .Values.global.serviceInfo.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range $index, $nodeSelectorTerms := . }}
            - matchExpressions:
            {{- if $nodeSelectorTerms.matchExpressions }}
              {{- range $expressions := $nodeSelectorTerms.matchExpressions }}
              - key: {{ $expressions.key | quote }}
                operator: In
                values:
                {{- range $expressions.values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- else }}
              - key: {{ $nodeSelectorTerms.key | quote }}
                operator: In
                values:
                {{- range $nodeSelectorTerms.values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - operator: {{ .operator }}
        {{- if .effect }}
        effect: {{ .effect }}
        {{- end }}
        {{- if .key }}
        key: {{ .key | quote }}
        {{- end }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.serviceInfo.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.fullname }}
        image: "{{ .Values.global.serviceInfo.bosonNodeAgentImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-node-agent-daemon
        - -c
        - /boson/etc/daemon.yaml
        securityContext:
          runAsUser: 0
          privileged: true
        ports:
        - name: http
          containerPort: {{ .Values.global.serviceInfo.httpPort }}
          protocol: TCP
        - name: grpc
          containerPort: {{ .Values.global.serviceInfo.grpcPort }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.serviceInfo.cpuLimits | quote }}
            memory: {{ .Values.global.serviceInfo.memLimits }}
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: KUBE_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NODE_AGENT_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NODE_AGENT_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NODE_AGENT_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ .Values.global.serviceInfo.httpPort }}
          failureThreshold: 3
          initialDelaySeconds: {{ .Values.global.serviceInfo.initialDelaySeconds }}
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /readyz
            port: {{ .Values.global.serviceInfo.httpPort }}
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: openvswitch
          mountPath: /var/run/openvswitch
        - name: boson
          mountPath: /var/run/boson
        - name: replay-dir
          mountPath: /boson/replay-dir
        - name: daemon-config
          mountPath: /boson/etc/
        - mountPath: /sys
          name: host-sys
        - mountPath: /etc/localtime
          name: localtime
        - name: host-run-netns
          mountPath: /run/netns
          mountPropagation: Bidirectional
      volumes:
      - name: openvswitch
        hostPath:
          path: /var/run/openvswitch
      - name: boson
        hostPath:
          path: /var/run/boson
      - name: host-run-netns
        hostPath:
          path: /run/netns
      - hostPath:
          path: /sys
        name: host-sys
      - hostPath:
          path: /etc/localtime
        name: localtime
      - name: replay-dir
        hostPath:
          path: {{ .Values.global.serviceInfo.replayDirInHost }}
      - name: daemon-config
        configMap:
          name: {{ .Values.global.fullname }}-config
