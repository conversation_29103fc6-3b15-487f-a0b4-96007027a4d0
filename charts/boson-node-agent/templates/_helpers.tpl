{{/*
Expand the name of the chart.
*/}}
{{- define "bosonNodeAgent.name" -}}
{{- default .Values.global.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonNodeAgent.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "bosonNodeAgent.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonNodeAgent.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "bosonNodeAgent.labels" -}}
helm.sh/chart: {{ include "bosonNodeAgent.chart" . }}
{{ include "bosonNodeAgent.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}
