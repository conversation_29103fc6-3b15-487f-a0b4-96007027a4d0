apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.fullname }}-service
    {{- include "bosonNodeAgent.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.fullname }}-service
  ports:
  - name: http
    port: {{ .Values.global.serviceInfo.httpPort }}
    protocol: TCP
  - name: grpc
    port: {{ .Values.global.serviceInfo.grpcPort }}
    protocol: TCP
