# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-infra
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  # 注意node的selector和这里是对应的
  shortname: "boson-na"
  fullname: "boson-node-agent"
  serviceInfo:
    region: "cn-sh-01"
    az: "cn-sh-01a"
    prp: "cn-sh-01a-prp01"
    httpPort: 55800
    grpcPort: 55900
    dryRun: False
    enablePprof: True
    independentNetns: false
    initialDelaySeconds: 60
    dataIpCidr: "**********/16"
    dgwBrName: br-data
    # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
    envName: devxx
    # 从镜像站下载镜像的账号，需要提前创建docker pull secret
    imagePullSecret: sensecore-boson
    bosonNodeAgentImage: registry.sensetime.com/sensecore-boson/boson-node-agent:v0.0.5-dev
    log_level: info
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 1
    # memLimits defines memory limit used by pods.
    memLimits: 1Gi
    replayDirInPod: /boson/replay-dir
    replayDirInHost: /var/lib/boson/boson-node-agent/replay-dir
    # used in rolling update
    maxUnavailable: 5
    # nodeSelectorTerms 定义服务部署所需要的node label标识
    nodeSelectorTerms:
    tolerations:
    - effect: NoSchedule
      operator: Exists
    - effect: NoExecute
      operator: Exists
    - key: CriticalAddonsOnly
      operator: Exists
