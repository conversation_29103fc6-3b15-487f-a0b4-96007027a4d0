# Since CoreDNS chart is originate from kubespray
# Lots of values should inherited from there
# This helm chart is created from kubespray at
# https://github.com/kubernetes-sigs/kubespray/tree/v2.26.0/roles/kubernetes-apps/ansible/
#
# CoreDNS Values
#
# deployment
coredns_image_repo: "registry.sensetime.com/sensecore/infra/coredns/coredns"
coredns_image_tag: "v1.11.3"
coredns_ordinal_suffix: ""
k8s_image_pull_policy: IfNotPresent
imagePullSecret: sensecore-boson
dns_extra_tolerations:
- effect: NoSchedule
  operator: "Exists"
coredns_deployment_nodeselector:
  kubernetes.io/os: linux
# resource limits
dns_cpu_limit: 2
dns_memory_limit: 512Mi
dns_cpu_requests: 100m
dns_memory_requests: 256Mi
# Ip address of the service ip
skydns_server: "**********"
#
# kubernetes plugin
enable_coredns_reverse_dns_lookups: true
dns_domain: "cluster.local"
# Apply extra options to coredns kubernetes plugin
coredns_kubernetes_extra_opts:
- 'ttl 30'
# Enable endpoint_pod_names option for kubernetes plugin
enable_coredns_k8s_endpoint_pod_names: false
#
# forward plugin
upstream_dns_servers:
- /etc/resolv.conf
dns_upstream_forward_max_concurrent: 2000
# dns_upstream_forward_extra_opts apply to coredns forward section as well as nodelocaldns upstream target forward section
# dns_upstream_forward_extra_opts:
#   policy: sequential
#
# file plugins
coredns_file_opts:
  ttl: 1h
  SOA:
    mname: kube-dns.kube-system.svc.cluster.local.
    rname: hostmaster
    refresh: 1d
    retry: 2h
    expire: 4w
    minimum_ttl: 1h
#
# cache plugin
coredns_default_zone_cache_block: |
  cache 30 {
      prefetch 5 30m 10%
      serve_stale 24h immediate
  }
#
# k8s_external plugin
enable_coredns_k8s_external: false
coredns_k8s_external_zone: k8s_external.local
#
# coredns_additional_configs adds any extra configuration to coredns
# coredns_additional_configs: |
#   whoami
#   local
#
# rewrite plugin
# coredns_rewrite_block: |
#   rewrite stop {
#     name regex (.*)\.my\.domain {1}.svc.cluster.local
#     answer name (.*)\.svc\.cluster\.local {1}.my.domain
#   }
# Configure coredns and nodelocaldns to correctly answer DNS queries when you changed
# your 'dns_domain' and some workloads used it directly.
old_dns_domains: []
#
# errors plugin
# coredns_additional_error_config: |
#   consolidate 5m ".* i/o timeout$" warning
#
# CoreDNS Pods Disruption Budget Values
#
coredns_pod_disruption_budget: false
# value for coredns pdb
coredns_pod_disruption_budget_max_unavailable: "30%"
#
# DNS Auto Scaler Values
#
# Limits for dns-autoscaler
enable_dns_autoscaler: false
dns_min_replicas: 3
dns_nodes_per_replica: 16
dns_cores_per_replica: 256
dns_prevent_single_point_failure: true
dns_autoscaler_cpu_requests: 20m
dns_autoscaler_memory_requests: 10Mi
dns_autoscaler_deployment_nodeselector:
  kubernetes.io/os: linux
dns_autoscaler_extra_tolerations:
- effect: NoSchedule
  operator: "Exists"
dnsautoscaler_image_repo: "registry.sensetime.com/sensecore/infra/cpa/cluster-proportional-autoscaler"
dnsautoscaler_image_tag: "v1.8.8"
