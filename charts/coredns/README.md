# CoreDNS 配置

<!-- TOC -->

- [CoreDNS 域名解析配置](#coredns-域名解析配置)
  - [CoreDNS 的配置文件](#coredns-的配置文件)
  - [CoreDNS 配置更新逻辑](#coredns-配置更新逻辑)
- [非 Helm 部署的 CoreDNS 变更逻辑](#非-helm-部署的-coredns-变更逻辑)
  - [Deployment 的 YAML 调整（一次性变更）](#deployment-的-yaml-调整一次性变更)
    - [Deployment 的变更](#deployment-的变更)
    - [Deployment 的回滚](#deployment-的回滚)
  - [CoreDNS 配置变更部署](#coredns-配置变更部署)
    - [Configmap 的变更](#configmap-的变更)
    - [Configmap 变更的验证](#configmap-变更的验证)
    - [Configmap 的回滚](#configmap-的回滚)
- [Helm 部署的 CoreDNS 变更逻辑](#helm-部署的-coredns-变更逻辑)
  - [部署的前提条件](#部署的前提条件)
  - [拉取服务 Helm Chart](#拉取服务-helm-chart)
  - [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
  - [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)
- [典型的配置文件](#典型的配置文件)
  - [Corefile 文件内容](#corefile-文件内容)
  - [Zone File 的内容](#zone-file-的内容)
  - [Kubespray 的 DNS 功能](#kubespray-的-dns-功能)
  - [查询集群规模](#查询集群规模)
  - [链接](#链接)

<!-- /TOC -->

## CoreDNS 域名解析配置

### CoreDNS 的配置文件

CoreDNS 的域名解析配置主要是针对不同的域名，返回配置文件中指定的 IP 地址列表。因此，更新域名解析的核心动作就是更新 CoreDNS 的配置文件。

域名解析的配置文件，分为两个部分，如下。解析配置文件（zone file）会以 **二级域名** 为边界分别保存在不同的域名解析文件中，以便在 Corefile 中引用。

1. 在 Corefile 中配置的 file 插件指向的解析配置文件

    ```plaintext
    # file "配置文件路径" "空格分隔的域名列表"
    file /etc/coredns/sensecoreapi.cn.db afs-internal-forward.cn-sh-01e-ec01.sensecoreapi.cn
    ```

2. 解析配置文件中，域名和 IP 的对应关系

    ```plaintext
    aoss-internal.cn-sh-01                  IN  A       **************
    *.aoss-internal.cn-sh-01                IN  A       **************
    ```

### CoreDNS 配置更新逻辑

Corefile 是 CoreDNS 的主配置文件。在 Corefile 中如果包含了 [`reload`](https://coredns.io/plugins/reload/) 模块，默认就会定时（30s±15s）自动加载这个配置文件，检查变更应用配置。如果 Corefile 中的内容更新，也会连带直接更新 file 模块中的内容。

插件 [file](https://coredns.io/plugins/file/) 也有默认自动更新（60s）的功能，但是只有当 zone file 配置里面的 `serial` 变化才会加载配置，否则不论 file 内容是否修改，都不会重新加载。在脚本生产 zone file 配置的时候，会根据需要配置的域名和 IPs 信息计算一个 hash 值，并将 hash 值转换成 [`uint32`](https://github.com/coredns/coredns/blob/master/plugin/file/file.go#L145) 的 `serial`，这样保证只要配置内容发生变化，就会自动生成不同的 `serial` 值。

## 非 Helm 部署的 CoreDNS 变更逻辑

非 Helm 部署的 CoreDNS 无法直接使用 Helm Chart 进行更新，需要删除现有的组件后，由 Helm Chart 部署第一版之后才可以由 Chart 更新。但是 CoreDNS 属于集群关键服务，无法卸载重新部署，因此，此段方法用于非 Helm 部署的 CoreDNS 集群。

### Deployment 的 YAML 调整（一次性变更）

`coredns_deploy_change.sh`：更新、备份、回滚 CoreDNS 的 Deployment volume 下的 `items`。

默认部署的 CoreDNS，在 volume 会下将 configmap 中的指定 `key` 的文件 mount 到 POD 当中。但由于增删域名解析配置，通常需要增减文件。为了降低更新配置的麻烦，需要删除 volume 下面 `items` 的 `key` 配置，这样 configmap 中包含的所有文件，都会默认 mount 到 POD 当中。

脚本属于一次性配置脚本，一旦删除了 `items` 下面的 `key` 配置，就无需多次运行了。

#### Deployment 的变更

```bash
# 变更前检查 Deployment 的配置，其中 oss.db 是否存在视实际集群配置而定。
# 如果能看到输出的 {"key":"Corefile","path":"Corefile"} 说明需要运行 coredns_deploy_change.sh
# 如果看不到，则说明当前CoreDNS已经完成Deployment的变更，以后该AZ无需再次重复这个步骤
$ kubectl get deployment coredns -n kube-system -o jsonpath="{.spec.template.spec.volumes}"
[{"configMap":{"defaultMode":420,"items":[{"key":"Corefile","path":"Corefile"},{"key":"oss.db","path":"oss.db"}],"name":"coredns"},"name":"config-volume"}]

# 备份当前 Deployment 的 volume 中 items 的配置，并删除 items
# 这个脚本是可重入的，无论当前 Deployment 中 items 是否已经删除，都可以运行，并在脚本输出中提示状态
# 最终目标都是移除 Deployment 中的 items 配置
$ bash coredns_deploy_change.sh remove

# 检查 Deployment 变更结果
$ kubectl get deployment coredns -n kube-system -o jsonpath="{.spec.template.spec.volumes}"
[{"configMap":{"defaultMode":420,"name":"coredns"},"name":"config-volume"}]
```

#### Deployment 的回滚

```bash
# 检查备份文件
$ ls coredns-deploy-volume-*-backup.json
coredns-deploy-volume-2025-02-21_12-45-37-backup.json

# 回滚配置，应用之前备份的文件的 Deployment items
# 回滚的前提是前面的变更确实在 Deployment 中还有 items 配置的情况下做的备份。
# 如果已经是第二次运行 coredns_deploy_change.sh remove 脚本，备份文件中也不会包含 items 内容
# 即便操作回滚，也无法还原 items 内容。需要使用第一次运行删除配置时保存的备份文件回滚才行。
$ bash coredns_deploy_change.sh restore coredns-deploy-volume-2025-02-21_12-45-37-backup.json
```

### CoreDNS 配置变更部署

#### Configmap 的变更

K8S 部署中，所有的配置文件，都放在 `kube-system` 的 `coredns` configmap 当中。解析配置的变更就是更新 `coredns` 的configmap。`coredns_cm_change.sh`：更新、备份、回滚 CoreDNS 的 configmap 配置文件。

```bash
# 备份当前 coredns 的 configmap，并更新 configmap 内容
$ bash coredns_cm_change.sh update
```

#### Configmap 变更的验证

`nslookup_domain_check.sh` 脚本中包含与配置域名相关的域名解析验证逻辑。脚本使用当前集群的 `kube-dns` (SenesCore 1.0) 或者 `coredns` (SenseCore 2.0) 的 ClusterIP 作为默认容器 DNS 解析服务器，以便兼容不同版本集群和不同 ClusterIP 配置的情况。如果在集群外或者租户 VPC 内运行，需要改为集群对应的 default-vpc-gateway Underlay IP 的服务器上运行，如 cn-sh-01a 区使用 ************ 作为 `core_dns_servers` 的 IP 配置。此项需要人工更改，但需要确认修改的域名服务器IP具备当前脚本配置的特殊域名解析内容。

这个脚本验证两类内容

- 第一类是环境不变的**容器侧域名**解析，**大装置域名**解析以及**公网某个域名**解析
- 第二部分就是根据 CoreDNS 的特殊配置生成的**所有特殊的域名**解析

```bash
# 运行检查脚本，看输出中是否存在 “CHECK FAIL” 的报错
$ bash nslookup_domain_check.sh
```

#### Configmap 的回滚

在运行 Configmap 更新时，脚本会自动对当前 CoreDNS 的配置进行备份。当需要回滚操作时，使用备份的配置文件回滚即可。

```bash
$ ls coredns-cm-*-backup.yaml
coredns-cm-2025-02-21_12-47-26-backup.yaml

# 回滚配置，应用之前的备份文件的 configmap
$ bash coredns_cm_change.sh restore coredns-cm-2025-02-21_12-47-26-backup.yaml
```

## Helm 部署的 CoreDNS 变更逻辑

### 部署的前提条件

- CoreDNS 服务作为 k8s 的核心组件需要部署到 namespace `kube-system` 中。
- CoreDNS 服务的相关镜像需要保存在 public 仓，需要按实际情况配置`imagePullSecrets`。
- Helm Chart 仓需要提前配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list
helm3 repo update

# pull latest chart version
helm3 pull boson/coredns
# or pull specific chart version
# helm3 pull boson/coredns --version x.y.z

tar -xzvf coredns-x.y.z.tgz
```

### 服务部署

#### 部署服务

```bash
helm3 install coredns coredns -n kube-system -f path/to/env/coredns/values.yaml
```

#### 更新服务

```bash
helm3 upgrade coredns coredns -n kube-system -f path/to/env/coredns/values.yaml
```

#### 卸载服务

```bash
helm3 uninstall coredns -n kube-system
```

### 检查服务状态

#### 查看 deployment

```bash
$ kubectl -n kube-system get deployment coredns
NAME      READY   UP-TO-DATE   AVAILABLE   AGE
coredns   3/3     3            3           4d
```

#### 查看运行的Pod

```bash
$ kubectl -n kube-system get pods -l k8s-app=kube-dns
NAME                       READY   STATUS    RESTARTS   AGE
coredns-7cf8fbd8d7-8rqz6   1/1     Running   0          4d
coredns-7cf8fbd8d7-k4j9r   1/1     Running   0          4d
coredns-7cf8fbd8d7-q6lsb   1/1     Running   0          4d
```

#### 检查服务启动日志

```bash
$ kubectl -n kube-system logs coredns-7cf8fbd8d7-8rqz6
.:53
[INFO] plugin/reload: Running configuration SHA512 = 0825f01f7e4d969aa0af933b395ab3fe7b89484b0299dae46db94508e2fedb2d7fe103d8de2a356bd6571ee633edae097b6a8d256131354530501d0b4cc78558
CoreDNS-1.11.3
linux/amd64, go1.21.11, a6338e9
[INFO] Reloading
[INFO] plugin/health: Going into lameduck mode for 5s
[INFO] plugin/reload: Running configuration SHA512 = 7c1188195279b86b7710d60e55f0280abfdb557075cf41989074d35bedf7cec084aa74a5647995e64e7f1d94a8ffe53a3fc5439d87b83985e13276f7b944eff3
[INFO] Reloading complete
```

## 典型的配置文件

### Corefile 文件内容

```plaintext
.:53 {
    errors
    health {
        lameduck 5s
    }
    ready
    kubernetes cluster.local in-addr.arpa ip6.arpa {
        pods insecure
        ttl 30
        fallthrough in-addr.arpa ip6.arpa
    }
    prometheus :9153
    forward . /etc/resolv.conf {
        prefer_udp
        max_concurrent 2000
    }
    cache 30 {
        prefetch 5 30m 10%
        serve_stale 24h immediate
    }
    loop
    reload
    loadbalance
}
```

### Zone File 的内容

```plaintext
$ORIGIN sensecoreapi-oss.cn.                        ; designates the start of this zone file in the namespace
$TTL 1h                                             ; default expiration time of all resource records without their own TTL value
; ============================== Resource Records ==============================
@                                       IN  SOA     kube-dns.kube-system.svc.cluster.local. hostmaster (
                                        148387146   ; Serial
                                        1d          ; Refresh
                                        2h          ; Retry
                                        4w          ; Expire
                                        1h)         ; Minimum TTL
@                                       IN  NS      kube-dns.kube-system.svc.cluster.local.    ; Name server
aoss-internal.cn-sh-01                  IN  A       **************
*.aoss-internal.cn-sh-01                IN  A       **************
aoss-internal.cn-sh-01                  IN  A       **************
*.aoss-internal.cn-sh-01                IN  A       **************
```

### Kubespray 的 DNS 功能

- 【启用】设置单独的 nodeSelector，`coredns_deployment_nodeselector`，字符串或字符串数组都可以。
- 【启用】设置额外上游服务器，`upstream_dns_servers`，默认为 `/etc/resolv.conf` 从主机配置，但也可以单独设置外部 DNS 服务器。
- 【启用】部署 NodeLocalDNS 组件，`enable_nodelocaldns`，并可以设置第二套 `enable_nodelocaldns_secondary`。
- 【不启用】自动扩缩容，需要部署 `dns_autoscaler` 的服务，根据 `replicas = max(ceil(cores * 1/coresPerReplica), ceil(nodes * 1/nodesPerReplica))` 动态计算副本数。详见：<https://github.com/kubernetes-sigs/cluster-proportional-autoscaler?tab=readme-ov-file#linear-mode>
- 设置 NodeLocalDNS 组件的额外插件，`nodelocaldns_additional_configs`
- 【不启用】支持部署 coredns 特殊后缀副本，修改 `coredns_ordinal_suffix`，新副本有独立的 CoreDNS 的 SVC、Deployment、Auto-Scaler，不过 CoreDNS 配置一样，且 NodeLocalDNS 不支持额外副本。
- 【不启用】设置 pod-name.svc-name.ns.svc.cluster.local的域名解析能力，`enable_coredns_k8s_endpoint_pod_names`
- 【不启用】设置 `k8s_external` 插件，并通过`coredns_k8s_external_zone`设置插件代理的 zone 域名。
- 【不启用】设置 `coredns_pod_disruption_budget` 的 pod disruption budge，最大 unavailable 去除量 `coredns_pod_disruption_budget_max_unavailable`
- 【不启用】除了现有 plugin 之外，增加其他 plugins，`coredns_additional_configs`
- 【不启用】设置 CoreDNS 的 rewrite 插件，`coredns_rewrite_block`
- 【不启用】设置 CoreDNS error 插件的额外处理，`coredns_additional_error_config`
- 配置其他 domain 的 rewrite，`old_dns_domains`
- `nodelocaldns_external_zones`
- 【未移植】Net-Checker，部署一个 Deployment 和 svc 作为服务端，并且以 overlay network 和 host Network 部署两套 Daemonset，从集群所有服务器上连 Net-Checker 服务端，用了判断集群节点连通性。

### 查询集群规模

```bash
# 获取集群机器数量、CPU 和内存数
kubectl get node -o custom-columns=NAME:.metadata.name,CPUS:.status.capacity.cpu,MEMORY:.status.capacity.memory
```

现有TK 集群中 Node per Replica 和 CPUs pre Replica 数量

```bash
Cluster         Replicas   Nodes      Total CPUs   Total Memory (Ki)  Nodes/Replica   CPUs/Replica
cqa             3          16         2016         12436845834240     5.33            672.00
fja             3          85         10736        68660000780288     28.33           3578.67
fza             3          140        17712        281787435487232    46.67           5904.00
jna             3          8          896          4323706757120      2.67            298.67
sha             6          932        113088       886485607768064    155.33          18848.00
shb             5          141        17888        108622421389312    28.20           3577.60
shc             3          205        26080        427324009058304    68.33           8693.33
shd             3          185        20784        192479802011648    61.67           6928.00
she             5          315        54800        614507879755776    63.00           10960.00
shp             3          31         2592         24043598213120     10.33           864.00
sza             3          70         8816         136832159670272    23.33           2938.67
sz2a            3          142        26064        282302015651840    47.33           8688.00
msa             3          396        50448        832436321316864    132.00          16816.00
sta             10         714        48280        366127942017024    71.40           4828.00

stagea          2          5          576          3784175280128      2.50            288.00
dev-01a         3          33         3664         10120031113216     11.00           1221.33
tech-01a        5          18         1904         14859402027008     3.60            380.80
tech-01b        2          8          784          5128992722944      4.00            392.00
tech-01c        1          5          640          6217396903936      5.00            640.00
tech-02a        9          16         2288         16360729907200     1.78            254.22
```

### 链接

- CoreDNS：<https://coredns.io/>，<https://github.com/coredns/coredns>
- DNS AutoScaler：<https://github.com/kubernetes-sigs/cluster-proportional-autoscaler>
- NodeLocalDNS：<https://github.com/kubernetes/dns>, <https://kubernetes.io/docs/tasks/administer-cluster/nodelocaldns/>
- Net Check: <http://bookstack.cn/read/kubespray/netcheck.md>
