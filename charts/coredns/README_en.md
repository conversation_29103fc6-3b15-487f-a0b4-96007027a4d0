# CoreDNS Configuration

<!-- TOC -->

- [CoreDNS Domain Resolution Configuration](#coredns-domain-resolution-configuration)
  - [CoreDNS Configuration Files](#coredns-configuration-files)
  - [CoreDNS Configuration Update Logic](#coredns-configuration-update-logic)
- [CoreDNS Change Logic for Non-Helm Deployments](#coredns-change-logic-for-non-helm-deployments)
  - [Deployment YAML Adjustments (One-Time Changes)](#deployment-yaml-adjustments-one-time-changes)
    - [Deployment Modifications](#deployment-modifications)
    - [Deployment Rollback](#deployment-rollback)
  - [CoreDNS Configuration Deployment](#coredns-configuration-deployment)
    - [Configmap Modifications](#configmap-modifications)
    - [Configmap Change Verification](#configmap-change-verification)
    - [Configmap Rollback](#configmap-rollback)
- [CoreDNS Change Logic for Helm Deployments](#coredns-change-logic-for-helm-deployments)
  - [Deployment Prerequisites](#deployment-prerequisites)
  - [Pulling Helm Chart](#pulling-helm-chart)
  - [Service Deployment](#service-deployment)
    - [Deploying Services](#deploying-services)
    - [Updating Services](#updating-services)
    - [Uninstalling Services](#uninstalling-services)
  - [Checking Service Status](#checking-service-status)
    - [Viewing Deployments](#viewing-deployments)
    - [Viewing Running Pods](#viewing-running-pods)
    - [Checking Service Startup Logs](#checking-service-startup-logs)
- [Typical Configuration Files](#typical-configuration-files)
  - [Corefile Content](#corefile-content)
  - [Zone File Content](#zone-file-content)
  - [Querying Cluster Scale](#querying-cluster-scale)
  - [Links](#links)

<!-- /TOC -->

## CoreDNS Domain Resolution Configuration

### CoreDNS Configuration Files

CoreDNS domain resolution configurations primarily specify IP address lists for different domains. Updating domain resolution configurations involves modifying CoreDNS configuration files.

Domain resolution configurations are divided into two parts:

1. **Corefile configurations** referencing zone files via the `file` plugin:

   ```plaintext
   # file "file_path" "space-separated_domain_list"
   file /etc/coredns/sensecoreapi.cn.db afs-internal-forward.cn-sh-01e-ec01.sensecoreapi.cn
   ```

2. **Zone files** defining domain-to-IP mappings:

   ```plaintext
   aoss-internal.cn-sh-01                  IN  A       **************
   *.aoss-internal.cn-sh-01                IN  A       **************
   ```

### CoreDNS Configuration Update Logic

**Corefile** is the primary configuration. If the [`reload`](https://coredns.io/plugins/reload/) plugin is enabled, CoreDNS automatically reloads this configuration file every `30s±15s`. Corefile updates trigger reloads of referenced zone files.

**File Plugin** for zone files are reloaded every `60s` **only if** the `serial` field changes. Otherwise, zone file won't be loaded no matter other content are changed or not. This `serial` value is generated base on configured domains and their IPs. The `serial` value changes when domain settings change. And the value will convert to [`uint32`](https://github.com/coredns/coredns/blob/master/plugin/file/file.go#L145) for changes comparison.

## CoreDNS Change Logic for Non-Helm Deployments

For clusters where CoreDNS was **not deployed via Helm**, modifications require manual adjustments to Deployments and Configmaps.

### Deployment YAML Adjustments (One-Time Changes)

The script `coredns_deploy_change.sh` removes `items` under Deployment volumes to simplify ConfigMap mounting. Meanwhile, this script support to validate and rollback the changes.

Default CoreDNS deployment mounts configmap by specific `key` of configmap under volume items. Removing these key will simplify multiple configmap keys mounting while Corefile and zone file changes.

This script only needs to run once for key removal.

#### Deployment Modifications

```bash
# Check current Deployment volume configuration
$ kubectl get deployment coredns -n kube-system -o jsonpath="{.spec.template.spec.volumes}"
[{"configMap":{"defaultMode":420,"items":[{"key":"Corefile","path":"Corefile"},{"key":"oss.db","path":"oss.db"}],"name":"coredns"},"name":"config-volume"}]

# Remove 'items' configuration
$ bash coredns_deploy_change.sh remove

# Verify changes
$ kubectl get deployment coredns -n kube-system -o jsonpath="{.spec.template.spec.volumes}"
[{"configMap":{"defaultMode":420,"name":"coredns"},"name":"config-volume"}]
```

#### Deployment Rollback

```bash
# List backup files
$ ls coredns-deploy-volume-*-backup.json
coredns-deploy-volume-2025-02-21_12-45-37-backup.json

# Restore from backup
$ bash coredns_deploy_change.sh restore coredns-deploy-volume-2025-02-21_12-45-37-backup.json
```

### CoreDNS Configuration Deployment

#### Configmap Modifications

Using `coredns_cm_change.sh` to update the `coredns` configmap in `kube-system` namespace. This script support to validate and rollback the changes.

```bash
# Backup and update ConfigMap
$ bash coredns_cm_change.sh update
```

#### Configmap Change Verification

Use `nslookup_domain_check.sh` to validate DNS resolution. This script will get coredns service ip and then test many types of domains including domains that in current settings.

```bash
# Run check script
$ bash nslookup_domain_check.sh
```

#### Configmap Rollback

```bash
# List backups
$ ls coredns-cm-*-backup.yaml
coredns-cm-2025-02-21_12-47-26-backup.yaml

# Restore ConfigMap
$ bash coredns_cm_change.sh restore coredns-cm-2025-02-21_12-47-26-backup.yaml
```

## CoreDNS Change Logic for Helm Deployments

### Deployment Prerequisites

- CoreDNS must be deployed in the `kube-system` namespace.
- CoreDNS images must be available in a public registry with appropriate `imagePullSecrets` configured.
- Helm Chart repository must be pre-configured. Refers to [配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### Pulling Helm Chart

```bash
helm3 repo list
helm3 repo update

# pull latest chart version
helm3 pull boson/coredns
# or pull specific chart version
# helm3 pull boson/coredns --version x.y.z

tar -xzvf coredns-x.y.z.tgz
```

### Service Deployment

#### Deploying Services

```bash
helm3 install coredns coredns -n kube-system -f path/to/env/coredns/values.yaml
```

#### Updating Services

```bash
helm3 upgrade coredns coredns -n kube-system -f path/to/env/coredns/values.yaml
```

#### Uninstalling Services

```bash
helm3 uninstall coredns -n kube-system
```

### Checking Service Status

#### Viewing Deployments

```bash
$ kubectl -n kube-system get deployment coredns
NAME      READY   UP-TO-DATE   AVAILABLE   AGE
coredns   3/3     3            3           4d
```

#### Viewing Running Pods

```bash
$ kubectl -n kube-system get pods -l k8s-app=kube-dns
NAME                       READY   STATUS    RESTARTS   AGE
coredns-7cf8fbd8d7-8rqz6   1/1     Running   0          4d
coredns-7cf8fbd8d7-k4j9r   1/1     Running   0          4d
coredns-7cf8fbd8d7-q6lsb   1/1     Running   0          4d
```

#### Checking Service Startup Logs

```bash
$ kubectl -n kube-system logs coredns-7cf8fbd8d7-8rqz6
.:53
[INFO] plugin/reload: Running configuration SHA512 = 0825f01f7e4d969aa0af933b395ab3fe7b89484b0299dae46db94508e2fedb2d7fe103d8de2a356bd6571ee633edae097b6a8d256131354530501d0b4cc78558
CoreDNS-1.11.3
linux/amd64, go1.21.11, a6338e9
[INFO] Reloading
[INFO] plugin/health: Going into lameduck mode for 5s
[INFO] plugin/reload: Running configuration SHA512 = 7c1188195279b86b7710d60e55f0280abfdb557075cf41989074d35bedf7cec084aa74a5647995e64e7f1d94a8ffe53a3fc5439d87b83985e13276f7b944eff3
[INFO] Reloading complete
```

## Typical Configuration Files

### Corefile Content

```plaintext
.:53 {
    errors
    health {
        lameduck 5s
    }
    ready
    kubernetes cluster.local in-addr.arpa ip6.arpa {
        pods insecure
        ttl 30
        fallthrough in-addr.arpa ip6.arpa
    }
    prometheus :9153
    forward . /etc/resolv.conf {
        prefer_udp
        max_concurrent 2000
    }
    cache 30 {
        prefetch 5 30m 10%
        serve_stale 24h immediate
    }
    loop
    reload
    loadbalance
}
```

### Zone File Content

```plaintext
$ORIGIN sensecoreapi-oss.cn.                        ; designates the start of this zone file in the namespace
$TTL 1h                                             ; default expiration time of all resource records without their own TTL value
; ============================== Resource Records ==============================
@                                       IN  SOA     kube-dns.kube-system.svc.cluster.local. hostmaster (
                                        148387146   ; Serial
                                        1d          ; Refresh
                                        2h          ; Retry
                                        4w          ; Expire
                                        1h)         ; Minimum TTL
@                                       IN  NS      kube-dns.kube-system.svc.cluster.local.    ; Name server
aoss-internal.cn-sh-01                  IN  A       **************
*.aoss-internal.cn-sh-01                IN  A       **************
aoss-internal.cn-sh-01                  IN  A       **************
*.aoss-internal.cn-sh-01                IN  A       **************
```

### Querying Cluster Scale

```bash
# Get node count, CPU, and memory
kubectl get node -o custom-columns=NAME:.metadata.name,CPUS:.status.capacity.cpu,MEMORY:.status.capacity.memory
```

Below are nodes, CPU, and memory info for current existing clusters.

```bash
Cluster         Replicas   Nodes      Total CPUs   Total Memory (Ki)  Nodes/Replica   CPUs/Replica
cqa             3          16         2016         12436845834240     5.33            672.00
fja             3          85         10736        68660000780288     28.33           3578.67
fza             3          140        17712        281787435487232    46.67           5904.00
jna             3          8          896          4323706757120      2.67            298.67
sha             6          932        113088       886485607768064    155.33          18848.00
shb             5          141        17888        108622421389312    28.20           3577.60
shc             3          205        26080        427324009058304    68.33           8693.33
shd             3          185        20784        192479802011648    61.67           6928.00
she             5          315        54800        614507879755776    63.00           10960.00
shp             3          31         2592         24043598213120     10.33           864.00
sza             3          70         8816         136832159670272    23.33           2938.67
sz2a            3          142        26064        282302015651840    47.33           8688.00
msa             3          396        50448        832436321316864    132.00          16816.00
sta             10         714        48280        366127942017024    71.40           4828.00

stagea          2          5          576          3784175280128      2.50            288.00
dev-01a         3          33         3664         10120031113216     11.00           1221.33
tech-01a        5          18         1904         14859402027008     3.60            380.80
tech-01b        2          8          784          5128992722944      4.00            392.00
tech-01c        1          5          640          6217396903936      5.00            640.00
tech-02a        9          16         2288         16360729907200     1.78            254.22
```

### Links

- CoreDNS: <https://coredns.io/>, <https://github.com/coredns/coredns>
- DNS AutoScaler: <https://github.com/kubernetes-sigs/cluster-proportional-autoscaler>
- NodeLocalDNS: <https://github.com/kubernetes/dns>, <https://kubernetes.io/docs/tasks/administer-cluster/nodelocaldns/>
- Net Check: <http://bookstack.cn/read/kubespray/netcheck.md>
