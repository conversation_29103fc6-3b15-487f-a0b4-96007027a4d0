apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    {{- include "coredns.labels" . | nindent 4 }}
data:
  Corefile: |
{{- if .Values.coredns_external_zones }}
{{-   range .Values.coredns_external_zones }}
    {{- range .zones }} {{ . }} {{- end }} {
        log
{{-     include "coredns.plugins.error" . | nindent 8 }}
{{-     if .rewrite }}
{{-       range .rewrite }}
        rewrite {{ . }}
{{-       end }}
{{-     end }}
{{-     if .Values.coredns_hosts_extra_domains }}
{{-     include "coredns.plugins.hosts" . | nindent 8 }}
{{-     end }}
        forward . {{ .nameservers | join " " }}
        loadbalance
        cache {{ .cache | default 5 }}
        reload
    }
{{-   end }}
{{- end }}
    .:53 {
{{- if .Values.coredns_additional_configs }}
        {{ .Values.coredns_additional_configs | toYaml | nindent 8 }}
{{- end }}
{{- include "coredns.plugins.error" . | nindent 8 }}
        health {
            lameduck 5s
        }
{{- if .Values.coredns_rewrite_block }}
        {{ .Values.coredns_rewrite_block | toYaml | nindent 8 }}
{{- end }}
{{- range .Values.old_dns_domains }}
        rewrite name suffix {{ . }} {{ $.Values.dns_domain }} answer auto
{{- end }}
        ready
        kubernetes {{ .Values.dns_domain }}{{- if .Values.coredns_kubernetes_extra_domains }} {{ .Values.coredns_kubernetes_extra_domains }}{{- end }}{{- if .Values.enable_coredns_reverse_dns_lookups }} in-addr.arpa ip6.arpa{{- end }} {
            pods insecure
{{- if .Values.enable_coredns_k8s_endpoint_pod_names }}
            endpoint_pod_names
{{- end }}
{{- if .Values.coredns_kubernetes_extra_opts }}
{{-   range .Values.coredns_kubernetes_extra_opts }}
            {{ . }}
{{-   end }}
{{- end }}
{{- if .Values.enable_coredns_reverse_dns_lookups }}
            fallthrough in-addr.arpa ip6.arpa
{{- end }}
        }
        prometheus :9153
{{- if .Values.coredns_hosts_extra_domains }}
{{- include "coredns.plugins.hosts" . | nindent 8 -}}
{{- end }}
{{- if .Values.coredns_file_extra_domains }}
{{-   range .Values.coredns_file_extra_domains }}
        file {{ .mounted_db_file }}{{- range .domains }} {{ .domain }}{{- end }}
{{-   end }}
{{- end }}
{{- if .Values.upstream_dns_servers }}
        forward . {{ .Values.upstream_dns_servers | join " " }} {
{{- else }}
        forward . /etc/resolv.conf {
{{- end }}
            prefer_udp
            max_concurrent {{ .Values.dns_upstream_forward_max_concurrent | default 1000 }}
{{- if .Values.dns_upstream_forward_extra_opts }}
{{-   range $optname, $optvalue := .Values.dns_upstream_forward_extra_opts }}
            {{ $optname }} {{ $optvalue }}
            {# do not add a trailing space when $optvalue == ''
               workaround for: https://github.com/kubernetes/kubernetes/issues/36222 #}
{{-   end }}
{{- end }}
        }
{{- if .Values.enable_coredns_k8s_external }}
        k8s_external {{ .Values.coredns_k8s_external_zone }}
{{- end }}
        {{- tpl .Values.coredns_default_zone_cache_block . | nindent 8 -}}
        loop
        reload
        loadbalance
    }
{{- if .Values.coredns_file_extra_domains }}
{{-   $coredns_file_opts := .Values.coredns_file_opts -}}
{{-   range .Values.coredns_file_extra_domains }}
  {{ .mounted_db_file | base }}: |
{{-     $root_zone := .root_zone }}
    $ORIGIN {{ $root_zone }}.{{ " " | repeat (sub 43 ($root_zone | len) | int) }}; designates the start of this zone file in the namespace
    $TTL {{ $coredns_file_opts.ttl }}                                             ; default expiration time of all resource records without their own TTL value
    ; ============================== Resource Records ==============================
    @                                       IN  SOA     {{ $coredns_file_opts.SOA.mname }} {{ $coredns_file_opts.SOA.rname }} (
                                            {{ .serial }}  ; Serial
                                            {{ $coredns_file_opts.SOA.refresh }}          ; Refresh
                                            {{ $coredns_file_opts.SOA.retry }}          ; Retry
                                            {{ $coredns_file_opts.SOA.expire }}          ; Expire
                                            {{ $coredns_file_opts.SOA.minimum_ttl }})         ; Minimum TTL
    @                                       IN  NS      {{ $coredns_file_opts.SOA.mname }}    ; Name server
{{-     range .domains }}
{{-       $domain_prefix := .domain | trimSuffix $root_zone | trimSuffix "." }}
{{-       $wildcard := .wildcard }}
{{-       range .ips }}
    {{ $domain_prefix }}{{ " " | repeat (sub 40 ($domain_prefix | len) | int) }}IN  A       {{ . }}
{{-         if $wildcard }}
    *.{{ $domain_prefix }}{{ " " | repeat (sub 38 ($domain_prefix | len) | int) }}IN  A       {{ . }}
{{-         end }}
{{-       end }}
{{-     end }}
{{-   end }}
{{- end }}
