apiVersion: v1
kind: Service
metadata:
  name: coredns{{ .Values.coredns_ordinal_suffix }}
  namespace: kube-system
  labels:
    k8s-app: kube-dns{{ .Values.coredns_ordinal_suffix }}
    kubernetes.io/name: "coredns{{ .Values.coredns_ordinal_suffix }}"
    addonmanager.kubernetes.io/mode: Reconcile
    {{- include "coredns.labels" . | nindent 4 }}
  annotations:
    prometheus.io/port: "9153"
    prometheus.io/scrape: "true"
spec:
  selector:
    k8s-app: kube-dns{{ .Values.coredns_ordinal_suffix }}
  clusterIP: {{ .Values.skydns_server }}
  ports:
    - name: dns
      port: 53
      protocol: UDP
    - name: dns-tcp
      port: 53
      protocol: TCP
    - name: metrics
      port: 9153
      protocol: TCP
