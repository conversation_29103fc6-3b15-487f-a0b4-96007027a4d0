{{/*
Expand the name of the chart.
*/}}
{{- define "coredns.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "coredns.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "coredns.selectorLabels" -}}
app.kubernetes.io/name: {{ include "coredns.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "coredns.labels" -}}
helm.sh/chart: {{ include "coredns.chart" . }}
{{ include "coredns.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Chart.Name }}
app.kubernetes.io/part-of: coredns
{{- end -}}

{{/*
error plugin
*/}}
{{- define "coredns.plugins.error" -}}
{{- if .Values.coredns_additional_error_config -}}
errors {
    {{ .Values.coredns_additional_error_config | toYaml | nindent 10 }}
}
{{- else -}}
errors
{{- end -}}
{{- end -}}

{{/*
hosts plugin
*/}}
{{- define "coredns.plugins.hosts" -}}
hosts {
{{-   range .Values.coredns_hosts_extra_domains }}
    {{ .ip }} {{ .domain }}
{{-   end }}
    fallthrough
}
{{- end -}}
