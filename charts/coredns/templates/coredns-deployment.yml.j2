apiVersion: apps/v1
kind: Deployment
metadata:
  name: "coredns{{ .Values.coredns_ordinal_suffix }}"
  namespace: kube-system
  labels:
    k8s-app: "kube-dns{{ .Values.coredns_ordinal_suffix }}"
    addonmanager.kubernetes.io/mode: Reconcile
    kubernetes.io/name: "coredns{{ .Values.coredns_ordinal_suffix }}"
    {{- include "coredns.labels" . | nindent 4 }}
spec:
{{- if not .Values.enable_dns_autoscaler }}
  replicas: {{ .Values.dns_min_replicas }}
{{- end }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 10%
  selector:
    matchLabels:
      k8s-app: kube-dns{{ .Values.coredns_ordinal_suffix }}
  template:
    metadata:
      labels:
        k8s-app: kube-dns{{ .Values.coredns_ordinal_suffix }}
    spec:
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      nodeSelector:
        {{- toYaml .Values.coredns_deployment_nodeselector | nindent 8 }}
      priorityClassName: system-cluster-critical
      serviceAccountName: coredns
{{- with .Values.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
{{- if .Values.dns_extra_tolerations }}
      tolerations:
{{- .Values.dns_extra_tolerations | toYaml | nindent 8 }}
{{- end }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - topologyKey: "kubernetes.io/hostname"
            labelSelector:
              matchLabels:
                k8s-app: kube-dns{{ .Values.coredns_ordinal_suffix }}
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-role.kubernetes.io/control-plane
                operator: In
                values:
                - ""
      containers:
      - name: coredns
        image: "{{ .Values.coredns_image_repo }}:{{ .Values.coredns_image_tag }}"
        imagePullPolicy: {{ .Values.k8s_image_pull_policy }}
        resources:
          limits:
{{- if .Values.dns_cpu_limit }}
            cpu: {{ .Values.dns_cpu_limit }}
{{- end }}
            memory: {{ .Values.dns_memory_limit }}
          requests:
            cpu: {{ .Values.dns_cpu_requests }}
            memory: {{ .Values.dns_memory_requests }}
        args: [ "-conf", "/etc/coredns/Corefile" ]
        volumeMounts:
        - name: config-volume
          mountPath: /etc/coredns
        ports:
        - containerPort: 53
          name: dns
          protocol: UDP
        - containerPort: 53
          name: dns-tcp
          protocol: TCP
        - containerPort: 9153
          name: metrics
          protocol: TCP
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            add:
            - NET_BIND_SERVICE
            drop:
            - all
          readOnlyRootFilesystem: true
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8181
            scheme: HTTP
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 10
      dnsPolicy: Default
      volumes:
        - name: config-volume
          configMap:
            name: coredns
