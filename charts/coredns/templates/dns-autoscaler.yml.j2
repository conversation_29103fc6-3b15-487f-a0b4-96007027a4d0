{{- if .Values.enable_dns_autoscaler }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
  namespace: kube-system
  labels:
    k8s-app: dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
    addonmanager.kubernetes.io/mode: Reconcile
    {{- include "coredns.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      k8s-app: dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
  template:
    metadata:
      labels:
        k8s-app: dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
      annotations:
    spec:
      nodeSelector:
        {{- toYaml .Values.dns_autoscaler_deployment_nodeselector | nindent 8 }}
      priorityClassName: system-cluster-critical
      securityContext:
        seccompProfile:
          type: RuntimeDefault
        supplementalGroups: [ 65534 ]
        fsGroup: 65534
{{- with .Values.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
{{- if .Values.dns_autoscaler_extra_tolerations }}
      tolerations:
{{- .Values.dns_autoscaler_extra_tolerations | toYaml | nindent 8 }}
{{- end }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - topologyKey: "kubernetes.io/hostname"
            labelSelector:
              matchLabels:
                k8s-app: dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-role.kubernetes.io/control-plane
                operator: In
                values:
                - ""
      containers:
      - name: autoscaler
        image: "{{ .Values.dnsautoscaler_image_repo }}:{{ .Values.dnsautoscaler_image_tag }}"
        resources:
          requests:
            cpu: {{ .Values.dns_autoscaler_cpu_requests }}
            memory: {{ .Values.dns_autoscaler_memory_requests }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8080
            scheme: HTTP
        command:
        - /cluster-proportional-autoscaler
        - --namespace=kube-system
        - --default-params={"linear":{"preventSinglePointFailure":{{ .Values.dns_prevent_single_point_failure }},"coresPerReplica":{{ .Values.dns_cores_per_replica }},"nodesPerReplica":{{ .Values.dns_nodes_per_replica }},"min":{{ .Values.dns_min_replicas }}}}
        - --logtostderr=true
        - --v=2
        - --configmap=dns-autoscaler{{ .Values.coredns_ordinal_suffix }}
        - --target=Deployment/coredns{{ .Values.coredns_ordinal_suffix }}
      serviceAccountName: dns-autoscaler
{{- end }}
