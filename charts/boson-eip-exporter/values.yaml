
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # bosonEIPExporterImage defines boson-ib-exporter image and tag.
  bosonEIPExporterImage: registry.sensetime.com/sensecore-boson/boson-eip-exporter:v1.0.1-c1a37ac-20240402150510
  product: BosonService
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists

  bosonEIPExporter:
    # fullname defines name of boson-eip-exporter and references every where.
    fullname: "boson-eip-exporter"
    # replicas defines number of pods running in cluster
    replicas: 1
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 300m
    # memLimits defines memory limit used by pods.
    memLimits: 300Mi
    # metric port of boson eip exporter container
    metrics: "55999"
    # eip db connection info
    DB:
        host: "XX.XX.XX.XX"
        port: "XXXX"
        user: "boson"
        password: "XXXX"
        db: "XXXX"
    collector:
      # when query colla api, the number of concurrency
      workers: 3
      # the interval for query colla api in second
      interval: 15
      # the delay time of the current time in second
      # we can only retrieve data up to 30 seconds ago at fastest
      delay: 30
      # 当请求colla api时, 我们能拿到某一个时间段的平均带宽， 这里设置为15s的平均带宽数据
      duration: 15
      # 每次调用colla api时， 每页请求多少条eip的带宽数据
      page_size: 5
      # colla api url
      url: https://*************/api/v2/query/getData/ipAddr
      # colla api secret key
      secretKey: 9fcfaedd366c5582427b9e038f1e4ff38686b426
