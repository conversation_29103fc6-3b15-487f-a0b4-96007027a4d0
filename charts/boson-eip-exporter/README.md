# Boson EIP Exporter

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

Boson EIP Exporter 作为 Boson Metrics的组件，用于输出 EIP带宽监控指标数据。

## 准备工作

boson-eip-exporter 组件需要部署到 Diamond KK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-eip-exporter
# or pull specific chart version
# helm3 pull boson/boson-eip-exporter --version x.y.z

tar -xzvf boson-eip-exporter-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-eip-exporter/values.yaml)
- [dev11](../../envs/dev11/boson-eip-exporter/values.yaml)
- [dev44](../../envs/dev44/boson-eip-exporter/values.yaml)
- [集成测试](../../envs/int-test/boson-eip-exporter/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-eip-exporter/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-eip-exporter/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-eip-exporter/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

## 服务部署

### 部署服务

```bash
helm3 install boson-eip-exporter boson-eip-exporter -n plat-boson-service -f path/to/env/boson-eip-exporter/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-eip-exporter boson-eip-exporter -n plat-boson-service -f path/to/env/boson-eip-exporter/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-eip-exporter -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME                   READY   UP-TO-DATE   AVAILABLE   AGE
# boson-eip-exporter      1/1     1            1           108s
```

### 查看运行的Pod

```bash
kubectl -n plat-boson-service get pod
# NAME                                    READY   STATUS             RESTARTS   AGE
# boson-eip-exporter-64cdc5684-lx9bb       1/1     Running            0          2m21s
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-eip-exporter-64cdc5684-lx9bb
# ts=2023-02-06T02:18:20.163Z caller=infiniband_exporter.go:128 level=info msg="Starting infiniband_exporter" version="(version=0.4.2, branch=HEAD, revision=dfec616c0f55559bebf0e47dd861ac785a6b72ef)"
# ts=2023-02-06T02:18:20.163Z caller=infiniband_exporter.go:129 level=info msg="Build context" build_context="(go=go1.16.15, user=root@ee54316e6647, date=20221207-16:30:36)"
# ts=2023-02-06T02:18:20.163Z caller=infiniband_exporter.go:130 level=info msg="Starting Server" address=:9315
```
