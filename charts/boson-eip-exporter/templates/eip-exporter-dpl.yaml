apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonEIPExporter.fullname }}
    {{- include "bosonEIPExporter.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonEIPExporter.fullname }}
  replicas: 1
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonEIPExporter.fullname }}
    spec:
      serviceAccount: {{ .Values.global.bosonEIPExporter.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonEIPExporter.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonEIPExporter.fullname }}
        image: "{{ .Values.global.bosonEIPExporterImage }}"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-eip-exporter
        - -c
        - /boson/conf/eip-exporter.yaml
        ports:
        - name: metrics
          containerPort: {{ .Values.global.bosonEIPExporter.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonEIPExporter.cpuLimits }}
            memory: {{ .Values.global.bosonEIPExporter.memLimits }}
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: BOSON_EIP_EXPORTER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_EIP_EXPORTER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_EIP_EXPORTER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_EIP_EXPORTER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: {{ .Values.global.bosonEIPExporter.metrics }}
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: {{ .Values.global.bosonEIPExporter.metrics }}
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/eip-exporter.yaml
          subPath: path/to/eip-exporter.yaml
      volumes:
      - name: config
        configMap:
          name: {{ .Values.global.bosonEIPExporter.fullname }}-config
          items:
          - key: eip-exporter.yaml
            path: path/to/eip-exporter.yaml
