apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}
  namespace: {{ .Values.global.namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.global.bosonEIPExporter.fullname }}
subjects:
- kind: ServiceAccount
  name: {{ .Values.global.bosonEIPExporter.fullname }}
  namespace: {{ .Values.global.namespace }}
