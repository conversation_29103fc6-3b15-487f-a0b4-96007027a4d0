apiVersion: v1
data:
  eip-exporter.yaml: |-
    eip_exporter:
      metrics_port: {{ .Values.global.bosonEIPExporter.metrics }}
    db:
    {{- with .Values.global.bosonEIPExporter.DB }}
      host: {{ .host | quote }}
      port: {{ .port }}
      user: {{ .user | quote }}
      password: {{ .password | quote }}
      db: {{ .db | quote }}
    {{- end }}

    {{- with .Values.global.bosonEIPExporter.collector }}
    collector:
      vendor: colla
      workers: {{ .workers }}
      interval: {{int .interval }}
      delay: {{int .delay }}
      duration: {{int .duration }}
      page_size: {{int .page_size }}
      url: {{ .url }}
      param:
        name: query
        value: |
          {secretKey:"{{ .secretKey }}",probeIds:"all",step:"second",date:"{{ `{{date}}` }}",filter:{field:"ipAddr",condition:"{{ `{{eip_ips}}` }}",filterType:"="},returnField:["ipAddr","totalByte","txByte","rxByte","txBitps","rxBitps","txTcpSegmentLostPacketRate","rxTcpSegmentLostPacketRate","inPacket","outPacket"],groupBy: ["ipAddr"]}
    {{- end }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonEIPExporter.labels" . | nindent 4 }}
