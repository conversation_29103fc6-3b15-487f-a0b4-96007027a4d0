{{/*
Expand the name of the chart.
*/}}
{{- define "bosonEIPExporter.name" -}}
{{- default .Values.global.bosonEIPExporter.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonEIPExporter.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end -}}

{{/*
Selector labels
*/}}
{{- define "bosonEIPExporter.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonEIPExporter.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end -}}

{{/*
Common labels
*/}}
{{- define "bosonEIPExporter.labels" -}}
helm.sh/chart: {{ include "bosonEIPExporter.chart" . }}
{{ include "bosonEIPExporter.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonEIPExporter.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end -}}
