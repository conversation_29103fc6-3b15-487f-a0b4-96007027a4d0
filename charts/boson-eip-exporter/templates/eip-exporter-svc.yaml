apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonEIPExporter.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonEIPExporter.fullname }}-service
    {{- include "bosonEIPExporter.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.bosonEIPExporter.fullname }}
  ports:
  - name: metrics
    port: {{ int .Values.global.bosonEIPExporter.metrics }}
    protocol: TCP
