# Default values for boson-default-vpc-gateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
# global values can be accessed from any chart or sub chart

global:
  product: BosonService
  # fullname defines name of boson-assistant and references every where.
  fullname: "boson-default-vpc-gateway"
  # cm_namespace 设定configmap boson-default-vpc-gateway-config 所在的namespace
  cm_namespaces:
  - plat-lepton-service
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： dev22|test65|systest|prod
  envName: devxx
  haMode: master-standby
  # natGateway default vpc nat gateway config
  natGateway:
    # 跟k8s节点同网段的ip，需提前申请
    net1Ips:
      # 上面ip的网段，需配置为ip/xx
      # 例如ip为************，cidr段***********/24，则ip_cidr应配置为************/24
      # ！！！！！注意：绝对不能直接使用cidr，配置为***********/24！！！！！
      # (Note: <PERSON><PERSON><PERSON>)根据实际生产来修改11x的网段
      vip: 10.11x.4.152
      rips:
      - 10.11x.4.162
      - 10.11x.4.163
      cidr: 24
    # 上面ip对应的网关
    gateway: 10.11x.4.1
    # lan_ip 为natGateway使用的子网ip，暂时固定不需要手动修改
    eth0Ips:
      vip: ************
      rips:
      - ************
      - ************
  # subnet 为natGateway所在子网配置，暂时固定不需要手动修改
  subnet:
    # createSeparatedSubnet 是否为 default vpc gateway 创建单独的 subnet
    createSeparatedSubnet: false
    # subnetName: 指定 default vpc gateway 所使用的 subnet 名称
    subnetName: boson-default-vpc-gateway-subnet
    # cidr 给 default vpc subnet 使用的子网
    cidr: **********/24
    # cidr 给 default vpc subnet 使用的子网网关
    gateway: **********
  # rules 为需要gateway暴露的ip及其端口，暂时固定不需要手动修改
  rules:
  - internal_ip: **********
    internal_port: 53
  - internal_ip: **********
    internal_port: 11010
  - internal_ip: **********
    internal_port: 11011
  - internal_ip: ********** # bosont-net-controller-service clusterIP
    internal_port: 18000 # bosont-net-controller-service.xds.port
  - internal_ip: ********** # bosont-net-controller-service clusterIP
    internal_port: 52040 # bosont-net-controller-service.slbHttp.port
  - internal_ip: ********** # bosont-net-controller-service clusterIP
    internal_port: 52090 # bosont-net-controller-service.slbGrpc.port
