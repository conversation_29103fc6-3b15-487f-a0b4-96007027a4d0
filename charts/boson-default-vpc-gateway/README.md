# Boson Default VPC Gateway


Boson Default VPC Gateway 是 Boson 服务中给默认 VPC 提供 NAT 网关的服务，并且默认配置开放 k8s coredns 在 10.96.0.10 的 53 端口提供 tcp/udp 的协议连接，支持 SNAT/DNAT 服务。此功能是便于每个租户 VPC 内部的容器可以通过链接当前 NAT 网关暴露的 coredns 服务来进行 dns 服务解析。

## 准备工作

- boson-default-vpc-gateway 组件需要部署到 Diamond TK 集群当中。与其他网络服务不同的是，该服务作为 kube-ovn 的扩展服务，需要部署到 kube-system namespace 之中。所有 kube-ovn 的 VPC 网关都在 kube-system 之中。
- 同时，boson-default-vpc-gateway 组件需要 namespace `plat-lepton-service` 已经创建。由于 default vpc gateway 会使用独立的 IP 地址代替 coredns 作为域名解析服务的地址。为了能方便的让 lepton 的服务知道该服务地址，boson-default-vpc-gateway 会在 namespace plat-lepton-service 的目录下，创建 configmap `boson-default-vpc-gateway-config` 包含该 IP 地址。

### 部署的前提条件

- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-default-vpc-gateway
# or pull specific chart version
# helm3 pull boson/boson-default-vpc-gateway --version x.y.z

tar -xzvf boson-default-vpc-gateway-x.y.z.tgz
```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-default-vpc-gateway/values.yaml)
- [dev11](../../envs/dev11/boson-default-vpc-gateway/values.yaml)
- [dev44](../../envs/dev44/boson-default-vpc-gateway/values.yaml)
- [集成测试](../../envs/int-test/boson-default-vpc-gateway/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-default-vpc-gateway/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-default-vpc-gateway/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-default-vpc-gateway/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

## 服务部署

### 部署服务

```bash
helm3 install boson-default-vpc-gateway boson-default-vpc-gateway -n kube-system -f path/to/env/boson-default-vpc-gateway/values.yaml
```

### 更新服务

```bash
helm3 upgrade boson-default-vpc-gateway boson-default-vpc-gateway -n kube-system -f path/to/env/boson-default-vpc-gateway/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-default-vpc-gateway -n kube-system
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n kube-system get deployment vpc-nat-gw-boson-default-vpc-gateway
# NAME                                   READY   UP-TO-DATE   AVAILABLE   AGE
# vpc-nat-gw-boson-default-vpc-gateway   1/1     1            1           2m
```

### 查看运行的Pod

```bash
kubectl -n kube-system get pod | grep vpc-nat-gw-boson-default-vpc-gateway
# NAME                                                     READY   STATUS      RESTARTS   AGE
# vpc-nat-gw-boson-default-vpc-gateway-b94c554c6-k2tmh     1/1     Running            0    2m
```
