{{/*
Expand the name of the chart.
*/}}
{{- define "defaultVpcGateway.name" -}}
{{- default .Values.global.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "defaultVpcGateway.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "defaultVpcGateway.selectorLabels" -}}
app.kubernetes.io/name: {{ include "defaultVpcGateway.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "defaultVpcGateway.labels" -}}
helm.sh/chart: {{ include "defaultVpcGateway.chart" . }}
{{ include "defaultVpcGateway.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
boson.sensetime.com/hagw-name: boson-vpc-gw-dummy-hagw
{{- end }}
