{{- $gwip := .Values.global.natGateway.net1Ips.vip -}}
apiVersion: kubeovn.io/v1
kind: VpcNatGateway
metadata:
  annotations:
    subnetGateway: {{ .Values.global.subnet.gateway }}
    boson.sensetime.com/ha-mode: {{ .Values.global.haMode }}
    boson.sensetime.com/ha-peer-ip: {{ index .Values.global.natGateway.net1Ips.rips 1 }}
    boson.sensetime.com/ha-vip: {{ .Values.global.natGateway.net1Ips.vip }}
    boson.sensetime.com/ha-monitor: {{ .Values.global.natGateway.gateway }}
    boson.sensetime.com/ha-external-vips: {{ .Values.global.natGateway.eth0Ips.vip }}
  name: {{ .Values.global.fullname }}-0
  labels:
    {{- include "defaultVpcGateway.labels" . | nindent 4 }}
spec:
  vpc: ovn-cluster
  subnet: {{ .Values.global.subnet.subnetName }}
  # use rip in lanIp
  lanIp: {{  index .Values.global.natGateway.eth0Ips.rips 0 }}
  eips:
  # use rip in eips
  - eipCIDR: {{ index .Values.global.natGateway.net1Ips.rips 0 }}/{{ .Values.global.natGateway.net1Ips.cidr }}
    gateway: {{ .Values.global.natGateway.gateway }}
  snatRules:
  # use vip in snat
  {{- range .Values.global.rules }}
  - eip: {{ $gwip }}
    internalCIDR: {{ .internal_ip }}/32
  {{- end }}
  dnatRules:
  {{- range .Values.global.rules }}
  - eip: {{ $gwip }}
    externalPort: "{{ .internal_port }}"
    internalIp: {{ .internal_ip }}
    internalPort: "{{ .internal_port }}"
    protocol: udp
  - eip: {{ $gwip }}
    externalPort: "{{ .internal_port }}"
    internalIp: {{ .internal_ip }}
    internalPort: "{{ .internal_port }}"
    protocol: tcp
  {{- end }}

---
apiVersion: kubeovn.io/v1
kind: VpcNatGateway
metadata:
  annotations:
    subnetGateway: {{ .Values.global.subnet.gateway }}
    boson.sensetime.com/ha-mode: {{ .Values.global.haMode }}
    boson.sensetime.com/ha-peer-ip: {{ index .Values.global.natGateway.net1Ips.rips 0 }}
    boson.sensetime.com/ha-vip: {{ .Values.global.natGateway.net1Ips.vip }}
    boson.sensetime.com/ha-monitor: {{ .Values.global.natGateway.gateway }}
    boson.sensetime.com/ha-external-vips: {{ .Values.global.natGateway.eth0Ips.vip }}
  name: {{ .Values.global.fullname }}-1
  labels:
    {{- include "defaultVpcGateway.labels" . | nindent 4 }}
spec:
  vpc: ovn-cluster
  subnet: {{ .Values.global.subnet.subnetName }}
  # use rip in lanIp
  lanIp: {{  index .Values.global.natGateway.eth0Ips.rips 1 }}
  eips:
  # use rip in eips
  - eipCIDR: {{ index .Values.global.natGateway.net1Ips.rips 1 }}/{{ .Values.global.natGateway.net1Ips.cidr }}
    gateway: {{ .Values.global.natGateway.gateway }}
  snatRules:
  {{- range .Values.global.rules }}
  # use vip in snat
  - eip: {{ $gwip }}
    internalCIDR: {{ .internal_ip }}/32
  {{- end }}
  dnatRules:
  {{- range .Values.global.rules }}
  - eip: {{ $gwip }}
    externalPort: "{{ .internal_port }}"
    internalIp: {{ .internal_ip }}
    internalPort: "{{ .internal_port }}"
    protocol: udp
  - eip: {{ $gwip }}
    externalPort: "{{ .internal_port }}"
    internalIp: {{ .internal_ip }}
    internalPort: "{{ .internal_port }}"
    protocol: tcp
  {{- end }}
