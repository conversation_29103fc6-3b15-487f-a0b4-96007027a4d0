{{- if .Values.global.subnet.createSeparatedSubnet }}
apiVersion: kubeovn.io/v1
kind: Subnet
metadata:
  name: {{ .Values.global.fullname }}-subnet
  labels:
    {{- include "defaultVpcGateway.labels" . | nindent 4 }}
spec:
  cidrBlock: {{ .Values.global.subnet.cidr }}
  default: false
  disableGatewayCheck: false
  disableInterConnection: true
  gatewayNode: ""
  gatewayType: distributed
  natOutgoing: false
  private: false
  protocol: IPv4
  provider: ovn
  vpc: ovn-cluster
{{- end }}
