
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  # product:
  product: BosonService
  # namespace defines the namespace for rdma-device-plugin daemon set to deploy
  namespace: kube-system
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
  # nodeSelectorTerms defines node labels for all nodes that contain rdma device
  nodeSelectorTerms:
  - matchExpressions:
    # plugin需要运行在有 IB 和 RoCE 训练网的机器上运行
    - key: diamond.sensetime.com/nic-training-protocol
      values:
      - IB
      - RoCE
    # 同时，plugin需要运行在有 PF 设备同容器的机器上运行
    - key: diamond.sensetime.com/nic-access-mode
      values:
      - PF
    # 4090机器Exist该标签
    - key: diamond.sensetime.com/hardware-net-data-training
      operator: NotIn
      values:
      - enabled
  # tolerations defines supported taints in nodes
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-acp
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-cci
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-ailab
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists

  # imagePullSecret: 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # rdmaDevicePluginImage defines rdma-device-plugin image and tag.
  rdmaDevicePluginImage: registry.sensetime.com/sensecore-boson/mellanox/k8s-rdma-shared-dev-plugin:v1.3.2-20231208211921
  macvlan:
    # name for macvlan network attachment definition
    enable: false
    namespace: "plat-boson-service"
    #nadName: "macvlan-training-roce"
    nadName: "macvlan-ovn-0-127"
    ovnSocketPath: "/run/openvswitch/kube-ovn-daemon.sock"
    ipamType: "kube-ovn"
    cidr: "************/25"
    gateway: "************"
    excludeIps:
    - ************..************0
    - ************20..**************
  pfvlan:
    enable: false
  # rdmaDevicePlugin variables
  rdmaDevicePlugin:
    # fullname defines name of rdma-device-plugin and references every where.
    fullname: "rdma-device-plugin"
    # periodicUpdateInterval defines the time interval in seconds to update the resources according to host devices in case of changes.
    periodicUpdateInterval: 300
    # rdmaHcaMax defines maximum number of RDMA resources that can be provided by the device plugin resource.
    rdmaHcaMax: 1024
    # devices defines the names for ib devices
    devices: ["ib0", "ib1", "ib2", "ib3", "ib4", "ib5", "ib6", "ib7"]
    # roce training devices
    rdmaRoceMax: 1
    # devices for training, only support one training roce devices
    trainingDevices: ["eth10", "eth11"]
    # device for apply dcu-cm yaml
    macVlanDevice: "eth10"
    # 4090 use data nic bond1 for train
    dataNicTrainEnabled: false
