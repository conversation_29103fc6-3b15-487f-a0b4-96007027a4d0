apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-{{ .Values.global.rdmaDevicePlugin.fullname }}-privileged-containers
  namespace: {{ .Values.global.policyExceptionNamespace }}
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - {{ .Values.global.rdmaDevicePlugin.fullname }}-*
        namespaces:
        - {{ .Values.global.namespace }}
