---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.global.rdmaDevicePlugin.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "rdmaDevicePlugin.labels" . | nindent 4 }}
data:
  config.json: |
    {
        "periodicUpdateInterval": {{ .Values.global.rdmaDevicePlugin.periodicUpdateInterval }},
        "configList": [
{{- if .Values.global.rdmaDevicePlugin.dataNicTrainEnabled }}
           {
             "resourceName": "roce",
             "resourcePrefix": "rdma-training",
             "rdmaHcaMax": {{ .Values.global.rdmaDevicePlugin.dataNicMax }},
             "selectors": {
               "vendors": {{ .Values.global.rdmaDevicePlugin.selectorVendors | toJson }},
               "deviceIDs": {{ .Values.global.rdmaDevicePlugin.selectorDeviceIDs | toJson }}
             }
           }
{{- else }}
           {
             "resourceName": "hca",
             "resourcePrefix": "rdma",
             "rdmaHcaMax": {{ .Values.global.rdmaDevicePlugin.rdmaHcaMax }},
             "selectors": {
               "ifNames": {{ .Values.global.rdmaDevicePlugin.devices | toJson }}
             }
           }
{{-   if .Values.global.pfvlan.enable -}}
           ,
           {
             "resourceName": "roce",
             "resourcePrefix": "rdma-training",
             "rdmaHcaMax": {{ .Values.global.rdmaDevicePlugin.rdmaRoceMax }},
             "selectors": {
               "ifNames": {{ .Values.global.rdmaDevicePlugin.trainingDevices | toJson }}
             }
           }
{{-   end }}
{{- end }}
        ]
    }
