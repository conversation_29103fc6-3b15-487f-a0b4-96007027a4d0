{{/*
Expand the name of the chart.
*/}}
{{- define "rdmaDevicePlugin.name" -}}
{{- default .Values.global.rdmaDevicePlugin.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "rdmaDevicePlugin.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "rdmaDevicePlugin.selectorLabels" -}}
app.kubernetes.io/name: {{ include "rdmaDevicePlugin.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "rdmaDevicePlugin.labels" -}}
helm.sh/chart: {{ include "rdmaDevicePlugin.chart" . }}
{{ include "rdmaDevicePlugin.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.rdmaDevicePlugin.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}
