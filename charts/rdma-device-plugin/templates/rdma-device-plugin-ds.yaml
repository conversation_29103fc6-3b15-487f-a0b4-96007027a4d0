apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ .Values.global.rdmaDevicePlugin.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.rdmaDevicePlugin.fullname }}
    {{- include "rdmaDevicePlugin.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      name: {{ .Values.global.rdmaDevicePlugin.fullname }}
  template:
    metadata:
      labels:
        name: {{ .Values.global.rdmaDevicePlugin.fullname }}
    spec:
      hostNetwork: true
      priorityClassName: system-node-critical
      affinity:
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              {{- if .matchExpressions }}
                {{- range $expression := .matchExpressions }}
              - key: {{ $expression.key }}
                {{- if $expression.operator }}
                operator: {{ $expression.operator }}
                {{- else }}
                operator: In
                {{- end }}
                values:
                  {{- range $value := $expression.values }}
                - {{ $value | quote }}
                  {{- end }}
                {{- end }}
              {{- else }}
              - key: {{ .key }}
                {{- if .operator }}
                operator: {{ .operator }}
                {{- else }}
                operator: In
                {{- end }}
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
              {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.rdmaDevicePlugin.fullname }}
        image: "{{ .Values.global.rdmaDevicePluginImage }}"
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        volumeMounts:
          - name: device-plugin
            mountPath: /var/lib/kubelet/
          - name: device-plugin-data
            mountPath: /data/kubelet/
          - name: {{ .Values.global.rdmaDevicePlugin.fullname }}-config
            mountPath: /k8s-rdma-shared-dev-plugin
          - name: devs
            mountPath: /dev/
      volumes:
        - name: device-plugin
          hostPath:
            path: /var/lib/kubelet/
        - name: device-plugin-data
          hostPath:
            path: /data/kubelet/
        - name: {{ .Values.global.rdmaDevicePlugin.fullname }}-config
          configMap:
            name: {{ .Values.global.rdmaDevicePlugin.fullname }}-config
            items:
            - key: config.json
              path: config.json
        - name: devs
          hostPath:
            path: /dev/
