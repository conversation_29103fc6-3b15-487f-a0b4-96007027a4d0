{{ if .Values.global.macvlan.enable }}
---
apiVersion: "k8s.cni.cncf.io/v1"
kind: NetworkAttachmentDefinition
metadata:
  name: {{ .Values.global.macvlan.nadName }}
  namespace: {{ .Values.global.macvlan.namespace }}
spec:
  config: '{
    "cniVersion": "0.3.1",
    "type": "macvlan",
     "master": "{{ .Values.global.rdmaDevicePlugin.macVlanDevice }}",
        "ipam": {
          "type": "{{ .Values.global.macvlan.ipamType }}",
          "server_socket": "{{ .Values.global.macvlan.ovnSocketPath }}",
          "provider": "{{ printf "%s.%s" .Values.global.macvlan.nadName .Values.global.macvlan.namespace }}"
        }
    }'

---
apiVersion: kubeovn.io/v1
kind: Subnet
metadata:
  name: {{ .Values.global.macvlan.nadName }}
  namespace: {{ .Values.global.macvlan.namespace }}
spec:
  protocol: IPv4
  provider: {{ printf "%s.%s" .Values.global.macvlan.nadName .Values.global.macvlan.namespace }}
  cidrBlock: {{ .Values.global.macvlan.cidr }}
  gateway: {{ .Values.global.macvlan.gateway }}
  excludeIps:
  {{- range .Values.global.macvlan.excludeIps }}
  - {{ . | quote }}
  {{- end }}
{{ end }}
