

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
        - [需要特别注意的参数](#需要特别注意的参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 daemonset](#查看-daemonset)
    - [查看运行的Pod](#查看运行的pod)
    - [查看部署结果](#查看部署结果)
    - [检查服务启动日志](#检查服务启动日志)

<!-- /TOC -->

rdma device plugin 是 Mellanox 提供的 k8s device plugin 以便将 IB 和 RoCE 的网络设备能力暴露到 k8s 容器内部的 plugin。当前使用的plugin的版本是**1.3.2**。

- Fork Plugin 源代码：<https://gitlab.bj.sensetime.com/elementary/boson/k8s-rdma-shared-dev-plugin>
- Mellanox 源代码：<https://github.com/Mellanox/k8s-rdma-shared-dev-plugin>

## 准备工作

rdma-device-plugin 组件需要部署到 Diamond TK 集群当中。与其他网络服务不同的是，该服务类似于 nvidia-device-plugin 作为基础能力服务，需要部署到 kube-system namespace 之中。

### 部署的前提条件

- Boson 服务在 namespace `kube-system` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/rdma-device-plugin
# or pull specific chart version
# helm3 pull boson/rdma-device-plugin --version x.y.z

tar -xzvf rdma-device-plugin-0.3.0.tgz

```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev11](../../envs/dev11/rdma-device-plugin/values.yaml)
- [dev44](../../envs/dev44/rdma-device-plugin/values.yaml)
- [集成测试](../../envs/int-test/rdma-device-plugin/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/rdma-device-plugin/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/prod/rdma-device-plugin/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-prod/TK/boson/rdma-device-plugin/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

#### 需要特别注意的参数

- **global.rdmaDevicePlugin.devices**: 默认是同时启用 ib0 和 ib1 两块 IB 网卡，标配的 GPU 机器都是两块IB 网卡且命名不变。如果某个非标环境，需要通过 `ip a | grep ib` 来查看具体 IB 网卡的个数和名称，并更新该参数。

## 服务部署

### 部署服务

```bash
helm3 install rdma-device-plugin rdma-device-plugin -n kube-system -f path/to/env/rdma-device-plugin/values.yaml
```

### 更新服务

```bash
helm3 upgrade rdma-device-plugin rdma-device-plugin -n kube-system -f path/to/env/rdma-device-plugin/values.yaml
```

### 卸载服务

```
helm3 uninstall rdma-device-plugin -n kube-system
```

## 检查服务状态

### 查看 daemonset

有多少台带有 IB 网卡的机器，daemonset就应该有多少个pod。

```bash
kubectl -n kube-system get daemonset
AME                             DESIRED   CURRENT   READY   UP-TO-DATE   AVAILABLE   NODE SELECTOR             AGE
rdma-device-plugin               4         4         4       4            4           <none>                    4m
```

### 查看运行的Pod

```bash
kubectl -n kube-system get pod | grep rdma-device-plugin
# NAME                            READY   STATUS      RESTARTS   AGE
rdma-device-plugin-9wddv          1/1     Running            0   3d23h
rdma-device-plugin-jm5hd          1/1     Running            0   3d23h
rdma-device-plugin-qwjt2          1/1     Running            0   3d22h
rdma-device-plugin-wnk8l          1/1     Running            0   3d22h
```

### 查看部署结果

```bash
kubectl describe node | grep "rdma/hca:"

# 节点的 Capacity 和 Allocatable 中增加了 rdma/hca
  rdma/hca:                   1024
```

### 检查服务启动日志

```bash
kubectl -n kube-system logs rdma-device-plugin-9wddv
# 2022/10/01 10:13:15 Starting K8s RDMA Shared Device Plugin version= master
# Using Deprecated Devie Plugin Registry Path
# 2022/10/01 10:13:15 resource manager reading configs
# 2022/10/01 10:13:15 Reading /k8s-rdma-shared-dev-plugin/config.json
# 2022/10/01 10:13:15 loaded config: [{ResourceName:hca ResourcePrefix:rdma RdmaHcaMax:1024 Devices:[] Selectors:{Vendors:[] DeviceIDs:[] Drivers:[] IfNames:[ib0] LinkTypes:[]}}]
# 2022/10/01 10:13:15 periodic update interval: +300
# 2022/10/01 10:13:15 Discovering host devices
# 2022/10/01 10:13:15 discovering host network devices
# 2022/10/01 10:13:15 DiscoverHostDevices(): device found: 0000:0a:00.0	02          	Intel Corporation   	I350 Gigabit Network Connection
# 2022/10/01 10:13:15 DiscoverHostDevices(): device found: 0000:0a:00.1	02          	Intel Corporation   	I350 Gigabit Network Connection
# 2022/10/01 10:13:15 DiscoverHostDevices(): device found: 0000:83:00.0	02          	Mellanox Technolo...	MT27500 Family [ConnectX-3]
# 2022/10/01 10:13:15 DiscoverHostDevices(): device found: 0000:84:00.0	02          	Mellanox Technolo...	MT27800 Family [ConnectX-5]
# 2022/10/01 10:13:15 Initializing resource servers
# 2022/10/01 10:13:15 Resource: &{ResourceName:hca ResourcePrefix:rdma RdmaHcaMax:1024 Devices:[] Selectors:{Vendors:[] DeviceIDs:[] Drivers:[] IfNames:[ib0] LinkTypes:[]}}
# 2022/10/01 10:13:15 error creating new device: "missing RDMA device spec for device 0000:0a:00.0, RDMA device \"issm\" not found"
# 2022/10/01 10:13:15 error creating new device: "missing RDMA device spec for device 0000:0a:00.1, RDMA device \"issm\" not found"
# 2022/10/01 10:13:15 Starting all servers...
# 2022/10/01 10:13:15 starting rdma/hca device plugin endpoint at: hca.sock
# 2022/10/01 10:13:15 rdma/hca device plugin endpoint started serving
# 2022/10/01 10:13:15 All servers started.
# 2022/10/01 10:13:15 Listening for term signals
# 2022/10/01 10:13:15 Starting OS watcher.
# 2022/10/01 10:13:15 Updating "rdma/hca" devices
# 2022/10/01 10:13:15 exposing "1024" devices
```
