apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.global.configmap.name }}
  namespace: {{ .Values.global.namespace }}
data:
  {{- range $iKey, $iValue := .Values.instance }}
  {{ $iValue.host }}_{{ $iValue.name }}: |-
    local_address_group {{ $iValue.local_ip.name }} {
      {{- range $iValue.local_ip.member }}
      {{ .range }} {{ .port }}
      {{- end }}
    }
    {{- range $iValue.vs_list }}
    virtual_server {{ .vip }} {{ .vport }} {
      delay_loop 6
      lb_algo {{ .lb_algo }}
      lb_kind FNAT
      connect_timeout 3000
      protocol {{ .proto }}
      laddr_group_name {{ $iValue.local_ip.name }}
      {{- range .rs}}
      real_server {{ .rip }} {{ .rport }} {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      {{- end }}
    }
    {{- end }}
  {{- end }}
  {{- range .Values.slbdp }}
  {{ .host }}_{{ .name }}: |-{{ .values | nindent 4}}
  {{- end }}
