{{/*
Expand the name of the chart.
*/}}
{{- define "boson-slbdp.name" -}}
{{- default .Values.global.name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "boson-slbdp.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "boson-slbdp.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "boson-slbdp.labels" -}}
helm.sh/chart: {{ include "boson-slbdp.chart" . }}
{{ include "boson-slbdp.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "boson-slbdp.selectorLabels" -}}
app.kubernetes.io/name: {{ include "boson-slbdp.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "boson-slbdp.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "boson-slbdp.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Service monitor template for k8s.
*/}}
{{- define "boson-slbdp.servicemonitor" -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.name }}-servicemonitor
  namespace: {{ .Values.global.namespace }}
  labels:
    prometheus: prometheus
    {{- include "boson-slbdp.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.name }}-service
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
{{- end }}
