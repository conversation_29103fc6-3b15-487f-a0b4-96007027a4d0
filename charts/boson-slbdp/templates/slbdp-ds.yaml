kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: {{ .Values.global.name }}
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.name }}
spec:
  selector:
    matchLabels:
      app-name: {{ .Values.global.name }}
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.name }}
    spec:
      hostNetwork: true
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-infra-slb-platform-dedicated: enabled
      tolerations:
      - effect: NoExecute
        key: diamond.sensetime.com/role-infra-slb-platform-dedicated
        operator: Equal
        value: enabled
      containers:
      - name: {{ .Values.global.podName }}
        image: {{ .Values.global.bosonSlbdpImage }}
        imagePullPolicy: IfNotPresent
        env:
        - name: K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        livenessProbe:
          exec:
            command:
            - /slbdp/bin/dpip
            - link
            - show
          initialDelaySeconds: 30
          timeoutSeconds: 30
          periodSeconds: 60
        readinessProbe:
          exec:
            command:
            - /slbdp/bin/dpip
            - link
            - show
          initialDelaySeconds: 30
          timeoutSeconds: 30
          periodSeconds: 60
        resources:
          limits:
            hugepages-2Mi: {{ .Values.global.hugepage2m }}
            memory: {{ .Values.global.memory }}
        securityContext:
          runAsUser: 0
          privileged: true
        volumeMounts:
        - name: usr
          mountPath: /usr
        - name: bin
          mountPath: /bin
        - name: dev
          mountPath: /dev
        - name: log
          mountPath: /var/log
        - name: mnt
          mountPath: /mnt
        - name: sys
          mountPath: /sys
        - name: etc
          mountPath: /etc
        - name: conf
          mountPath: /slbdp/conf/
      volumes:
      - name: usr
        hostPath:
          path: /usr
      - name: bin
        hostPath:
          path: /bin
      - name: dev
        hostPath:
          path: /dev
      - name: log
        hostPath:
          path: /var/log
      - name: mnt
        hostPath:
          path: /mnt
      - name: sys
        hostPath:
          path: /sys
      - name: etc
        hostPath:
          path: /etc
      - name: conf
        configMap:
          name: {{ .Values.global.configmap.name }}
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      serviceAccount: default
      serviceAccountName: default
      priorityClassName: system-cluster-critical
