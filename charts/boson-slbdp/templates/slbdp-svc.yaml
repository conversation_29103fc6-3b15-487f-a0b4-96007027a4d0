apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.name }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.name }}-service
    {{- include "boson-slbdp.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.name }}
  ports:
  - name: metrics
    port: {{ int .Values.global.service.ports.metrics }}
    protocol: TCP
