global:
  enableServiceMonitor: true
  bosonSlbdpImage: registry.sensetime.com/sensecore-boson/slbdp:v1.0-12-lower-cpu
  namespace: plat-boson-infra
  name: boson-slbdp
  podName: slbdp
  memory: 8Gi
  hugepage2m: 128Gi
  configmap:
    name: boson-slbdp
  service:
    ports:
      metrics: 5000
  fullname: "boson-slbdp"
  # policyExceptionNamespace 设置 kyverno.io 的 PolicyException 对应的 namespace
  policyExceptionNamespace: plat-diamond-pss
instance:
  - host: host-10-118-253-17-*************
    name: keepalived.conf
    local_ip:
      name: host1_localip
      member:
      - range: ***********
        port: eth60
    vs_list:
    - name: vs1
      vip: *************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
      - rip: ************
        rport: 80
    - name: vs2
      vip: *************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 443
      - rip: ************
        rport: 443
  - host: host-10-118-253-18-*************
    name: keepalived.conf
    local_ip:
      name: host2_localip
      member:
      - range: *************
        port: eth60
    vs_list:
    - name: vs1
      vip: *************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
      - rip: ************
        rport: 80
    - name: vs2
      vip: *************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 443
      - rip: ************
        rport: 443
slbdp:
  - host: host-10-118-253-17-*************
    name: slb-dp.yaml
    values: |-
      host:
        hugepage: 64
        ports:
        - pci: "0000:b1:00.0"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        - pci: "0000:e3:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth60.kni: ************/30
          eth50.kni: ***********/30
        hc_ip:
          ip: ***********/32
          nexthop: ************
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (**********/24,************,eth60)
      rule:
        kernel:
        - (all, **********/24, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
  - host: host-10-118-253-18-*************
    name: slb-dp.yaml
    values: |-
      host:
        hugepage: 64
        ports:
        - pci: "0000:b1:00.0"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        - pci: "0000:e3:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth60.kni: ************/30
          eth50.kni: ************/30
        hc_ip:
          ip: ***********30/32
          nexthop: ************
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (**********/24,************,eth60)
      rule:
        kernel:
        - (all, **********/24, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
