
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  product: BosonService
  # bosonProviderImage defines boson-provider image and tag.
  bosonProviderImage: registry.sensetime.com/sensecore-boson/boson-provider:v1.7.0-7-g08b557d-20231020085153
  # hook image
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:v1.8.0-1-g2b3dabe-20231116213725
  # namespace 设定用户部署所有组件的namespace，如果组件不在这个下面，需要特殊考虑
  namespace: plat-boson-service
  # 从镜像站下载镜像的账号，需要提前创建docker pull secret
  imagePullSecret: sensecore-boson
  # enableServiceMonitor 定义是否生成对接Diamond Prometheus 的服务的ServiceMonitor资源的yaml
  enableServiceMonitor: true
  # domainName 定义了 boson ingress 对外暴露服务的域名，根据环境配置 network-internal.cn-sh-01.sensecoreapi.cn/.tech/.dev
  domainName: network-internal.cn-sh-01.sensecoreapi.dev
  # sslSecretName 定义了 https 接口上配置的 ingress 证书 secret tls 的名字
  sslSecretName: tls-cnsh01-api
  # nodeSelectorTerms 定义服务部署所需要的node label标识
  nodeSelectorTerms:
    # 指定服务在有role-business-app的节点上运行
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  # tolerations 定义支持运行机器的node taint
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  # envName 定义当前部署环境的命名，用于日志或其他显式信息的环境标识，比如： devxx|test65|systest|prod
  envName: devxx
  # reginal的pgsql连接信息
  pgsql:
    host: xxx
    port: 12345
    user: xxx
    db: boson_service_v2
    password: ""
    show_sql: false
  # regional的mq连接信息
  rocketmq:
    default:
      nameServers:
      - "xx.xx.xx.xx:9876"
      - "xx.xx.xx.xx:9876"
      instanceName: "sensetime-core-networkDefault-v1-cn-sh-01z"
      topic: "sensetime-core-network-vpc-v1-cn-sh-01z"
      eip_topic: "sensetime-core-network-eip-v1-cn-sh-01a"
      notice_topic: "sensetime-core-message-engine-msg"
      rmAckTopic: "sensetime-core-rm-resource-state-sync"
      bossAckTopic: "sensetime-core-resource-operation-result"
      brokerConsumerGroupName: "sensetime-core-network-v1-cn-sh-01a-consumer"
      eip_consumer_group_name: "sensetime-core-network-v1-cn-sh-01a-consumer-eip"
      brokerRMProducerGroupName: "sensetime-core-network-v1-cn-sh-01a-producer-rm"
      brokerBossProducerGroupName: "sensetime-core-network-v1-cn-sh-01a-producer-boss"
      brokerNoticeProducerGroupName: "sensetime-core-network-v1-cn-sh-01a-producer-Notice"
      slb_topic: "sensetime-core-network-slb-v1-cn-sh-01a"
      slb_consumer_group_name: "sensetime-core-network-v1-cn-sh-01a-consumer-slb"
      accessKey: "rocketmq-ak-boson-provider"
      secretKey: "devxx_mq_secret_key"
    cloudAudit:
      nameServers:
      - "xx.xx.xx.xx:9876"
      - "xx.xx.xx.xx:9876"
      instanceName: "sensetime-core-networkCloudAudit-v1-cn-sh-01z"
      topics:
        eip:
          topic: "sensetime-core-trail-eip-operation"
          producerGroupName: "sensetime-core-trail-eip-producer"
          accessKey: "xxx"
          secretKey: "xxx"
        vpc:
          topic: "sensetime-core-trail-vpc-operation"
          producerGroupName: "sensetime-core-trail-vpc-producer"
          accessKey: "xxx"
          secretKey: "xxx"
        dc:
          topic: "sensetime-core-trail-dc-operation"
          producerGroupName: "sensetime-core-trail-dc-producer"
          accessKey: "xxx"
          secretKey: "xxx"
        slb:
          topic: "sensetime-core-trail-slb-operation"
          producerGroupName: "sensetime-core-trail-slb-producer"
          accessKey: "xxx"
          secretKey: "xxx"

  # reginal的redis连接信息
  redis:
    host: devxx.redis.boson.com
    port: 6379
    password: devxx_redis_pass_com
  # 【保留】发送邮件配置
  smtp:
    enable: true
    host: smtp.partner.outlook.cn
    port: 587
    username: devxx_send_from_username_com
    sender: <EMAIL>
    password: devxx_send_from_password_com

  # bosonProvider variables
  bosonProvider:
    # fullname defines name of boson-provider and references every where.
    fullname: "boson-provider"
    # replicas defines number of pods running in cluster.
    replicas: 3
    # cpuLimits defines cpu limit used by pods.
    cpuLimits: 200m
    # memLimits defines memory limit used by pods.
    memLimits: 500Mi
    # 是否在 deployment 中增加当前时间的 annotation，以便在部署是可以更新deployment 出发 rolling update
    generate_deployment_annotations_timestamp: false
    defaults:
      vpc_cidr: "**********/16"
      geneve_subnet_cidr: "***********/20"
      vlan_subnet_cidr: "***********/22"
      dc_vxlan_cidr: "**********/24"
      dc_console_cidr: "***********/24"
      zone_name_for_az_resource: "cn-sh-01a"
      zone_name_for_region_resource: "cn-sh-01z"
      region: "cn-sh-01"
      az: "cn-sh-01a"
      prp: "cn-sh-01a-prp01"
      # 分布式网关配置
      dgw:
        enable: true
        policy_cidr: "***********/24"
        external_subnet: "**********/16"
        reserved: "**********..**********,**************..**************"
      quota:
        vpcs_per_tenant: 1
        geneve_subnets_per_vpc: 1
        vlan_subnets_per_vpc: 1
        ib_subnets_per_vpc: 1
        roce_subnets_per_vpc: 1
        eips_per_vpc: 10
        nat_gateways_per_vpc: 1
        dnat_rules_per_eip: 50
        slb_per_vpc: 10
        listener_per_slb: 50
        rule_per_slb: 50
        target_group_per_slb: 50
        target_per_target_group: 50
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000
      training_networks:
        vlan_acl_nic_max_count: 0
        kube_ovn:
        - if_id: "roce_0"
          gw: "************"
        - if_id: "roce_1"
          gw: "************"
        bms:
          gws:
          - "************"
          - "************"
      cloud_audit_enable: true
      slb_enable: false
      slb_replica: 1
      bgp_enable: false
      slb_expose_basic_network_vip: false
      slb_expose_overlay_vip: false
      slb_dataplane:
        # 此处为 net-controller 的 DB 信息
        host: "xxx"
        port: "xxx"
        user: "xxx"
        password: "xxx"
        db: "xxx"
      # bms2pod macvlan子接口的父接口网卡，默认为空表示不启用该功能，为bond1时表示选用数据网卡bond1创建出macvlan子接口
      bms_master_nic: ""
      ts_gateway_nodes:
        - hostxxx
        - hostxxx
        - hostxxx
      vpc_default_acls:
        - direction: "xx"
          priority: 30000
          match: "xx"
          action: "xx"
      vpc_acls:
        - src_ip: "xx"
          src_port: "xx"
          dest_ip: "xx"
          dest_port: "xx"
          protocol: "xx"
          priority: 30000
          action: "xx"
          description: "xx"
    # envs defines environment related values.
    envs:
      # varName defines variable used for Boson Service
      varName: "boson-provider"
      rocketmqLogLevel: warn
    # service defines ports for service.
    service:
      # ports defines port numbers of pod, svc, ingress.
      ports:
        # http port number for container, service, ingress.
        http: "51080"
        # grpc port number for container, service, ingress.
        grpc: "51090"
        # metrics port number for container, service, service monitor.
        metrics: "51030"
