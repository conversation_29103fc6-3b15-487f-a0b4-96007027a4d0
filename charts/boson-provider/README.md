# Boson Provider

<!-- TOC -->

- [准备工作](#准备工作)
    - [部署的前提条件](#部署的前提条件)
    - [拉取服务 Helm Chart](#拉取服务-helm-chart)
    - [配置部署参数](#配置部署参数)
        - [需要特别注意的参数](#需要特别注意的参数)
- [服务部署](#服务部署)
    - [部署服务](#部署服务)
    - [更新服务](#更新服务)
    - [卸载服务](#卸载服务)
- [检查服务状态](#检查服务状态)
    - [查看 deployment](#查看-deployment)
    - [查看运行的Pod](#查看运行的pod)
    - [检查服务启动日志](#检查服务启动日志)
- [查看数据库信息](#查看数据库信息)
    - [DB 表初始化成功](#db-表初始化成功)
    - [DB pool 数据初始化成功](#db-pool-数据初始化成功)

<!-- /TOC -->

Boson Provider 作为 Boson 服务中北向 API 接口程序，为调用者和 RM 提供基于业务抽象的网络资源操作 API，如 VPC、EIP 的增删改查等操作。

## 准备工作

boson-provider 组件需要部署到 Diamond TK 集群当中。

### 部署的前提条件

- Boson 服务的 namespace `plat-boson-service` 已经创建。如未创建，请参考[创建 Boson 依赖的 Diamond TK 集群 namespace](../../deployment/deployment-guide.md#创建-boson-依赖的-diamond-tk-集群-namespace)
- Boson 服务在 namespace `plat-boson-service` 下面的拉取镜像的账号 `sensecore-boson` 已经创建。如未创建，请参考[创建 Boson 镜像依赖的 Docker-Registry](../../deployment/deployment-guide.md#创建-boson-镜像依赖的-docker-registry)
- Boson 服务的数据库 `boson_service_v2` 已经创建。如未创建，请参考[PostgreSql DB 创建](../../deployment/deployment-guide.md#postgresql-db-%E5%88%9B%E5%BB%BA)
- Boson 服务对应的 RocketMQ topic 对接已经创建。如未创建，请参考[RocketMQ topic 初始化](../../deployment/deployment-guide.md#rocketmq-topic-%E5%88%9D%E5%A7%8B%E5%8C%96)
- Boson 服务的 Helm Chart 镜像仓已经配置。如未配置，请参考[配置 Helm Chart 镜像仓](../../deployment/deployment-guide.md#helm-chart-仓库配置)

### 拉取服务 Helm Chart

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/boson-provider
# or pull specific chart version
# helm3 pull boson/boson-provider --version x.y.z

tar -xzvf boson-provider-x.y.z.tgz

```

### 配置部署参数

不同环境的部署参数分别放在以下位置：

- [dev22](../../envs/dev22/boson-provider/values.yaml)
- [dev11](../../envs/dev11/boson-provider/values.yaml)
- [dev44](../../envs/dev44/boson-provider/values.yaml)
- [集成测试](../../envs/int-test/boson-provider/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/blob/boson-values-4-tech/TK/boson/boson-provider/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。
- [生产环境](../../envs/int-test/boson-provider/values.yaml)
  - [SRE部署的集成测试](https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-prod/TK/boson/boson-provider/values.yaml)：SRE使用的文件内容，必须和本项目中的环境文件内容一致。

#### 需要特别注意的参数

- **global.pgsql.password**: 很多环境的密码是空的或者是加密的，因此部署前需要修改环境的数据库密码
- **global.bosonProvider.enablePoolInit**: 默认是 `false`。特别注意，仅仅当抽次创建数据库，需要初始化数据表和各种资源池数据的时候，该参数才设置为 `true`

## 服务部署

### 部署服务

```bash
helm3 install boson-provider boson-provider -n plat-boson-service -f path/to/env/boson-provider/values.yaml
```

### 更新服务

```bash
# 更新服务所用的crd
tar -xzvf boson-provider-x.y.z.tgz
kubectl apply -f boson-provider-x.y.z/crds/

# 更新服务的helm chart
helm3 upgrade boson-provider boson-provider -n plat-boson-service -f path/to/env/boson-provider/values.yaml
```

### 卸载服务

```
helm3 uninstall boson-provider -n plat-boson-service
```

## 检查服务状态

### 查看 deployment

```bash
kubectl -n plat-boson-service get deployment
# NAME             READY   UP-TO-DATE   AVAILABLE   AGE
# boson-provider   1/1     1            1           1m
```

### 查看运行的Pod

boson-provider-job 只有在 enablePoolInit: true 时候才会运行

```bash
kubectl -n plat-boson-service get pod
# NAME                            READY   STATUS      RESTARTS   AGE
# boson-provider-64464584-h5scz   1/1     Running     0          1m
# boson-provider-job-ghtkg        0/1     Completed   0          1m
```

### 检查服务启动日志

```bash
kubectl -n plat-boson-service logs boson-provider-64464584-h5scz
# [2022-09-29 07:09:42] [info] [utils/init.go:52] Initialize Resource Provider
# [2022-09-29 07:09:42] [info] [resourceprovider/resourceprovider.go:166] Recovering boson-provider
# [2022-09-29 07:09:42] [info] [resourceprovider/resourceprovider.go:173] Running boson-provider
# [2022-09-29 07:09:42] [info] [receiver/receiver.go:44] Starting sensetime-core-network-vpc-v1-cn-sh-01z Receiver
# [2022-09-29 07:09:42] [info] [receiver/receiver.go:51] Started sensetime-core-network-vpc-v1-cn-sh-01z Receiver
# [2022-09-29 07:09:42] [info] [informer/ipa.go:28] Start IpaController
# [2022-09-29 07:09:42] [info] [informer/eipRule.go:29] Start EipRuleController
# [2022-09-29 07:09:42] [info] [server/server.go:82] GRPC server start :51090
# [2022-09-29 07:09:42] [info] [informer/eip.go:29] Start EIPController
# [2022-09-29 07:09:42] [info] [server/server.go:52] HTTP server start :51080
# [2022-09-29 07:09:42] [info] [receiver/receiver.go:44] Starting sensetime-core-network-eip-v1-cn-sh-01a Receiver
# [2022-09-29 07:09:42] [info] [receiver/receiver.go:51] Started sensetime-core-network-eip-v1-cn-sh-01a Receiver
```

## 查看数据库信息

### DB 表初始化成功

```bash
# 进入 pg，能看到表
psql -U postgres -h <pg_host> -p <pg_port>
\c boson_service_v2
\d
```

### DB pool 数据初始化成功

当 enablePoolInit: true 的时候，boson-provider 会启动 init job 初始化数据库表。部署完成后，查看数据库表的各种地址池是否包含预期数据。

```bash
# pg 表 nat_gateway_external_ip_pools 里有 ip 池数据
select * from nat_gateway_external_ip_pools;

# pg 表 eip_pools 里有 ip 池数据
select * from eip_pools;

# pg 表 cidr_pools 里有 TRAINING、DATA、SERVICE 的网段配置数据
select * from cidr_pools;
```
