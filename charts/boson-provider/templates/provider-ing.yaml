# HTTP ingress host and path couldn't be same to GRPC ingress, and GRPC ingress path must be /, so this path is /network.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-service-ingress
  namespace: {{ .Values.global.namespace }}
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    {{- include "bosonProvider.labels" . | nindent 4 }}
spec:
  rules:
  - host: {{ .Values.global.domainName }}
    http:
      paths:
      - path: /network
        pathType: Prefix
        backend:
          service:
            name: {{ .Values.global.bosonProvider.fullname }}-service
            port:
              number: {{ int .Values.global.bosonProvider.service.ports.http }}
  tls:
  - hosts:
    - {{ .Values.global.domainName }}
    secretName: {{ .Values.global.sslSecretName }}
