{{- $grpcPort := .Values.global.bosonProvider.service.ports.grpc -}}
{{- $httpPort := .Values.global.bosonProvider.service.ports.http -}}
{{- $metricsPort := .Values.global.bosonProvider.service.ports.metrics -}}
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: {{ .Values.global.envName }}
    pg:
      host: {{ .Values.global.pgsql.host }}
      port: {{ .Values.global.pgsql.port | quote }}
      user: {{ .Values.global.pgsql.user }}
      password: {{ .Values.global.pgsql.password }}
      db: {{ .Values.global.pgsql.db }}
      show_sql: {{ .Values.global.pgsql.show_sql }}

    # regional的mq连接信息
    rocketmq:
      default:
        nameServer:
        {{- range .Values.global.rocketmq.default.nameServers }}
        - {{ . | quote }}
        {{- end }}
        instanceName: {{ .Values.global.rocketmq.default.instanceName }}
        topic: {{ .Values.global.rocketmq.default.topic }}
        eip_topic: {{ .Values.global.rocketmq.default.eip_topic }}
        notice_topic: {{ .Values.global.rocketmq.default.notice_topic }}
        rmAckTopic: {{ .Values.global.rocketmq.default.rmAckTopic }}
        bossAckTopic: {{ .Values.global.rocketmq.default.bossAckTopic }}
        brokerConsumerGroupName: {{ .Values.global.rocketmq.default.brokerConsumerGroupName }}
        eip_consumer_group_name: {{ .Values.global.rocketmq.default.eip_consumer_group_name }}
        brokerRMProducerGroupName: {{ .Values.global.rocketmq.default.brokerRMProducerGroupName }}
        brokerBossProducerGroupName: {{ .Values.global.rocketmq.default.brokerBossProducerGroupName }}
        brokerNoticeProducerGroupName: {{ .Values.global.rocketmq.default.brokerNoticeProducerGroupName }}
        slb_topic: {{ .Values.global.rocketmq.default.slb_topic }}
        slb_consumer_group_name: {{ .Values.global.rocketmq.default.slb_consumer_group_name }}
        accessKey: {{ .Values.global.rocketmq.default.accessKey }}
        secretKey: {{ .Values.global.rocketmq.default.secretKey }}
      cloudAudit:
        nameServer:
        {{- range .Values.global.rocketmq.cloudAudit.nameServers }}
        - {{ . | quote }}
        {{- end }}
        instanceName: {{ .Values.global.rocketmq.cloudAudit.instanceName }}
        topics:
          eip:
            topic: {{ .Values.global.rocketmq.cloudAudit.topics.eip.topic }}
            producerGroupName: {{ .Values.global.rocketmq.cloudAudit.topics.eip.producerGroupName }}
            accessKey: {{ .Values.global.rocketmq.cloudAudit.topics.eip.accessKey }}
            secretKey: {{ .Values.global.rocketmq.cloudAudit.topics.eip.secretKey }}
          vpc:
            topic: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.topic }}
            producerGroupName: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.producerGroupName }}
            accessKey: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.accessKey }}
            secretKey: {{ .Values.global.rocketmq.cloudAudit.topics.vpc.secretKey }}
          dc:
            topic: {{ .Values.global.rocketmq.cloudAudit.topics.dc.topic }}
            producerGroupName: {{ .Values.global.rocketmq.cloudAudit.topics.dc.producerGroupName }}
            accessKey: {{ .Values.global.rocketmq.cloudAudit.topics.dc.accessKey }}
            secretKey: {{ .Values.global.rocketmq.cloudAudit.topics.dc.secretKey }}
          slb:
            topic: {{ .Values.global.rocketmq.cloudAudit.topics.slb.topic }}
            producerGroupName: {{ .Values.global.rocketmq.cloudAudit.topics.slb.producerGroupName }}
            accessKey: {{ .Values.global.rocketmq.cloudAudit.topics.slb.accessKey }}
            secretKey: {{ .Values.global.rocketmq.cloudAudit.topics.slb.secretKey }}

    # reginal的redis连接信息
    redis:
      host: {{ .Values.global.redis.host }}
      port: {{ .Values.global.redis.port }}
      password: {{ .Values.global.redis.password }}

    # 【保留】发送邮件配置
    smtp:
      enable: {{ .Values.global.smtp.enable }}
      host: {{ .Values.global.smtp.host }}
      port: {{ .Values.global.smtp.port }}
      username: {{ .Values.global.smtp.username }}
      password: {{ .Values.global.smtp.password }}
      sender: {{ .Values.global.smtp.sender }}

    # Provider的初始化地址池参数
    {{- with .Values.global.bosonProvider.init_pool }}
    init_pool:
      nat_gateways:
        cidr: {{ .nat_gateways.cidr }}
        gateway: {{ .nat_gateways.gateway }}
        ips:
        {{- range .nat_gateways.ips }}
        - {{ . | quote }}
        {{- end }}
      eips:
        cidr: {{ .eips.cidr }}
        gateway: {{ .eips.gateway }}
        ips:
        {{- range .eips.ips }}
        - {{ . | quote }}
        {{- end }}
        {{- if .eips.sku }}
        sku: {{ .eips.sku }}
        {{- end }}
      cidr_pools:
        ib:
        {{- range .cidr_pools.ib }}
        - cidr: {{ .cidr }}
          gateway: {{ .gateway }}
          zone_prp: {{ .zone_prp }}
          vni: {{ .vni }}
          scope: {{ .scope }}
          reserved: {{ .reserved }}
        {{- end }}
        vlan:
        {{- range .cidr_pools.vlan }}
        - cidr: {{ .cidr }}
          gateway: {{ .gateway }}
          zone_prp: {{ .zone_prp }}
          vni: {{ .vni }}
          scope: {{ .scope }}
          reserved: {{ .reserved }}
        {{- end }}
      slbs:
        cidr: {{ .slbs.cidr }}
        gateway: {{ .slbs.gateway }}
        ips:
        {{- range .slbs.ips }}
        - {{ . | quote }}
        {{- end }}
    {{- end }}

    # Provider的默认参数
    {{- with .Values.global.bosonProvider.defaults }}
    boson_default:
      vpc_cidr: {{ .vpc_cidr }}
      geneve_subnet_cidr: {{ .geneve_subnet_cidr }}
      vlan_subnet_cidr: {{ .vlan_subnet_cidr }}
      dc_vxlan_cidr: {{ .dc_vxlan_cidr }}
      dc_console_cidr: {{ .dc_console_cidr }}
      vpc_default_az: {{ .zone_name_for_az_resource }}
      vpc_default_region: {{ .zone_name_for_region_resource }}
      grpc_port: {{ $grpcPort }}
      http_port: {{ $httpPort }}
      metrics_port: {{ $metricsPort }}
      region: {{ .region }}
      az: {{ .az }}
      prp: {{ .prp }}
      dgw:
        enable: {{ .dgw.enable }}
        external_subnet: {{ .dgw.external_subnet }}
        resered: {{ .dgw.reserved }}
        policy_cidr: {{ .dgw.policy_cidr }}
        infra_ns: "plat-boson-infra"
      quota:
        vpcs_per_tenant: {{ .quota.vpcs_per_tenant }}
        geneve_subnets_per_vpc: {{ .quota.geneve_subnets_per_vpc }}
        vlan_subnets_per_vpc: {{ .quota.vlan_subnets_per_vpc }}
        ib_subnets_per_vpc: {{ .quota.ib_subnets_per_vpc }}
        roce_subnets_per_vpc: {{ .quota.roce_subnets_per_vpc }}
        eips_per_vpc: {{ .quota.eips_per_vpc }}
        nat_gateways_per_vpc: {{ .quota.nat_gateways_per_vpc }}
        dnat_rules_per_eip: {{ .quota.dnat_rules_per_eip }}
        slb_per_vpc: {{ .quota.slb_per_vpc }}
        listener_per_slb: {{ .quota.listener_per_slb }}
        rule_per_slb: {{ .quota.rule_per_slb }}
        target_group_per_slb: {{ .quota.target_group_per_slb }}
        target_per_target_group: {{ .quota.target_per_target_group }}
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: {{ .slow_ops_threshold_milli_seconds_config.http_request_rt }}
        mq_request_rt: {{ .slow_ops_threshold_milli_seconds_config.mq_request_rt }}
        db_request_rt: {{ .slow_ops_threshold_milli_seconds_config.db_request_rt }}
        k8s_request_rt: {{ .slow_ops_threshold_milli_seconds_config.k8s_request_rt }}
      training_networks:
        vlan_acl_nic_max_count: {{ .training_networks.vlan_acl_nic_max_count }}
        kube_ovn:
        {{- range .training_networks.kube_ovn }}
        - if_id: {{ .if_id }}
          gw: {{ .gw }}
        {{- end }}
        bms:
          gws:
          {{- range .training_networks.bms.gws }}
          - {{ . }}
          {{- end }}
      cloud_audit_enable: {{ .cloud_audit_enable }}
      slb_enable: {{ .slb_enable }}
      slb_replica: {{ .slb_replica }}
      bgp_enable: {{ .bgp_enable }}
      slb_expose_basic_network_vip: {{ .slb_expose_basic_network_vip }}
      slb_expose_overlay_vip: {{ .slb_expose_overlay_vip }}
      slb_dataplane:
        # 此处为 net-controller 的 DB 信息
        host: {{ .slb_dataplane.host }}
        port: {{ .slb_dataplane.port }}
        user: {{ .slb_dataplane.user }}
        password: {{ .slb_dataplane.password }}
        db: {{ .slb_dataplane.db }}
      # bms2pod macvlan子接口的父接口网卡，默认为空表示不启用该功能，为bond1时表示选用数据网卡bond1创建出macvlan子接口
      bms_master_nic: {{ .bms_master_nic | quote }}
      ts_gateway_nodes:
      {{- range .ts_gateway_nodes }}
        - {{ . }}
      {{- end }}
      vpc_default_acls:
      {{- range .vpc_default_acls }}
        - direction: {{ .direction }}
          priority: {{ .priority }}
          match: {{ .match }}
          action: {{ .action }}
      {{- end }}
      vpc_acls:
      {{- range .vpc_acls }}
        - src_ip: {{ .src_ip | default "" | quote }}
          src_port: {{ .src_port | default "" | quote }}
          dest_ip: {{ .dest_ip | default "" | quote }}
          dest_port: {{ .dest_port | default "" | quote }}
          protocol: {{ .protocol | default "all" }}
          priority: {{ .priority }}
          action: {{ .action }}
          description: {{ .description }}
      {{- end }}
    {{- end }}
kind: ConfigMap
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "bosonProvider.labels" . | nindent 4 }}
