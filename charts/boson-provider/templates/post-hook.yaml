apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-post-hook
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonProvider.fullname }}-post-hook
    {{- include "bosonProvider.labels" . | nindent 4 }}
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": post-upgrade,post-install
    "helm.sh/hook-weight": "3"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 10
  completions: 1
  parallelism: {{ int .Values.global.bosonProvider.replicas }}
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: {{ .Values.global.bosonProvider.fullname }}-post-hook
    spec:
      serviceAccount: {{ .Values.global.bosonProvider.fullname }}-hook
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonProvider.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Never
      containers:
      - name: {{ .Values.global.bosonProvider.fullname }}-post-hook
        image: "{{ .Values.global.hookImage }}"
        command:
        - /bin/bash
        - -c
        - |
          echo "waiting all provider replica upgrade finish..."
          rs=$(kubectl describe deploy {{ .Values.global.bosonProvider.fullname }} -n {{ .Values.global.namespace }} | grep "NewReplicaSet:" | awk '{print $2}')
          echo "got NewReplicaSet ${rs}"

          max_attempts=300
          sleep_second=6
          for ((i=0; i<max_attempts; i++)); do
            rs_desired=$(kubectl get rs "${rs}" -n {{ .Values.global.namespace }} --no-headers | awk '{print $2}' | xargs)
            rs_ready=$(kubectl get rs "${rs}" -n {{ .Values.global.namespace }} --no-headers | awk '{print $4}' | xargs)
            if [[ "${rs_desired}" == "${rs_ready}" ]]; then
              echo "replica ready=${rs_ready}, desired=${rs_desired}, upgrade finish!!!"
              break
            fi

            echo "replica ready=${rs_ready}, desired=${rs_desired}, attempts=${i}, retry"
            sleep ${sleep_second}
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: check replica timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          max_attempts=300
          sleep_second=6
          url="https://{{ .Values.global.domainName }}/network/healthz"
          for ((i=0; i<max_attempts; i++)); do
            echo "health check url '${url}'"
            http_code=$(curl -k -s -o /dev/null -w "%{http_code}" "${url}")
            if [[ ${http_code} -eq 200 ]]; then
              echo "http status code is 200, healthz check success"
              break
            fi

            echo "http status code is ${http_code}, healthz check fail, attempts=${i}, retry"
            sleep ${sleep_second}
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: health check url '${url}' timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          # sleep a while for provider acquire lease
          for ((i=0; i<10; i++)); do
            leader_pod=$(kubectl get leases boson-provider -n {{ .Values.global.namespace }} --no-headers | awk '{print $2}')
            if [[ -z ${leader_pod} ]]; then
              echo "Error: not found boson-provider leader pod"
              exit 1
            fi
            if [[ "${leader_pod}" =~ "${rs}" ]]; then
              echo "got new leader pod ${leader_pod}"
              break
            else
              sleep 6
            fi
          done

          kubectl logs "${leader_pod}" -n {{ .Values.global.namespace }} | grep "I am the new leader"
          if [[ $? -eq 0 ]]; then
            echo "check leader ${leader_pod} log success"
            exit 0
          fi
          echo "Error: leader logs were not found in leader's pod ${leader_pod}"
          exit 1
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
