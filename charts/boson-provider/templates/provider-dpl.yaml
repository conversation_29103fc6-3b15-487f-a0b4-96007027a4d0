apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}
  namespace: {{ .Values.global.namespace }}
  labels:
    name: {{ .Values.global.bosonProvider.fullname }}
    {{- include "bosonProvider.labels" . | nindent 4 }}
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonProvider.fullname }}
  replicas: {{ int .Values.global.bosonProvider.replicas }}
  template:
    metadata:
      labels:
        app-name: {{ .Values.global.bosonProvider.fullname }}
{{- if .Values.global.bosonProvider.generate_deployment_annotations_timestamp }}
      annotations:
        timestamp: {{ now | date "2006-01-02T15:04:05" | quote }}
{{- end }}
    spec:
      serviceAccount: {{ .Values.global.bosonProvider.fullname }}
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - {{ .Values.global.bosonProvider.fullname }}
            topologyKey: kubernetes.io/hostname
{{- with .Values.global.nodeSelectorTerms }}
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            {{- range . }}
            - matchExpressions:
              - key: {{ .key | quote }}
                operator: In
                values:
                {{- range .values }}
                - {{ . | quote }}
                {{- end }}
            {{- end }}
{{- end }}
{{- with .Values.global.tolerations }}
      tolerations: #设置容忍性
      {{- range . }}
      - key: {{ .key }}
        effect: {{ .effect }}
        operator: {{ .operator }}
        {{- if .value }}
        value: {{ .value | quote }}
        {{- end }}
      {{- end }}
{{- end }}
{{- with .Values.global.imagePullSecret }}
      imagePullSecrets:
      - name: {{ toYaml . }}
{{- end }}
      restartPolicy: Always
      containers:
      - name: {{ .Values.global.bosonProvider.fullname }}
        image: "{{ .Values.global.bosonProviderImage }}"
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: {{ int .Values.global.bosonProvider.service.ports.http }}
          protocol: TCP
        - name: grpc
          containerPort: {{ int .Values.global.bosonProvider.service.ports.grpc }}
          protocol: TCP
        - name: metrics
          containerPort: {{ int .Values.global.bosonProvider.service.ports.metrics }}
          protocol: TCP
        resources:
          limits:
            cpu: {{ .Values.global.bosonProvider.cpuLimits }}
            memory: {{ .Values.global.bosonProvider.memLimits }}
          requests:
            cpu: 100m
            memory: 100Mi
        readinessProbe:
          tcpSocket:
            port: {{ int .Values.global.bosonProvider.service.ports.http }}
        livenessProbe:
          tcpSocket:
            port: {{ int .Values.global.bosonProvider.service.ports.http }}
          initialDelaySeconds: 10
        env:
        - name: ROCKETMQ_GO_LOG_LEVEL
          value: {{ .Values.global.bosonProvider.envs.rocketmqLogLevel }}
        - name: BOSON_PROVIDER_VAR_NAME
          value: "{{ .Values.global.bosonProvider.envs.varName }}"
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /{{ .Values.global.bosonProvider.fullname }}.yaml
          subPath: path/to/{{ .Values.global.bosonProvider.fullname }}.yaml
      volumes:
      - configMap:
          name: {{ .Values.global.bosonProvider.fullname }}-config
          items:
          - key: {{ .Values.global.bosonProvider.fullname }}.yaml
            path: path/to/{{ .Values.global.bosonProvider.fullname }}.yaml
        name: config
