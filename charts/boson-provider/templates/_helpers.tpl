{{/*
Expand the name of the chart.
*/}}
{{- define "bosonProvider.name" -}}
{{- default .Values.global.bosonProvider.fullname .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bosonProvider.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "bosonProvider.selectorLabels" -}}
app.kubernetes.io/name: {{ include "bosonProvider.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "bosonProvider.labels" -}}
helm.sh/chart: {{ include "bosonProvider.chart" . }}
{{ include "bosonProvider.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
app.kubernetes.io/component: {{ .Values.global.bosonProvider.fullname }}
app.kubernetes.io/part-of: {{ .Values.global.product }}
{{- end }}

{{/*
Service template for k8s.
*/}}
{{- define "bosonProvider.svc" -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-service
  namespace: {{ .Values.global.namespace }}
  labels:
    app-name: {{ .Values.global.bosonProvider.fullname }}-service
    {{- include "bosonProvider.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  selector:
    app-name: {{ .Values.global.bosonProvider.fullname }}
  ports:
{{- end -}}

{{/*
Service monitor template for k8s.
*/}}
{{- define "bosonProvider.servicemonitor" -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Values.global.bosonProvider.fullname }}-servicemonitor
  namespace: {{ .Values.global.namespace }}
  labels:
    k8s-app: http
    prometheus: prometheus
    {{- include "bosonProvider.labels" . | nindent 4 }}
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: {{ .Values.global.bosonProvider.fullname }}-service
  namespaceSelector:
    matchNames:
    - {{ .Values.global.namespace }}
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
{{- end }}
