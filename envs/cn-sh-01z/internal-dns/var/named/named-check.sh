#!/usr/bin/bash

# 设置配置文件权限
chown named:named /etc/named.conf
chown -R named:named /var/named/zones
chmod -R 640 /var/named/zones/*.db

# 校验/etc/named.conf配置文件正确性，如果正常则不会输出任何日志，有问题会报错
named-checkconf
# 测试加载named.conf中定义的所有zone的配置文件
# 如果正常的主节点会输出 loaded serial 的字样，有问题会报错
# 而备节点正常不会输出任何日志，有问题会报错
named-checkconf -z
# zone aoss-internal.cn-sh-01b.sensecoreapi-oss.cn/IN: loaded serial 2023112700
# zone aoss-internal.cn-sh-01c.sensecoreapi-oss.cn/IN: loaded serial 2023112700
# zone aoss-internal.cn-sh-01d.sensecoreapi-oss.cn/IN: loaded serial 2023112700


# 重新加载所有配置
# systemctl reload named

# 请勿使用，仅供参考。非必要情况下，不要重启named服务
# systemctl restart named
