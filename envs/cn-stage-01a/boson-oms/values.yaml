global:
  region: cn-stage-01
  zone: cn-stage-01a
  namespace: plat-boson-infra
  envName: prod-cn-stage-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal-stage.cn-sh-01.sensecoreapi.cn
  sslSecretName: tls-cnsh01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-stage-01
    k8sInfo:
      enabled: True
      region: cn-stage-01
      dryrun: False
      env: stage
      kubeConfigs:
      - zone: cn-stage-01a
        region: cn-stage-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-stage-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: https://network-service-stage.cn-sh-01.sensecoreapi.cn/network/boson-ndc
      southDB:
        host: ************
        port: 43801
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: ************
        port: 43801
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-stage-01z
          host: network-internal-stage.cn-sh-01.sensecoreapi.cn
        - zone: cn-stage-01a
          host: network-internal-stage.cn-sh-01.sensecoreapi.cn
