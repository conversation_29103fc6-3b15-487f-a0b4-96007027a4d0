\c boson_service_v2;

/*
# 生成云联电信 EIP 数据库插入语句
    for eip_d in $(seq 118 127); do
        echo "INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('$(uuidgen)','14.103.16.${eip_d}','YUNLIAN_BGP_INTERNAL',false,'','');"
    done
*/
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('a78bdd4d-b442-4f81-814b-d54be85a649d','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('75605153-8d07-4cfe-9699-cf5b85ab0e22','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('f3e5ece3-a988-440b-b5a0-c06ef350026f','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('7cabd86b-41b4-4038-995f-b30deff6a653','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('a682e59d-624a-41c3-9415-ddb9801edff6','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('61875777-4da0-4f3a-83e0-53a319ac79d4','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('37b6d129-b407-4980-986a-adc3a8f8ae1f','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('545a2396-7fe7-46aa-8569-ae078712c6dd','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('bcd0ab1e-683a-4e7e-9200-35cd3805f1a5','*************','YUNLIAN_BGP_INTERNAL',false,'','');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('31b864c4-2070-40ad-bae9-190d055dc313','*************','YUNLIAN_BGP_INTERNAL',false,'','');
