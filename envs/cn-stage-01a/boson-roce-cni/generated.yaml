---
# Source: boson-roce-cni/templates/boson-roce-cni-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-roce-cni
  namespace: plat-boson-infra
---
# Source: boson-roce-cni/templates/pfc-ecn-cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ecn-optimize-config
  namespace: plat-boson-infra
  labels:
    helm.sh/chart: boson-roce-cni-1.20.0
    app.kubernetes.io/name: boson-roce-cni
    app.kubernetes.io/instance: boson-roce-cni
    app.kubernetes.io/version: 1.20.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-roce-cni
    app.kubernetes.io/part-of: BosonService
data:
  config.json: |
    {
        "configList": [
            {
            "series": "ROCE-400G",
                "match_bash": "lspci -nn | grep 15b3:1021",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 100,
                "rpg_byte_reset": 16384,
                "rpg_min_dec_fac": 1,
                "rpg_time_reset": 50
            },
            {
                "series": "V100+RoCE+MSN4600+100G",
                "match_bash": "lspci -nn |grep 10de:1df2",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 5,
                "rpg_byte_reset": 32767,
                "rpg_min_dec_fac": 100,
                "rpg_time_reset": 50
            },
            {
                "series": "dcu",
                "match_bash": "lspci | grep Z100",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 5,
                "rpg_byte_reset": 32767,
                "rpg_min_dec_fac": 100,
                "rpg_time_reset": 50
            },
            {
                "series": "default",
                "match_bash": "echo default",
                "rate_reduce_monitor_period": 20,
                "rpg_ai_rate": 100,
                "rpg_byte_reset": 16384,
                "rpg_min_dec_fac": 1,
                "rpg_time_reset": 50
            }
        ]
    }
---
# Source: boson-roce-cni/templates/boson-roce-cni-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-roce-cni
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-roce-cni/templates/boson-roce-cni-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-roce-cni
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-roce-cni
subjects:
- kind: ServiceAccount
  name: boson-roce-cni
  namespace: plat-boson-infra
---
# Source: boson-roce-cni/templates/boson-roce-cni-ds.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    kubernetes.io/description: |
      This daemon set launches the boson-roce-cni daemon.
  name: boson-roce-cni
  namespace: plat-boson-infra
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: boson-roce-cni
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: boson-roce-cni
        component: network
        type: infra
    spec:
      serviceAccount: boson-roce-cni
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/nic-access-mode"
                operator: In
                values:
                - "PF"
              - key: "diamond.sensetime.com/nic-training-protocol"
                operator: In
                values:
                - "RoCE"
      containers:
        - args:
            - --debug
          command:
            - bash
            - /opt/boson-roce-cni/start-daemon.sh
            - --enable-cross-nic=true
          env:
            - name: DBUS_SYSTEM_BUS_ADDRESS
              value: unix:path=/host/var/run/dbus/system_bus_socket
          image: registry.sensetime.com/sensecore-boson/boson-roce-cni:v1.1.7
          imagePullPolicy: Always
          name: cni-server
          livenessProbe:
            exec:
              command:
              - /usr/bin/roce-ctl
              - health_check
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 5
          readinessProbe:
            exec:
              command:
              - /usr/bin/roce-ctl
              - health_check
            failureThreshold: 3
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 200m
              memory: 200Mi
          securityContext:
            privileged: true
            runAsUser: 0
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /lib/modules
              name: host-modules
              readOnly: true
            - mountPath: /etc/host-network-scripts
              name: host-network-scripts
              readOnly: true
            - mountPath: /etc/roce-cni
              name: host-etc
            - mountPath: /run/roce-cni
              name: host-run
            - mountPath: /var/run/netns
              mountPropagation: HostToContainer
              name: host-ns
            - mountPath: /var/log/roce-cni
              name: host-log
            - mountPath: /host/var/run/dbus
              name: host-dbus
            - mountPath: /etc/localtime
              name: localtime
            - name: ecn-optimize-config-volume
              mountPath: /etc/ecn-config
      dnsPolicy: ClusterFirst
      hostNetwork: true
      hostPID: true
      imagePullSecrets:
        - name: sensecore-boson
      initContainers:
        - command:
            - bash
            - /opt/boson-roce-cni/install-cni.sh
          image: registry.sensetime.com/sensecore-boson/boson-roce-cni:v1.1.7
          imagePullPolicy: Always
          name: install-cni
          resources: {}
          securityContext:
            privileged: true
            runAsUser: 0
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /opt/cni/bin
              name: cni-bin
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoExecute
        operator: Exists
      - key: diamond.sensetime.com/role-business-acp
        effect: NoExecute
        operator: Equal
        value: "enabled"
      volumes:
        - hostPath:
            path: /opt/cni/bin
            type: ""
          name: cni-bin
        - hostPath:
            path: /lib/modules
            type: ""
          name: host-modules
        - hostPath:
            path: /etc/sysconfig/network-scripts
            type: ""
          name: host-network-scripts
        - hostPath:
            path: /etc/roce-cni
            type: ""
          name: host-etc
        - hostPath:
            path: /run/roce-cni
            type: ""
          name: host-run
        - hostPath:
            path: /var/run/netns
            type: ""
          name: host-ns
        - hostPath:
            path: /var/log/roce-cni
            type: ""
          name: host-log
        - hostPath:
            path: /var/run/dbus
            type: ""
          name: host-dbus
        - hostPath:
            path: /etc/localtime
            type: ""
          name: localtime
        - name: ecn-optimize-config-volume
          configMap:
            name: ecn-optimize-config
            items:
              - key: config.json
                path: ecn-optimize-config
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
---
# Source: boson-roce-cni/templates/boson-roce-cni-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-roce-cni-host-namespaces
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-roce-cni-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-roce-cni/templates/boson-roce-cni-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-roce-cni-host-path
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-roce-cni-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-roce-cni/templates/boson-roce-cni-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-roce-cni-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-roce-cni-*
        namespaces:
        - plat-boson-infra
