#!/usr/bin/env bash
set -beou pipefail
cd "$(dirname "${0}")"
# shellcheck source=/dev/null
source comm_libs.sh

function backup_coredns_cm(){
    timestamp=$(date +%Y-%m-%d_%H-%M-%S)
    file_path="coredns-cm-${timestamp}-backup.yaml"
    sys_exec kubectl get cm coredns -n kube-system -o yaml > "${file_path}"
    echo "Backup coredns cm to ${file_path}"
}

function restore_coredns_cm(){
    backup_file="${1}"
    read -r -p "Are you sure to restore coredns cm from ${backup_file}? [Y/n]" confirm
    if [[ "${confirm}" == "Y" ]]; then
        sys_exec kubectl apply -f "${backup_file}"
        echo "Restore coredns cm from ${backup_file}"
    else
        echo "Skip restore coredns cm"
    fi
}

function update_coredns_cm(){
    backup_coredns_cm
    file_path="coredns-cm-new.yaml"
    sys_exec bash coredns_cm_create.sh | tee "${file_path}"
    echo
    read -r -p "Are you sure to update coredns cm above? [Y/n]" confirm
    if [[ "${confirm}" == "Y" ]]; then
        sys_exec kubectl apply -f ${file_path}
        echo "Updated coredns cm from ${file_path}"
    else
        echo "Skip update coredns cm"
    fi
}

function print_help(){
    echo "Usage: $0 [backup|restore|update]"
    echo "    backup: Backup coredns cm"
    echo "    restore: Restore coredns cm from backup file"
    echo "    update: Update coredns cm"
}

OPERATION=""
if [[ -n "${1-}" ]]; then
    OPERATION="${1}"
else
    print_help
    exit 1
fi

if [[ "${OPERATION}" == "backup" ]]; then
    backup_coredns_cm
elif [[ "${OPERATION}" == "restore" ]]; then
    if [[ -z "${2-}" ]]; then
        echo "Invalid argument"
        echo "    Usage: $0 restore [backup_file]"
        exit 1
    elif [[ ! -f "${2}" ]]; then
        echo "File not found: ${2}"
        exit 1
    fi
    restore_coredns_cm "${2}"
elif [[ "${OPERATION}" == "update" ]]; then
    update_coredns_cm
else
    print_help
    exit 1
fi
