---
# Source: coredns/templates/coredns-sa.yml.j2
apiVersion: v1
kind: ServiceAccount
metadata:
  name: coredns
  namespace: kube-system
  labels:
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
---
# Source: coredns/templates/coredns-config.yml.j2
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
    helm.sh/chart: coredns-v1.11.3
    app.kubernetes.io/name: coredns
    app.kubernetes.io/instance: coredns
    app.kubernetes.io/version: v1.11.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: coredns
    app.kubernetes.io/part-of: coredns
data:
  Corefile: |
    .:53 {
        errors
        health {
            lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
            pods insecure
            ttl 30
            fallthrough in-addr.arpa ip6.arpa
        }
        prometheus :9153
        file /etc/coredns/sensecoreapi-oss.cn.db aoss-internal.cn-sh-01.sensecoreapi-oss.cn archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn
        file /etc/coredns/sensecoreapi.cn.db afs-internal-forward.cn-sh-01e-ec01.sensecoreapi.cn
        forward . /etc/resolv.conf {
            prefer_udp
            max_concurrent 2000
        }
        cache 30 {
            prefetch 5 30m 10%
            serve_stale 24h immediate
        }
        loop
        reload
        loadbalance
    }
  sensecoreapi-oss.cn.db: |
    $ORIGIN sensecoreapi-oss.cn.                        ; designates the start of this zone file in the namespace
    $TTL 1h                                             ; default expiration time of all resource records without their own TTL value
    ; ============================== Resource Records ==============================
    @                                       IN  SOA     kube-dns.kube-system.svc.cluster.local. hostmaster (
                                            2613213670  ; Serial
                                            1d          ; Refresh
                                            2h          ; Retry
                                            4w          ; Expire
                                            1h)         ; Minimum TTL
    @                                       IN  NS      kube-dns.kube-system.svc.cluster.local.    ; Name server
    aoss-internal.cn-sh-01                  IN  A       **************
    *.aoss-internal.cn-sh-01                IN  A       **************
    aoss-internal.cn-sh-01                  IN  A       **************
    *.aoss-internal.cn-sh-01                IN  A       **************
    aoss-internal.cn-sh-01                  IN  A       **************
    *.aoss-internal.cn-sh-01                IN  A       **************
    archive-aoss-internal.cn-sh-01          IN  A       **************
    *.archive-aoss-internal.cn-sh-01        IN  A       **************
    archive-aoss-internal.cn-sh-01          IN  A       10.118.184.233
    *.archive-aoss-internal.cn-sh-01        IN  A       10.118.184.233
    archive-aoss-internal.cn-sh-01          IN  A       **************
    *.archive-aoss-internal.cn-sh-01        IN  A       **************
  sensecoreapi.cn.db: |
    $ORIGIN sensecoreapi.cn.                            ; designates the start of this zone file in the namespace
    $TTL 1h                                             ; default expiration time of all resource records without their own TTL value
    ; ============================== Resource Records ==============================
    @                                       IN  SOA     kube-dns.kube-system.svc.cluster.local. hostmaster (
                                            4242725151  ; Serial
                                            1d          ; Refresh
                                            2h          ; Retry
                                            4w          ; Expire
                                            1h)         ; Minimum TTL
    @                                       IN  NS      kube-dns.kube-system.svc.cluster.local.    ; Name server
    afs-internal-forward.cn-sh-01e-ec01     IN  A       **************
    afs-internal-forward.cn-sh-01e-ec01     IN  A       **************
    afs-internal-forward.cn-sh-01e-ec01     IN  A       **************
---
# Source: coredns/templates/coredns-clusterrole.yml.j2
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
    addonmanager.kubernetes.io/mode: Reconcile
  name: system:coredns
rules:
- apiGroups:
  - ""
  resources:
  - endpoints
  - services
  - pods
  - namespaces
  verbs:
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - nodes
  verbs:
  - get
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - list
  - watch
---
# Source: coredns/templates/coredns-clusterrolebinding.yml.j2
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    rbac.authorization.kubernetes.io/autoupdate: "true"
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
    addonmanager.kubernetes.io/mode: EnsureExists
  name: system:coredns
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:coredns
subjects:
  - kind: ServiceAccount
    name: coredns
    namespace: kube-system
---
# Source: coredns/templates/coredns-svc.yml.j2
apiVersion: v1
kind: Service
metadata:
  name: coredns
  namespace: kube-system
  labels:
    k8s-app: kube-dns
    kubernetes.io/name: "coredns"
    addonmanager.kubernetes.io/mode: Reconcile
    helm.sh/chart: coredns-v1.11.3
    app.kubernetes.io/name: coredns
    app.kubernetes.io/instance: coredns
    app.kubernetes.io/version: v1.11.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: coredns
    app.kubernetes.io/part-of: coredns
  annotations:
    prometheus.io/port: "9153"
    prometheus.io/scrape: "true"
spec:
  selector:
    k8s-app: kube-dns
  clusterIP: **********
  ports:
    - name: dns
      port: 53
      protocol: UDP
    - name: dns-tcp
      port: 53
      protocol: TCP
    - name: metrics
      port: 9153
      protocol: TCP
---
# Source: coredns/templates/coredns-deployment.yml.j2
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "coredns"
  namespace: kube-system
  labels:
    k8s-app: "kube-dns"
    addonmanager.kubernetes.io/mode: Reconcile
    kubernetes.io/name: "coredns"
    helm.sh/chart: coredns-v1.11.3
    app.kubernetes.io/name: coredns
    app.kubernetes.io/instance: coredns
    app.kubernetes.io/version: v1.11.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: coredns
    app.kubernetes.io/part-of: coredns
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 10%
  selector:
    matchLabels:
      k8s-app: kube-dns
  template:
    metadata:
      labels:
        k8s-app: kube-dns
    spec:
      securityContext:
        seccompProfile:
          type: RuntimeDefault
      nodeSelector:
        kubernetes.io/os: linux
      priorityClassName: system-cluster-critical
      serviceAccountName: coredns
      imagePullSecrets:
      - name: sensecore-boson
      tolerations:
        - effect: NoSchedule
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - topologyKey: "kubernetes.io/hostname"
            labelSelector:
              matchLabels:
                k8s-app: kube-dns
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            preference:
              matchExpressions:
              - key: node-role.kubernetes.io/control-plane
                operator: In
                values:
                - ""
      containers:
      - name: coredns
        image: "registry.sensetime.com/sensecore/infra/coredns/coredns:v1.11.3"
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 2
            memory: 512Mi
          requests:
            cpu: 100m
            memory: 256Mi
        args: [ "-conf", "/etc/coredns/Corefile" ]
        volumeMounts:
        - name: config-volume
          mountPath: /etc/coredns
        ports:
        - containerPort: 53
          name: dns
          protocol: UDP
        - containerPort: 53
          name: dns-tcp
          protocol: TCP
        - containerPort: 9153
          name: metrics
          protocol: TCP
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            add:
            - NET_BIND_SERVICE
            drop:
            - all
          readOnlyRootFilesystem: true
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8181
            scheme: HTTP
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 10
      dnsPolicy: Default
      volumes:
        - name: config-volume
          configMap:
            name: coredns
