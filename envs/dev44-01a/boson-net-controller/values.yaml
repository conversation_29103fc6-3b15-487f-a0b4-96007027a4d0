global:
  envName: dev-cn-sh-01a
  bosonNetControllerImage: registry.sensetime.com/sensecore-boson/boson-net-controller:v1.20.0-10-gd7ef4d38-20250522170046
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-internal.cn-sh-01.sensecoreapi.dev
  sslSecretName: tls-cnsh01-api
  nodeSelectorTerms:
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  wxURL: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=51e84518-fcf6-4654-9fe4-9186779a7df3
  pgsql:
    host: *************
    port: 34150
    user: boson
    db: boson_service_dev44
    password: b5bioOt5sPuJm2
  boson_default:
    zone_name_for_az_resource: cn-sh-01a
    zone_name_for_region_resource: cn-sh-01z
  bosonNetController:
    config:
      disableFeatures:
      - snat
      gcEnable: false
      netDeviceEnable: true
      netDeviceServerURL: http://************:5050
      globalSnatRuleName: sensecore_dev44_vpc_default_snat_rule_cn_sh_01
      globalSnatQosPolicyName: snatGlobalQosPolicy
      globalDsnatQosPolicyName: dnatGlobalQosPolicy
      defaultInnerSecurityPolicyRuleName: Sensecore_tech_inter_nat
    service:
      clusterIP: **********
      ports:
        xds: 18000
        slbHttp: 52040
        slbGrpc: 52090
