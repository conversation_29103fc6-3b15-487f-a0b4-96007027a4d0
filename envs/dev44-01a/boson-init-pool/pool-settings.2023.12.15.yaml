pool_settings:
  cidr_pools:
    # 所有子网的掩码位数
    prefix: 27
    # 每个子网最前的保留 IP 数，本网地址和网关地址也算在其内
    start_reserve: 10
    # 每个子网最后的保留 IP 数，网关地址也算在期内
    end_reserve: 6
    vni_base: 2500
    roce_training_vni_base: 1500
    zone_prp: cn-sh-01a
    ib:
    - scope: TRAINING
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
    vlan:
    - scope: DATA
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
    - scope: SERVICE
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
  eips:
    cidr: 10.0.0.0/16
    gateway: ********
    ips:
      # type: list 会将 ips 下的 IP 列表直接返回
    - type: list
      ips:
      - ********0
      - *********
  nat_gateways:
    cidr: ***********/24
    gateway: ***********
    ips:
      # type: range 会生产 start 到 end 的所有 ip 地址
    - type: range
      start: *************
      end: *************
    - type: range
      start: *************
      end: *************
