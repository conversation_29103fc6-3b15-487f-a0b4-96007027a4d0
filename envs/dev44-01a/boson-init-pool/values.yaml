global:
  envName: dev-cn-sh-01a
  bosonInitPoolImage: registry.sensetime.com/sensecore-boson/boson-toolbox:v1.13.0-15-g865e71f-20240604165007-dev
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  nodeSelectorTerms: []
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  pgsql:
    host: *************
    port: 34150
    user: boson
    db: boson_service_dev44
    password: b5bioOt5sPuJm2
  bosonProvider:
    enablePoolCmd: ./syncTables && ./initPools
    defaults:
      zone_name_for_az_resource: cn-sh-01a
      zone_name_for_region_resource: cn-sh-01z
