\c boson_service_v2;

/*
# 生成云联电信 EIP 数据库插入语句
for eip_d in $(seq 181 190); do
    echo "INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('$(uuidgen)','120.25.41.${eip_d}','BGP','BGP',false,'*************','*************/27');"
done
*/

BEGIN TRANSACTION;

INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('29019faa-d661-440c-8a15-617a455d7fd5','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('74c2438b-68c0-4203-9eed-588d313d9797','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('d6edb7bc-a776-45bf-b820-************','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('9232de0a-5a4f-45c7-bf8d-dc69f188d84d','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('de38d16c-f0c3-4e74-a6d8-6d7ba55f1853','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('472a95e0-bda4-4d51-bcce-fe94db5d8158','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('3faf97e9-54ea-4c7b-a8eb-be91ed1652d6','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('7bddd4f3-1317-4efa-9523-2655c8ef132c','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('0dfe34e0-6c05-4e34-8ae4-7bf651199a80','*************','BGP','BGP',true,'*************','*************/27');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,line,allocated,gateway,cidr) VALUES ('682e1dd5-2748-4c60-abd3-3a213164629e','*************','BGP','BGP',true,'*************','*************/27');

SELECT * FROM eip_pools WHERE eip_ip LIKE '120.25.41.%';

-- ROLLBACK TRANSACTION;
-- COMMIT TRANSACTION;
