.:53 {
    errors
    health {
        lameduck 5s
    }
    ready
    kubernetes cluster.local in-addr.arpa ip6.arpa {
        pods insecure
        ttl 30
        fallthrough in-addr.arpa ip6.arpa
    }
    prometheus :9153
    hosts {
        ************* registry.sensetime.com
        fallthrough
    }
    file /etc/coredns/sensecore.cn.db registry.cn-sh-01.sensecore.cn
    file /etc/coredns/sensecoreapi-oss.cn.db aoss-internal.cn-sh-01b.sensecoreapi-oss.cn aoss-internal.st-sh-01.sensecoreapi-oss.cn aoss.cn-sh-01.sensecoreapi-oss.cn auto-internal.st-sh-01.sensecoreapi-oss.cn
    forward . /etc/resolv.conf {
        prefer_udp
        max_concurrent 2000
    }
    cache 30 {
        prefetch 5 30m 10%
        serve_stale 24h immediate
    }
    loop
    reload
    loadbalance
}
