global:
  envName: dev-cn-sh-01a
  bosonNdcImage: registry.sensetime.com/sensecore-boson/boson-ndc:v1.20.0-22-g0d39c15-20250520203559
  imagePullSecret: sensecore-boson
  domainName: network-service.cn-sh-01.sensecoreapi.dev
  sslSecretName: tls-cnsh01-api
  enableServiceMonitor: true
  pgsql:
    enabled: true
    host: *************
    port: 34150
    user: boson
    db: boson_service_dev44
    password: b5bioOt5sPuJm2
  bosonNdc:
    # 此处必须在 bosonNdc 下面渲染一个 key。否则 yaml 会把 bosonNdc 当做 str 类型，而不是 dict 类型。如果使用 helm --set bosonNdc.key=XXX 的方式会报数据类型错误
    replicas: 2
