global:
  region: cn-sh-01
  zone: cn-sh-01a
  namespace: plat-boson-infra
  envName: dev-cn-sh-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal.cn-sh-01.sensecoreapi.dev
  sslSecretName: tls-cnsh01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-sh-01
    k8sInfo:
      enabled: False
      region: cn-sh-01
      dryrun: False
      env: dev44
      kubeConfigs:
      - zone: cn-sh-01a
        region: cn-sh-01
        env: dev44
        enabled: True
        kubeConfigFile: /boson/cn-sh-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5050
      southDB:
        host: *************
        port: 34150
        user: boson
        db: boson_service_dev44
        password: b5bioOt5sPuJm2
      omsDB:
        host: *************
        port: 34150
        user: boson
        db: boson_oms
        password: b5bioOt5sPuJm2
      providerAddr:
        - zone: cn-sh-01z
          host: network-internal.cn-sh-01.sensecoreapi.dev
        - zone: cn-sh-01a
          host: network-internal.cn-sh-01.sensecoreapi.dev
        - zone: cn-sh-01b
          host: network-internal.cn-sh-01b.sensecoreapi.dev
        - zone: cn-sh-01p
          host: network-internal.cn-sh-01p.sensecoreapi.dev
        - zone: cn-sh-01e
          host: network-internal.cn-sh-01e.sensecoreapi.dev
