---
# Source: boson-frr/templates/boson-frr-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-frr
  namespace: plat-boson-infra
---
# Source: boson-frr/templates/boson-frr-cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: boson-frr-conf
  namespace: plat-boson-infra
data:
  bj-idc1-10-105-10-30-************_frr-frr.conf: |-
    !
    ! Zebra configuration saved from vty
    !   2024/03/26 12:26:44
    !
    frr version 9.0.1_git
    frr defaults traditional
    !
    hostname bj-idc1-10-105-10-30-************
    !
    interface eth50.kni
     ip address **********/30
    exit
    !
    interface eth60.kni
     ip address ***********/30
    exit
    !
    interface lo
     ip address *************/32
     ip address ***********/32
    exit
    !
    router bgp 65520
    bgp router-id ************
    no bgp ebgp-requires-policy
    bgp bestpath as-path multipath-relax
    neighbor ********** remote-as 65411
    neighbor ********** interface eth50.kni
    neighbor ********** advertisement-interval 0
    neighbor *********** remote-as 65415
    neighbor *********** interface eth60.kni
    neighbor *********** advertisement-interval 0
    !
    address-family ipv4 unicast
      network *************/32
      network ***********/32
      aggregate-address *************/32
      aggregate-address ***********/25
      neighbor ********** soft-reconfiguration inbound
      neighbor ********** prefix-list public_vip_valid_export out
      neighbor ********** prefix-list public_vip_valid_import in
      neighbor *********** soft-reconfiguration inbound
      neighbor *********** prefix-list private_localip_valid_export out
      neighbor *********** prefix-list private_localip_valid_import in
      maximum-paths 8
    exit-address-family
    !
    exit
    !
    ip prefix-list public_vip_valid_export seq 5 permit *************/32
    ip prefix-list public_vip_valid_import seq 5 deny *************/32
    ip prefix-list private_localip_valid_export seq 5 permit ***********/25
    ip prefix-list private_localip_valid_import seq 5 deny ***********/25
    !
    !
    !
    !
    !
  bj-idc1-10-105-10-31-************_frr-frr.conf: |-
    !
    ! Zebra configuration saved from vty
    !   2024/03/26 12:26:44
    !
    frr version 9.0.1_git
    frr defaults traditional
    !
    hostname bj-idc1-10-105-10-31-************
    !
    interface eth50.kni
     ip address ***********/30
    exit
    !
    interface eth60.kni
     ip address ***********/30
    exit
    !
    interface lo
     ip address *************/32
     ip address ***********29/32
    exit
    !
    router bgp 65521
    bgp router-id ************
    no bgp ebgp-requires-policy
    bgp bestpath as-path multipath-relax
    neighbor *********** remote-as 65411
    neighbor *********** interface eth50.kni
    neighbor *********** advertisement-interval 0
    neighbor *********** remote-as 65415
    neighbor *********** interface eth60.kni
    neighbor *********** advertisement-interval 0
    !
    address-family ipv4 unicast
      network *************/32
      network ***********29/32
      aggregate-address *************/32
      aggregate-address *************/25
      neighbor *********** soft-reconfiguration inbound
      neighbor *********** prefix-list public_vip_valid_export out
      neighbor *********** prefix-list public_vip_valid_import in
      neighbor *********** soft-reconfiguration inbound
      neighbor *********** prefix-list private_localip_valid_export out
      neighbor *********** prefix-list private_localip_valid_import in
      maximum-paths 8
    exit-address-family
    !
    exit
    !
    ip prefix-list public_vip_valid_export seq 5 permit *************/32
    ip prefix-list public_vip_valid_import seq 5 deny *************/32
    ip prefix-list private_localip_valid_export seq 5 permit *************/25
    ip prefix-list private_localip_valid_import seq 5 deny *************/25
    !
    !
    !
    !
    !
---
# Source: boson-frr/templates/boson-frr-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-frr
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-frr/templates/boson-frr-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-frr
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-frr
subjects:
- kind: ServiceAccount
  name: boson-frr
  namespace: plat-boson-infra
---
# Source: boson-frr/templates/boson-frr-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-frr-service
  namespace: plat-boson-infra
  labels:
    app-name: boson-frr-service
    helm.sh/chart: boson-frr-1.16.0
    app.kubernetes.io/name: boson-frr
    app.kubernetes.io/instance: boson-frr
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-frr
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-frr-service
  ports:
  - name: http
    port: 8080
    protocol: TCP
  - name: grpc
    port: 8090
    protocol: TCP
---
# Source: boson-frr/templates/boson-frr-ds.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: boson-frr
  namespace: plat-boson-infra
spec:
  selector:
    matchLabels:
      app-name: boson-frr
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        app-name: boson-frr
    spec:
      hostNetwork: true
      containers:
      - name: boson-frr
        image: registry.sensetime.com/sensecore-boson/quay.io/frrouting/frr:9.0.1_installed_tools_01
        imagePullPolicy: IfNotPresent
        livenessProbe:
          exec:
            command:
            - cat
            - /etc/frr/frr.conf
          initialDelaySeconds: 10
          timeoutSeconds: 5
          periodSeconds: 10
        readinessProbe:
          tcpSocket:
            port: 179
          initialDelaySeconds: 20
          timeoutSeconds: 5
          periodSeconds: 10
        env:
        - name: K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
          requests:
            cpu: 100m
            memory: 100Mi
        securityContext:
          capabilities:
            add:
            - NET_BIND_SERVICE
            - NET_ADMIN
            - NET_RAW
            - SYS_ADMIN
        volumeMounts:
        - name: frrconf
          mountPath: /mnt
          readOnly: true
      volumes:
      - name: frrconf
        configMap:
          name: boson-frr-conf
      nodeSelector:
        diamond.sensetime.com/role-infra-slb-platform-dedicated: enabled
      tolerations:
        - key: "diamond.sensetime.com/role-infra-slb-platform-dedicated"
          operator: "Equal"
          value: "enabled"
          effect: "NoExecute"
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      serviceAccount: default
      serviceAccountName: default
      priorityClassName: system-cluster-critical
---
# Source: boson-frr/templates/boson-frr-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-frr-disallow-capabilities
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-capabilities
    ruleNames:
    - adding-capabilities
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-frr-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-frr/templates/boson-frr-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-frr-host-namespaces
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-frr-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-frr/templates/boson-frr-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-frr-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-frr-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-frr/templates/boson-frr-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-frr-servicemonitor
  namespace: plat-boson-infra
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-frr-1.16.0
    app.kubernetes.io/name: boson-frr
    app.kubernetes.io/instance: boson-frr
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-frr
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-frr-service
  namespaceSelector:
    matchNames:
    - plat-boson-infra
  endpoints:
  - port: http
    interval: 10s
    honorLabels: true
