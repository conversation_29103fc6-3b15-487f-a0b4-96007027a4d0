global:
  enableServiceMonitor: true
  namespace: plat-boson-infra
  name: boson-frr
  podName: boson-frr
  configmap:
    name: boson-frr-conf
  serviceInfo:
    envName: dev-cn-sh-01a
    region: cn-sh-01
    az: cn-sh-01a
    bosonFrrImage: registry.sensetime.com/sensecore-boson/quay.io/frrouting/frr:9.0.1_installed_tools_01
  service:
    ports:
      metrics: 5000
  oss_vip: *************/32
instance:
  - host: bj-idc1-10-105-10-30-************
    values:
    oss_vip_cidr: *************/32
    oss_vips:
    - name: vs1
      vip: *************
      vport: 80
    - name: vs2
      vip: *************
      vport: 443
    lvs_local_ip: ***********/25
    lvs_rs_hc_ip: ***********/32
    frr_host_name: bj-idc1-10-105-10-30-************
    frr_bgp_as: 65520
    frr_bgp_router_id: ************
    nics:
    - nic: eth50
      local_kni_ip: **********/30
      peer_ip: **********
      peer_bgp_as: 65411
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth60
      local_kni_ip: ***********/30
      peer_ip: ***********
      peer_bgp_as: 65415
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
  - host: bj-idc1-10-105-10-31-************
    values:
    oss_vip_cidr: *************/32
    oss_vips:
    - name: vs1
      vip: *************
      vport: 80
    - name: vs2
      vip: *************
      vport: 443
    lvs_local_ip: ***********28/25
    lvs_rs_hc_ip: ***********29/32
    frr_host_name: bj-idc1-10-105-10-31-************
    frr_bgp_as: 65521
    frr_bgp_router_id: ************
    nics:
    - nic: eth50
      local_kni_ip: ***********/30
      peer_ip: ***********
      peer_bgp_as: 65411
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth60
      local_kni_ip: ***********/30
      peer_ip: ***********
      peer_bgp_as: 65415
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
