global:
  envName: dev-cn-sh-01a
  bosonProviderImage: registry.sensetime.com/sensecore-boson/boson-provider:v1.20.0-21-g07bb81fc-20250521160632
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-internal.cn-sh-01.sensecoreapi.dev
  sslSecretName: tls-cnsh01-api
  nodeSelectorTerms:
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  pgsql:
    host: *************
    port: 34150
    user: boson
    db: boson_service_dev44
    password: b5bioOt5sPuJm2
  rocketmq:
    default:
      nameServers:
      - *************:9876
      - ***********:9876
      instanceName: sensetime-core-networkDefault-v1-cn-sh-01z
      topic: sensetime-core-network-vpc-v1-cn-sh-01z
      eip_topic: sensetime-core-network-eip-v1-cn-sh-01a
      rmAckTopic: sensetime-core-rm-resource-state-sync
      bossAckTopic: sensetime-core-resource-operation-result
      brokerConsumerGroupName: sensetime-core-network-v1-az1-consumer
      eip_consumer_group_name: sensetime-core-network-v1-az1-consumer-eip
      brokerRMProducerGroupName: sensetime-core-network-v1-az1-producer-rm
      brokerBossProducerGroupName: sensetime-core-network-v1-az1-producer-boss
      brokerNoticeProducerGroupName: sensetime-core-network-v1-az1-producer-Notice
      slb_topic: sensetime-core-network-slb-v1-cn-sh-01a
      slb_consumer_group_name: sensetime-core-network-v1-az1-consumer-slb
      accessKey: rocketmq-ak-boson-provider
      secretKey: e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce
    cloudAudit:
      nameServers:
      - ***********:9876
      - *************:9876
      instanceName: sensetime-core-networkCloudAudit-v1-cn-sh-01z
      topics:
        eip:
          topic: sensetime-core-trail-eip-operation
          producerGroupName: sensetime-core-trail-eip-producer
          accessKey: rocketmq-ak-eip-servcie
          secretKey: gluon-dev-bdec-4df9-8b42-3ce58e81deb8
        vpc:
          topic: sensetime-core-trail-vpc-operation
          producerGroupName: sensetime-core-trail-vpc-producer
          accessKey: rocketmq-ak-vpc-servcie
          secretKey: gluon-dev-f3e8-4d12-8296-173c8b59bb25
        dc:
          topic: sensetime-core-trail-dc-operation
          producerGroupName: sensetime-core-trail-dc-producer
          accessKey: rocketmq-ak-dc-service
          secretKey: xxxxxxxxxxxx
        slb:
          topic: sensetime-core-trail-slb-operation
          producerGroupName: sensetime-core-trail-slb-producer
          accessKey: rocketmq-ak-slb-service
          secretKey: gluon-dev-9f75-47a5-b8ea-54d8cffcd25a
  bosonProvider:
    replicas: 1
    generate_deployment_annotations_timestamp: true
    defaults:
      geneve_subnet_cidr: ***********/20
      dc_vxlan_cidr: **********/24
      dc_console_cidr: ***********/24
      zone_name_for_az_resource: cn-sh-01a
      zone_name_for_region_resource: cn-sh-01z
      region: cn-sh-01
      az: cn-sh-01a
      prp: cn-sh-01a-prp01
      dgw:
        enable: true
        policy_cidr: ***********/24,**********/32,**********/32,**********/32
      training_networks:
        vlan_acl_nic_max_count: 0
        kube_ovn:
        - if_id: roce_0
          gw: ************
        - if_id: roce_1
          gw: ************
        bms:
          gws:
          - ************
          - ************
      bms_master_nic: bond1
      cloud_audit_enable: true
      vpc_default_acls: []
      ts_gateway_nodes: null
      vpc_acls:
      - dest_ip: 10.0.0.0/8,**********/12,***********/16
        protocol: tcp
        dest_port: 22,110,111,139,143,179,445,873
        priority: 30050
        action: deny
        description: 临时：在启用全量 acl 之前，临时屏蔽风险端口，第一趴。每条规则最多 10 个端口
      - dest_ip: 10.0.0.0/8,**********/12,***********/16
        protocol: tcp
        dest_port: 2049,3306,5101,5102,8181,9100,9876
        priority: 30000
        action: deny
        description: 临时：在启用全量 acl 之前，临时屏蔽风险端口，第二趴。每条规则最多 10 个端口
