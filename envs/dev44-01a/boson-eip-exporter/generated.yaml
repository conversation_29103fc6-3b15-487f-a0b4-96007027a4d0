---
# Source: boson-eip-exporter/templates/eip-exporter-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-eip-exporter
  namespace: plat-boson-service
---
# Source: boson-eip-exporter/templates/eip-exporter-cm.yaml
apiVersion: v1
data:
  eip-exporter.yaml: |-
    eip_exporter:
      metrics_port: 55999
    db:
      host: "*************"
      port: 34150
      user: "boson"
      password: "b5bioOt5sPuJm2"
      db: "boson_service_dev44"
    collector:
      vendor: colla
      workers: 3
      interval: 15
      delay: 30
      duration: 15
      page_size: 5
      url: https://*************/api/v2/query/getData/ipAddr
      param:
        name: query
        value: |
          {secretKey:"9fcfaedd366c5582427b9e038f1e4ff38686b426",probeIds:"all",step:"second",date:"{{date}}",filter:{field:"ipAddr",condition:"{{eip_ips}}",filterType:"="},returnField:["ipAddr","totalByte","txByte","rxByte","txBitps","rxBitps","txTcpSegmentLostPacketRate","rxTcpSegmentLostPacketRate","inPacket","outPacket"],groupBy: ["ipAddr"]}
kind: ConfigMap
metadata:
  name: boson-eip-exporter-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-eip-exporter-1.17.0
    app.kubernetes.io/name: boson-eip-exporter
    app.kubernetes.io/instance: boson-eip-exporter
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-eip-exporter
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-eip-exporter/templates/eip-exporter-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-eip-exporter
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-eip-exporter/templates/eip-exporter-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-eip-exporter
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-eip-exporter
subjects:
- kind: ServiceAccount
  name: boson-eip-exporter
  namespace: plat-boson-service
---
# Source: boson-eip-exporter/templates/eip-exporter-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-eip-exporter-service
  namespace: plat-boson-service
  labels:
    app-name: boson-eip-exporter-service
    helm.sh/chart: boson-eip-exporter-1.17.0
    app.kubernetes.io/name: boson-eip-exporter
    app.kubernetes.io/instance: boson-eip-exporter
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-eip-exporter
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-eip-exporter
  ports:
  - name: metrics
    port: 55999
    protocol: TCP
---
# Source: boson-eip-exporter/templates/eip-exporter-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-eip-exporter
  namespace: plat-boson-service
  labels:
    name: boson-eip-exporter
    helm.sh/chart: boson-eip-exporter-1.17.0
    app.kubernetes.io/name: boson-eip-exporter
    app.kubernetes.io/instance: boson-eip-exporter
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-eip-exporter
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-eip-exporter
  replicas: 1
  template:
    metadata:
      labels:
        app-name: boson-eip-exporter
    spec:
      serviceAccount: boson-eip-exporter
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-eip-exporter
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      - key: diamond.sensetime.com/role-business-dps
        effect: NoSchedule
        operator: Equal
        value: "enabled"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-eip-exporter
        image: "registry.sensetime.com/sensecore-boson/boson-eip-exporter:v1.0.5-5c8f01e-20250523144124"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-eip-exporter
        - -c
        - /boson/conf/eip-exporter.yaml
        ports:
        - name: metrics
          containerPort: 55999
          protocol: TCP
        resources:
          limits:
            cpu: 300m
            memory: 300Mi
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: BOSON_EIP_EXPORTER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_EIP_EXPORTER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_EIP_EXPORTER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_EIP_EXPORTER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: 55999
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: 55999
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/eip-exporter.yaml
          subPath: path/to/eip-exporter.yaml
      volumes:
      - name: config
        configMap:
          name: boson-eip-exporter-config
          items:
          - key: eip-exporter.yaml
            path: path/to/eip-exporter.yaml
---
# Source: boson-eip-exporter/templates/eip-exporter-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-eip-exporter-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-eip-exporter-1.17.0
    app.kubernetes.io/name: boson-eip-exporter
    app.kubernetes.io/instance: boson-eip-exporter
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-eip-exporter
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-eip-exporter-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 15s
    honorLabels: true
