global:
  enableServiceMonitor: true
  bosonSlbdpImage: registry.sensetime.com/sensecore-boson/slbdp:v1.0-12-lower-cpu
  namespace: plat-boson-infra
  name: boson-slbdp
  podName: slbdp
  memory: 16Gi
  hugepage2m: 64Gi
  configmap:
    name: boson-slbdp
  service:
    ports:
      metrics: 5000
instance:
  - host: bj-idc1-10-105-10-30-************
    name: keepalived.conf
    local_ip:
      name: host1_localip
      member:
      - range: ***********-12
        port: eth60
    vs_list:
    - name: vs1
      vip: *************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ***********
        rport: 80
    - name: vs2
      vip: *************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ***********
        rport: 443
  - host: bj-idc1-10-105-10-31-************
    name: keepalived.conf
    local_ip:
      name: host2_localip
      member:
      - range: ***********30-140
        port: eth60
    vs_list:
    - name: vs1
      vip: *************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ***********
        rport: 80
    - name: vs2
      vip: *************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ***********
        rport: 443
slbdp:
  - host: bj-idc1-10-105-10-30-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:af:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        - pci: "0000:af:00.1"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth50.kni: **********/30
          eth60.kni: ***********/30
        hc_ip:
          ip: ***********/32
          nexthop: ***********
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (***********/30,***********,eth60)
      rule:
        kernel:
        - (all, ***********/30, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
  - host: bj-idc1-10-105-10-31-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:af:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        - pci: "0000:af:00.1"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth50.kni: ***********/30
          eth60.kni: ***********/30
        hc_ip:
          ip: ***********29/32
          nexthop: ***********
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (***********/30,***********,eth60)
      rule:
        kernel:
        - (all, ***********/30, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
