global:
  bosonBrokerImage: registry.sensetime.com/sensecore-boson/boson-broker:v1.20.0-12-g8c4aee0-20250422205712
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-service.cn-fj-01.thinkheadbdapi.com
  sslSecretName: tls-cnfj01-api
  nodeSelectorTerms:
    - key: diamond.sensetime.com/role-business-app
      values:
        - sensecore
  tolerations:
    - effect: NoSchedule
      key: diamond.sensetime.com/role-business-app
      operator: Equal
      value: sensecore
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-resource-prp
      operator: Equal
      value: cn-sh-01a-control
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-ns-layer
      operator: Equal
      value: iaas
    - effect: NoExecute
      key: diamond.sensetime.com/role-k8s-master
      operator: Equal
      value: enabled
  envName: prod-cn-fj-01z
  pgsql:
    host: ************
    port: 35802
    user: boson
    db: boson_service_v2
    password: xxxxxxxxxxxx
  rocketmq:
    default:
      nameServers:
       - ***********:9876
       - ***********:9876
      dcplConsumerGroupName: sensetime-core-network-v1-kk-consumer
      brokerRMProducerGroupName: sensetime-core-network-v1-kk-producer-rm
      brokerBossProducerGroupName: sensetime-core-network-v1-kk-producer-boss
      brokerNoticeProducerGroupName: sensetime-core-network-v1-kk-producer-notice
      retryInterval: 5
      dcplTopic:  sensetime-core-network-dc-v1-cn-fj-01z
      rmAckTopic: sensetime-core-rm-resource-state-sync
      bossAckTopic: sensetime-core-resource-operation-result
      instanceName: boson-broker-instance
      accessKey: rocketmq-ak-boson-provider
      secretKey: xxxxxxxxxxxx
    cloudAudit:
      nameServers:
       - ***********:9876
       - ***********:9876
      instanceName: boson-broker-cloudAudit-instance
      topics:
        vpc:
          topic: sensetime-core-trail-vpc-operation
          producerGroupName: sensetime-core-trail-vpc-producer
          accessKey: rocketmq-ak-vpc-servcie
          secretKey: xxxxxxxxxxxx
  dns:
    name_server:
      cn-fj-01a: http://network-internal.cn-fj-01.thinkheadbdapi.com
  business:
    iam_auth_addr: https://iam-internal.cn-fj-01.thinkheadbdapi.com
  ovn:
    ovn_ic: ""
    ts_subnet_cidr: "169.254.0.0/17"
  bosonBroker:
    generate_deployment_annotations_timestamp: true
    defaults:
      vpc_default_region: cn-fj-01z
      pg_lock_timeout: 30
      cloud_audit_enable: false
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000
