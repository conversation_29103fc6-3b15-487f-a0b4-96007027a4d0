global:
  region: cn-fj-01
  zone: cn-fj-01z
  namespace: plat-boson-service
  envName: prod-cn-fj-01z
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  omsDashboardImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.15.0-53ab42c-20240730160253
  imagePullSecret: sensecore-boson
  domainName: boson-oms.cn-fj-01.thinkheadbdapi.com
  sslSecretName: tls-cnfj01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-fj-01
    k8sInfo:
      enabled: False
      region: cn-fj-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-fj-01a
        region: cn-fj-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-fj-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5100
      southDB:
        host: ************
        port: 35802
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: ************
        port: 35802
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-fj-01z
          host: network-internal.cn-fj-01.thinkheadbdapi.com
        - zone: cn-fj-01a
          host: network-internal.cn-fj-01.thinkheadbdapi.com
  omsDashboard:
    clickHouse:
      host: clickhouse-higgs.app-higgs-middleware.svc.cluster.local
      port: 9000
      user: boson
      database: boson
      password: RP03PZgeDH
