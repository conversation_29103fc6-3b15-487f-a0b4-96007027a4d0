global:
  region: cn-fz-01
  zone: cn-fz-01a
  namespace: plat-boson-infra
  envName: prod-cn-fz-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal.cn-fz-01.fjscmsapi.com
  sslSecretName: tls-cnfz01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-fz-01
    k8sInfo:
      enabled: False
      region: cn-fz-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-fz-01a
        region: cn-fz-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-fz-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: https://network-service.cn-fz-01.fjscmsapi.com/network/boson-ndc
      southDB:
        host: *************
        port: 35108
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: *************
        port: 35108
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-fz-01z
          host: network-internal.cn-fz-01.fjscmsapi.com
        - zone: cn-fz-01a
          host: network-internal.cn-fz-01.fjscmsapi.com
