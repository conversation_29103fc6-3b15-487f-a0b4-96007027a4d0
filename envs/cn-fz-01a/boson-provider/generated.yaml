---
# Source: boson-provider/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-provider
  namespace: plat-boson-service
---
# Source: boson-provider/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-fz-01a
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2
      show_sql: false

    # regional的mq连接信息
    rocketmq:
      default:
        nameServer:
        - "************:9876"
        - "************:9876"
        - "************:9876"
        - "************:9876"
        instanceName: sensetime-core-network-v1-cn-fz-01z
        topic: sensetime-core-network-vpc-v1-cn-fz-01z
        eip_topic: sensetime-core-network-eip-v1-cn-fz-01a
        notice_topic: sensetime-core-message-engine-msg
        rmAckTopic: sensetime-core-rm-resource-state-sync
        bossAckTopic: sensetime-core-resource-operation-result
        brokerConsumerGroupName: sensetime-core-network-v1-cn-fz-01a-consumer
        eip_consumer_group_name: sensetime-core-network-v1-cn-fz-01a-consumer-eip
        brokerRMProducerGroupName: sensetime-core-network-v1-cn-fz-01a-producer-rm
        brokerBossProducerGroupName: sensetime-core-network-v1-cn-fz-01a-producer-boss
        brokerNoticeProducerGroupName: sensetime-core-network-v1-cn-fz-01a-producer-Notice
        slb_topic: sensetime-core-network-slb-v1-cn-fz-01a
        slb_consumer_group_name: sensetime-core-network-v1-cn-fz-01a-consumer-slb
        accessKey: rocketmq-ak-boson-provider
        secretKey: xxxxxxxxxxxx
      cloudAudit:
        nameServer:
        - "************:9876"
        - "************:9876"
        instanceName: sensetime-core-networkCloudAudit-v1-cn-fz-01z
        topics:
          eip:
            topic: sensetime-core-trail-eip-operation
            producerGroupName: sensetime-core-trail-eip-producer
            accessKey: rocketmq-ak-eip-servcie
            secretKey: xxxxxxxxxxxx
          vpc:
            topic: sensetime-core-trail-vpc-operation
            producerGroupName: sensetime-core-trail-vpc-producer
            accessKey: rocketmq-ak-vpc-servcie
            secretKey: xxxxxxxxxxxx
          dc:
            topic: sensetime-core-trail-dc-operation
            producerGroupName: sensetime-core-trail-dc-producer
            accessKey: rocketmq-ak-dc-service
            secretKey: xxxxxxxxxxxx
          slb:
            topic: sensetime-core-trail-slb-operation
            producerGroupName: sensetime-core-trail-slb-producer
            accessKey: rocketmq-ak-slb-service
            secretKey: xxxxxxxxxxxx

    # reginal的redis连接信息
    redis:
      host: devxx.redis.boson.com
      port: 6379
      password: devxx_redis_pass_com

    # 【保留】发送邮件配置
    smtp:
      enable: true
      host: smtp.partner.outlook.cn
      port: 587
      username: devxx_send_from_username_com
      password: devxx_send_from_password_com
      sender: <EMAIL>

    # Provider的初始化地址池参数

    # Provider的默认参数
    boson_default:
      vpc_cidr: **********/16
      geneve_subnet_cidr: ***********/20
      vlan_subnet_cidr: ***********/22
      dc_vxlan_cidr: **********/24
      dc_console_cidr: ***********/28
      vpc_default_az: cn-fz-01a
      vpc_default_region: cn-fz-01z
      grpc_port: 51090
      http_port: 51080
      metrics_port: 51030
      region: cn-fz-01
      az: cn-fz-01a
      prp: cn-fz-01a-prp01
      dgw:
        enable: true
        external_subnet: **********/16
        resered: **********..**********,**************..**************
        policy_cidr: ***********/21,**********/32,**********/32,**********/32
        infra_ns: "plat-boson-infra"
      quota:
        vpcs_per_tenant: 1
        geneve_subnets_per_vpc: 1
        vlan_subnets_per_vpc: 1
        ib_subnets_per_vpc: 1
        roce_subnets_per_vpc: 1
        eips_per_vpc: 10
        nat_gateways_per_vpc: 1
        dnat_rules_per_eip: 50
        slb_per_vpc: 10
        listener_per_slb: 50
        rule_per_slb: 50
        target_group_per_slb: 50
        target_per_target_group: 50
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000
      training_networks:
        vlan_acl_nic_max_count: 0
        kube_ovn:
        - if_id: roce_0
          gw: **********
        bms:
          gws:
          - ************
      cloud_audit_enable: true
      slb_enable: false
      slb_replica: 1
      bgp_enable: false
      slb_expose_basic_network_vip: false
      slb_expose_overlay_vip: false
      slb_dataplane:
        # 此处为 net-controller 的 DB 信息
        host: xxx
        port: xxx
        user: xxx
        password: xxx
        db: xxx
      # bms2pod macvlan子接口的父接口网卡，默认为空表示不启用该功能，为bond1时表示选用数据网卡bond1创建出macvlan子接口
      bms_master_nic: "bond1"
      ts_gateway_nodes:
      vpc_default_acls:
        - direction: from-lport
          priority: 30950
          match: ip4.dst == ************/29 && ip.proto == 6 && (tcp.dst == 5000 || tcp.dst == 8080 || tcp.dst == 2049)
          action: allow
        - direction: from-lport
          priority: 30900
          match: ip4.dst == ************/29 && ip.proto == 6 && tcp.dst >= 18000 && tcp.dst <= 18010
          action: allow
        - direction: from-lport
          priority: 30850
          match: (ip4.dst == *************/31 || ip4.dst == *************/31) && ip.proto == 6 && tcp.dst == 443
          action: allow
        - direction: from-lport
          priority: 30800
          match: (ip4.dst == **********/16 || ip4.dst == *********/12 || ip4.dst == ***********/24)
          action: allow
        - direction: from-lport
          priority: 30750
          match: ip4.dst == 1************/32 && ip.proto == 6 && (tcp.dst == 11010 || tcp.dst == 11011 || tcp.dst == 53)
          action: allow
        - direction: from-lport
          priority: 30700
          match: ip4.dst == 1************/32 && ip.proto == 17 && udp.dst == 53
          action: allow
        - direction: from-lport
          priority: 30650
          match: ip4.dst == *************/31 && ip.proto == 6 && tcp.dst == 30636
          action: allow
        - direction: from-lport
          priority: 30600
          match: ip4.dst == *************/31 && ip.proto == 6 && (tcp.dst == 33080 || tcp.dst == 33023 || tcp.dst == 33024 || tcp.dst == 33025 || tcp.dst == 32200 || tcp.dst == 38080 || tcp.dst == 30080 || tcp.dst == 33200 || tcp.dst == 30843)
          action: allow
        - direction: from-lport
          priority: 30550
          match: ip4.dst == *************/32 && ip.proto == 6 && tcp.dst == 20001
          action: allow
        - direction: from-lport
          priority: 30500
          match: (ip4.dst == ***********/24)
          action: allow
        - direction: from-lport
          priority: 30450
          match: (ip4.dst == *************/32 || ip4.dst == *************/31 || ip4.dst == *************/31) && ip.proto == 6 && (tcp.dst == 80 || tcp.dst == 443)
          action: allow
        - direction: from-lport
          priority: 30400
          match: (ip4.dst == *************/32 || ip4.dst == *************/31 || ip4.dst == *************/31) && ip.proto == 1
          action: allow
        - direction: from-lport
          priority: 30350
          match: ip4.dst == *************/31 && ip.proto == 6 && (tcp.dst == 30033 || tcp.dst == 30037 || tcp.dst == 30039 || tcp.dst == 30034 || tcp.dst == 30035 || tcp.dst == 30040)
          action: allow
        - direction: from-lport
          priority: 30300
          match: ip4.dst == *************/31 && ip.proto == 6 && (tcp.dst == 51808 || tcp.dst == 32766)
          action: allow
        - direction: from-lport
          priority: 30250
          match: ip4.dst == *************/31 && ip.proto == 6 && tcp.dst == 53
          action: allow
        - direction: from-lport
          priority: 30200
          match: ip4.dst == *************/31 && ip.proto == 17 && udp.dst == 53
          action: allow
        - direction: from-lport
          priority: 30150
          match: ip4.dst == ********/31 && ip.proto == 6 && tcp.dst == 53
          action: allow
        - direction: from-lport
          priority: 30100
          match: ip4.dst == ********/31 && ip.proto == 17 && udp.dst == 53
          action: allow
        - direction: from-lport
          priority: 30050
          match: ip4.dst == **********/32 && ip.proto == 6 && tcp.dst == 3128
          action: allow
        - direction: from-lport
          priority: 30000
          match: (ip4.dst == 10.0.0.0/8 || ip4.dst == **********/12 || ip4.dst == ***********/16)
          action: drop
      vpc_acls:
        - src_ip: ""
          src_port: ""
          dest_ip: "************/29"
          dest_port: "5000,8080,2049"
          protocol: tcp
          priority: 30950
          action: allow
          description: TK满足客户拷盘SOP能力，支持访问临时NAS文件存储 5000、8080、2049 个别端口
        - src_ip: ""
          src_port: ""
          dest_ip: "************/29"
          dest_port: "18000:18010"
          protocol: tcp
          priority: 30900
          action: allow
          description: TK满足客户拷盘SOP能力，支持访问临时NAS文件存储 18000~18010 端口范围
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31,*************/31"
          dest_port: "443"
          protocol: tcp
          priority: 30850
          action: allow
          description: 租户自定义监控从VPC向KK上传用户数据访问higgs-iap-teleport.sensecoreapi.cn服务443，计算实例访问CCR内网IP端口加速
        - src_ip: ""
          src_port: ""
          dest_ip: "**********/16,*********/12,***********/24"
          dest_port: ""
          protocol: all
          priority: 30800
          action: allow
          description: vpc 内部放行 vpc subnet, service subnet, 发卡 subnet
        - src_ip: ""
          src_port: ""
          dest_ip: "1************/32"
          dest_port: "11010,11011,53"
          protocol: tcp
          priority: 30750
          action: allow
          description: 访问 TK 的 DNS Gateway 53端口的 tcp，访问算力池 TK 的 Transom 服务 11010 和 11011 端口的 tcp
        - src_ip: ""
          src_port: ""
          dest_ip: "1************/32"
          dest_port: "53"
          protocol: udp
          priority: 30700
          action: allow
          description: 访问 TK 的 DNS Gateway 53端口的 udp 协议
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "30636"
          protocol: tcp
          priority: 30650
          action: allow
          description: 云管 IAM 的 POSIX 权限认证需要访问 KK Ingress 的 ldap 服务的 node port 端口
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "33080,33023,33024,33025,32200,38080,30080,33200,30843"
          protocol: tcp
          priority: 30600
          action: allow
          description: 开发机pod 通过 aicl-proxy-internal.[region].[domain].com 访问 iap 服务33080和33023~25，通过aicl-audit-internal.[region].[domain].cn访问audit服务32200，通过ams-inference-gateway-internal-data.sensecoreapi.cn服务38080，通过ams-inference-gateway-internal-data.sensecoreapi.cn服务v2版本的30080、ams-mq-proxy-data.sensecoreapi.cn服务33200、租户iap agent访问higgs-iap-teleport.sensecoreapi.cn服务30843
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/32"
          dest_port: "20001"
          protocol: tcp
          priority: 30550
          action: allow
          description: 开发机pod 通过 cci-proxy-internal.[region].[domain].com 访问 dps 服务
        - src_ip: ""
          src_port: ""
          dest_ip: "***********/24"
          dest_port: ""
          protocol: all
          priority: 30500
          action: allow
          description: 租户访问所有zone文件存储数据网Gateway入口
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/32,*************/31,*************/31"
          dest_port: "80,443"
          protocol: tcp
          priority: 30450
          action: allow
          description: 租户访问所有zone对象存储external、business、data数据网入口
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/32,*************/31,*************/31"
          dest_port: ""
          protocol: icmp
          priority: 30400
          action: allow
          description: 租户ping所有zone对象存储external、business、data数据网入口
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "30033,30037,30039,30034,30035,30040"
          protocol: tcp
          priority: 30350
          action: allow
          description: PushGateway，裸金属的 quarkfs-client 访问 30033，裸金属的新版 quarkfs-client 访问 30037/30039，裸金属接云监控访问 30034，AMS 服务访问 30035，ECS的带内监控用30040
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "51808,32766"
          protocol: tcp
          priority: 30300
          action: allow
          description: KK 裸金属云助手 VIP, 裸金属镜像下载备用策略访问 32766
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "53"
          protocol: tcp
          priority: 30250
          action: allow
          description: 访问 boson 在 KK 部署的 internal DNS 的 tcp 协议
        - src_ip: ""
          src_port: ""
          dest_ip: "*************/31"
          dest_port: "53"
          protocol: udp
          priority: 30200
          action: allow
          description: 访问 boson 在 KK 部署的 internal DNS 的 udp 协议
        - src_ip: ""
          src_port: ""
          dest_ip: "********/31"
          dest_port: "53"
          protocol: tcp
          priority: 30150
          action: allow
          description: 访问企信部 DNS 的 tcp 协议
        - src_ip: ""
          src_port: ""
          dest_ip: "********/31"
          dest_port: "53"
          protocol: udp
          priority: 30100
          action: allow
          description: 访问企信部 DNS 的 udp 协议
        - src_ip: ""
          src_port: ""
          dest_ip: "**********/32"
          dest_port: "3128"
          protocol: tcp
          priority: 30050
          action: allow
          description: 公有云科学上网
        - src_ip: ""
          src_port: ""
          dest_ip: "10.0.0.0/8,**********/12,***********/16"
          dest_port: ""
          protocol: all
          priority: 30000
          action: allow
          description: 商汤内网所有服务主机禁止访问 外部通过vpn连入的商汤的网段没有考虑
kind: ConfigMap
metadata:
  name: boson-provider-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-provider/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-provider
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-provider/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-provider
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-provider
subjects:
- kind: ServiceAccount
  name: boson-provider
  namespace: plat-boson-service
---
# Source: boson-provider/templates/provider-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-provider-service
  namespace: plat-boson-service
  labels:
    app-name: boson-provider-service
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-provider
  ports:
  - name: http
    port: 51080
    protocol: TCP
  - name: grpc
    port: 51090
    protocol: TCP
  - name: metrics
    port: 51030
    protocol: TCP
---
# Source: boson-provider/templates/provider-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-provider
  namespace: plat-boson-service
  labels:
    name: boson-provider
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-provider
  replicas: 3
  template:
    metadata:
      labels:
        app-name: boson-provider
    spec:
      serviceAccount: boson-provider
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-provider
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-provider
        image: "registry.sensetime.com/sensecore-boson/boson-provider:v1.20.0-21-g07bb81fc-**************"
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 51080
          protocol: TCP
        - name: grpc
          containerPort: 51090
          protocol: TCP
        - name: metrics
          containerPort: 51030
          protocol: TCP
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
          requests:
            cpu: 100m
            memory: 100Mi
        readinessProbe:
          tcpSocket:
            port: 51080
        livenessProbe:
          tcpSocket:
            port: 51080
          initialDelaySeconds: 10
        env:
        - name: ROCKETMQ_GO_LOG_LEVEL
          value: warn
        - name: BOSON_PROVIDER_VAR_NAME
          value: "boson-provider"
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-provider.yaml
          subPath: path/to/boson-provider.yaml
      volumes:
      - configMap:
          name: boson-provider-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
---
# Source: boson-provider/templates/provider-grpc-ing.yaml
# HTTP ingress host and path couldn't be same to GRPC ingress, and GRPC ingress path must be /, so this path is /network.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-provider-service-ingress-grpc
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/ssl-redirect: "True"
  labels:
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-internal.cn-fz-01.fjscmsapi.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: boson-provider-service
            port:
              number: 51090
  tls:
  - hosts:
    - network-internal.cn-fz-01.fjscmsapi.com
    secretName: tls-cnfz01-api
---
# Source: boson-provider/templates/provider-ing.yaml
# HTTP ingress host and path couldn't be same to GRPC ingress, and GRPC ingress path must be /, so this path is /network.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-provider-service-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-internal.cn-fz-01.fjscmsapi.com
    http:
      paths:
      - path: /network
        pathType: Prefix
        backend:
          service:
            name: boson-provider-service
            port:
              number: 51080
  tls:
  - hosts:
    - network-internal.cn-fz-01.fjscmsapi.com
    secretName: tls-cnfz01-api
---
# Source: boson-provider/templates/provider-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-provider-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-provider-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
---
# Source: boson-provider/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-provider-hook
  namespace: plat-boson-service
---
# Source: boson-provider/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-provider-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-provider/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-provider-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-provider-hook
subjects:
- kind: ServiceAccount
  name: boson-provider-hook
  namespace: plat-boson-service
---
# Source: boson-provider/templates/post-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-provider-post-hook
  namespace: plat-boson-service
  labels:
    name: boson-provider-post-hook
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": post-upgrade,post-install
    "helm.sh/hook-weight": "3"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 10
  completions: 1
  parallelism: 3
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-provider-post-hook
    spec:
      serviceAccount: boson-provider-hook
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-provider
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-provider-post-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "waiting all provider replica upgrade finish..."
          rs=$(kubectl describe deploy boson-provider -n plat-boson-service | grep "NewReplicaSet:" | awk '{print $2}')
          echo "got NewReplicaSet ${rs}"

          max_attempts=300
          sleep_second=6
          for ((i=0; i<max_attempts; i++)); do
            rs_desired=$(kubectl get rs "${rs}" -n plat-boson-service --no-headers | awk '{print $2}' | xargs)
            rs_ready=$(kubectl get rs "${rs}" -n plat-boson-service --no-headers | awk '{print $4}' | xargs)
            if [[ "${rs_desired}" == "${rs_ready}" ]]; then
              echo "replica ready=${rs_ready}, desired=${rs_desired}, upgrade finish!!!"
              break
            fi

            echo "replica ready=${rs_ready}, desired=${rs_desired}, attempts=${i}, retry"
            sleep ${sleep_second}
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: check replica timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          max_attempts=300
          sleep_second=6
          url="https://network-internal.cn-fz-01.fjscmsapi.com/network/healthz"
          for ((i=0; i<max_attempts; i++)); do
            echo "health check url '${url}'"
            http_code=$(curl -k -s -o /dev/null -w "%{http_code}" "${url}")
            if [[ ${http_code} -eq 200 ]]; then
              echo "http status code is 200, healthz check success"
              break
            fi

            echo "http status code is ${http_code}, healthz check fail, attempts=${i}, retry"
            sleep ${sleep_second}
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: health check url '${url}' timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          # sleep a while for provider acquire lease
          for ((i=0; i<10; i++)); do
            leader_pod=$(kubectl get leases boson-provider -n plat-boson-service --no-headers | awk '{print $2}')
            if [[ -z ${leader_pod} ]]; then
              echo "Error: not found boson-provider leader pod"
              exit 1
            fi
            if [[ "${leader_pod}" =~ "${rs}" ]]; then
              echo "got new leader pod ${leader_pod}"
              break
            else
              sleep 6
            fi
          done

          kubectl logs "${leader_pod}" -n plat-boson-service | grep "I am the new leader"
          if [[ $? -eq 0 ]]; then
            echo "check leader ${leader_pod} log success"
            exit 0
          fi
          echo "Error: leader logs were not found in leader's pod ${leader_pod}"
          exit 1
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
---
# Source: boson-provider/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-provider-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-provider-pre-hook
    helm.sh/chart: boson-provider-1.16.0
    app.kubernetes.io/name: boson-provider
    app.kubernetes.io/instance: boson-provider
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-provider
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 3
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-provider-pre-hook
    spec:
      serviceAccount: boson-provider-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-provider-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
          - /bin/bash
          - -c
          - |
            echo "PG DB connection check"
            PGPASSWORD=xxxxxxxxxxxx psql -h ************* -p 35108 -U boson -d boson_service_v2 -c "select count(*) from vpcs;" > /dev/null;
            if [[ $? -eq 0 ]]; then
              echo "psql connection successful"
            else
              echo "Error: psql connection failed"
              exit 1
            fi
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
