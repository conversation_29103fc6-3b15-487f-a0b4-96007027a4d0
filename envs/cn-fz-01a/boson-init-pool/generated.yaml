---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-fz-01a
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ***********/24
        gateway: ***********
        ips:
        - "***********0"
        - "***********1"
        - "***********2"
        - "***********3"
        - "************"
        - "************"
        - "************"
        - "***********7"
        - "***********8"
        - "***********9"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "10.123.69.49"
        - "10.123.69.50"
        - "10.123.69.51"
        - "10.123.69.52"
        - "10.123.69.53"
        - "10.123.69.54"
        - "10.123.69.55"
        - "10.123.69.56"
        - "10.123.69.57"
        - "10.123.69.58"
        - "10.123.69.59"
        - "10.123.69.60"
        - "10.123.69.61"
        - "10.123.69.62"
        - "10.123.69.63"
        - "10.123.69.64"
        - "10.123.69.65"
        - "10.123.69.66"
        - "10.123.69.67"
        - "10.123.69.68"
        - "10.123.69.69"
        - "10.123.69.70"
        - "10.123.69.71"
        - "10.123.69.72"
        - "10.123.69.73"
        - "10.123.69.74"
        - "10.123.69.75"
        - "10.123.69.76"
        - "10.123.69.77"
        - "10.123.69.78"
        - "10.123.69.79"
        - "10.123.69.80"
        - "10.123.69.81"
        - "10.123.69.82"
        - "10.123.69.83"
        - "10.123.69.84"
        - "10.123.69.85"
        - "10.123.69.86"
        - "10.123.69.87"
        - "10.123.69.88"
        - "10.123.69.89"
        - "10.123.69.90"
        - "10.123.69.91"
        - "10.123.69.92"
        - "10.123.69.93"
        - "10.123.69.94"
        - "10.123.69.95"
        - "10.123.69.96"
        - "10.123.69.97"
        - "10.123.69.98"
        - "10.123.69.99"
        - "***********00"
        - "***********01"
        - "***********02"
        - "***********03"
        - "***********04"
        - "***********05"
        - "***********06"
        - "***********07"
        - "***********08"
        - "***********09"
        - "***********10"
        - "***********11"
        - "***********12"
        - "***********13"
        - "***********14"
        - "***********15"
        - "***********16"
        - "***********17"
        - "***********18"
        - "***********19"
        - "***********20"
        - "***********21"
        - "***********22"
        - "***********23"
        - "***********24"
        - "***********25"
        - "***********26"
        - "***********27"
        - "***********28"
        - "***********29"
        - "***********30"
        - "***********31"
        - "***********32"
        - "***********33"
        - "***********34"
        - "***********35"
        - "***********36"
        - "***********37"
        - "***********38"
        - "***********39"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "***********70"
        - "***********71"
        - "***********72"
        - "***********73"
        - "***********74"
        - "***********75"
        - "***********76"
        - "***********77"
        - "***********78"
        - "***********79"
        - "***********80"
        - "***********81"
        - "***********82"
        - "***********83"
        - "***********84"
        - "***********85"
        - "***********86"
        - "***********87"
        - "***********88"
        - "***********89"
        - "***********90"
        - "***********91"
        - "***********92"
        - "***********93"
        - "***********94"
        - "***********95"
        - "***********96"
        - "***********97"
        - "***********98"
        - "***********99"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
      slbs:
        cidr: ***********/24
        gateway: ***********
        ips:
        - "***********0"
        - "***********1"
        - "***********2"
        - "************"
        - "***********4"
        - "***********5"
        - "************"
        - "***********7"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "10.123.70.49"
        - "10.123.70.50"
        - "10.123.70.51"
        - "10.123.70.52"
        - "10.123.70.53"
        - "10.123.70.54"
        - "10.123.70.55"
        - "10.123.70.56"
        - "10.123.70.57"
        - "10.123.70.58"
        - "10.123.70.59"
        - "10.123.70.60"
        - "10.123.70.61"
        - "10.123.70.62"
        - "10.123.70.63"
        - "10.123.70.64"
        - "10.123.70.65"
        - "10.123.70.66"
        - "10.123.70.67"
        - "10.123.70.68"
        - "10.123.70.69"
        - "10.123.70.70"
        - "10.123.70.71"
        - "10.123.70.72"
        - "10.123.70.73"
        - "10.123.70.74"
        - "10.123.70.75"
        - "10.123.70.76"
        - "10.123.70.77"
        - "10.123.70.78"
        - "10.123.70.79"
        - "10.123.70.80"
        - "10.123.70.81"
        - "10.123.70.82"
        - "10.123.70.83"
        - "10.123.70.84"
        - "10.123.70.85"
        - "10.123.70.86"
        - "10.123.70.87"
        - "10.123.70.88"
        - "10.123.70.89"
        - "10.123.70.90"
        - "10.123.70.91"
        - "10.123.70.92"
        - "10.123.70.93"
        - "10.123.70.94"
        - "10.123.70.95"
        - "10.123.70.96"
        - "10.123.70.97"
        - "10.123.70.98"
        - "10.123.70.99"
        - "***********00"
        - "***********01"
        - "***********02"
        - "***********03"
        - "***********04"
        - "***********05"
        - "***********06"
        - "***********07"
        - "***********08"
        - "***********09"
        - "***********10"
        - "***********11"
        - "***********12"
        - "***********13"
        - "***********14"
        - "***********15"
        - "***********16"
        - "***********17"
        - "***********18"
        - "***********19"
        - "***********20"
        - "***********21"
        - "***********22"
        - "***********23"
        - "***********24"
        - "***********25"
        - "***********26"
        - "***********27"
        - "***********28"
        - "***********29"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "***********40"
        - "***********41"
        - "***********42"
        - "***********43"
        - "***********44"
        - "***********45"
        - "***********46"
        - "***********47"
        - "***********48"
        - "***********49"
        - "***********50"
        - "***********51"
        - "***********52"
        - "***********53"
        - "***********54"
        - "***********55"
        - "***********56"
        - "***********57"
        - "***********58"
        - "***********59"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "***********70"
        - "***********71"
        - "***********72"
        - "***********73"
        - "***********74"
        - "***********75"
        - "***********76"
        - "***********77"
        - "***********78"
        - "***********79"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
      eips:
        cidr: ***********/24
        gateway: ***********
        ips:
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "58.22.103.51"
        - "58.22.103.52"
        - "58.22.103.53"
        - "58.22.103.54"
        - "58.22.103.55"
        - "58.22.103.56"
        - "58.22.103.57"
        - "58.22.103.58"
        - "58.22.103.59"
        - "58.22.103.60"
        - "58.22.103.61"
        - "58.22.103.62"
        - "58.22.103.63"
        - "58.22.103.67"
        - "58.22.103.68"
        - "58.22.103.69"
        - "58.22.103.70"
        - "58.22.103.71"
        - "58.22.103.72"
        - "58.22.103.73"
        - "58.22.103.74"
        - "58.22.103.75"
        - "58.22.103.76"
        - "58.22.103.77"
        - "58.22.103.78"
        - "58.22.103.79"
        - "58.22.103.80"
        - "58.22.103.81"
        - "58.22.103.82"
        - "58.22.103.83"
        - "58.22.103.84"
        - "58.22.103.85"
        - "58.22.103.86"
        - "58.22.103.87"
        - "58.22.103.88"
        - "58.22.103.89"
        - "58.22.103.90"
        sku: CHINA_UNICOM
      cidr_pools:
        ib:
        - cidr: 10.110.128.0/26
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 2500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.58..10.110.128.62
        - cidr: 10.110.128.64/26
          gateway: 10.110.128.65
          zone_prp: cn-fz-01a
          vni: 2501
          scope: TRAINING
          reserved: 10.110.128.65..10.110.128.73,10.110.128.122..10.110.128.126
        - cidr: 10.110.128.128/26
          gateway: 10.110.128.129
          zone_prp: cn-fz-01a
          vni: 2502
          scope: TRAINING
          reserved: 10.110.128.129..10.110.128.137,10.110.128.186..10.110.128.190
        - cidr: 10.110.128.192/26
          gateway: 10.110.128.193
          zone_prp: cn-fz-01a
          vni: 2503
          scope: TRAINING
          reserved: 10.110.128.193..10.110.128.201,10.110.128.250..10.110.128.254
        - cidr: 10.110.129.0/26
          gateway: 10.110.129.1
          zone_prp: cn-fz-01a
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-fz-01a
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-fz-01a
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-fz-01a
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-fz-01a
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-fz-01a
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-fz-01a
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-fz-01a
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-fz-01a
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-fz-01a
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-fz-01a
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-fz-01a
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-fz-01a
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-fz-01a
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-fz-01a
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-fz-01a
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-fz-01a
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-fz-01a
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-fz-01a
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-fz-01a
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-fz-01a
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-fz-01a
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-fz-01a
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-fz-01a
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-fz-01a
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-fz-01a
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-fz-01a
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-fz-01a
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.119.56.0/26
          gateway: 10.119.56.1
          zone_prp: cn-fz-01a
          vni: 2500
          scope: DATA
          reserved: 10.119.56.1..10.119.56.9,10.119.56.58..10.119.56.62
        - cidr: 10.119.56.64/26
          gateway: 10.119.56.65
          zone_prp: cn-fz-01a
          vni: 2501
          scope: DATA
          reserved: 10.119.56.65..10.119.56.73,10.119.56.122..10.119.56.126
        - cidr: 10.119.56.128/26
          gateway: 10.119.56.129
          zone_prp: cn-fz-01a
          vni: 2502
          scope: DATA
          reserved: 10.119.56.129..10.119.56.137,10.119.56.186..10.119.56.190
        - cidr: 10.119.56.192/26
          gateway: 10.119.56.193
          zone_prp: cn-fz-01a
          vni: 2503
          scope: DATA
          reserved: 10.119.56.193..10.119.56.201,10.119.56.250..10.119.56.254
        - cidr: 10.119.57.0/26
          gateway: 10.119.57.1
          zone_prp: cn-fz-01a
          vni: 2504
          scope: DATA
          reserved: 10.119.57.1..10.119.57.9,10.119.57.58..10.119.57.62
        - cidr: 10.119.57.64/26
          gateway: 10.119.57.65
          zone_prp: cn-fz-01a
          vni: 2505
          scope: DATA
          reserved: 10.119.57.65..10.119.57.73,10.119.57.122..10.119.57.126
        - cidr: 10.119.57.128/26
          gateway: 10.119.57.129
          zone_prp: cn-fz-01a
          vni: 2506
          scope: DATA
          reserved: 10.119.57.129..10.119.57.137,10.119.57.186..10.119.57.190
        - cidr: 10.119.57.192/26
          gateway: 10.119.57.193
          zone_prp: cn-fz-01a
          vni: 2507
          scope: DATA
          reserved: 10.119.57.193..10.119.57.201,10.119.57.250..10.119.57.254
        - cidr: 10.119.58.0/26
          gateway: 10.119.58.1
          zone_prp: cn-fz-01a
          vni: 2508
          scope: DATA
          reserved: 10.119.58.1..10.119.58.9,10.119.58.58..10.119.58.62
        - cidr: 10.119.58.64/26
          gateway: 10.119.58.65
          zone_prp: cn-fz-01a
          vni: 2509
          scope: DATA
          reserved: 10.119.58.65..10.119.58.73,10.119.58.122..10.119.58.126
        - cidr: 10.119.58.128/26
          gateway: 10.119.58.129
          zone_prp: cn-fz-01a
          vni: 2510
          scope: DATA
          reserved: 10.119.58.129..10.119.58.137,10.119.58.186..10.119.58.190
        - cidr: 10.119.58.192/26
          gateway: 10.119.58.193
          zone_prp: cn-fz-01a
          vni: 2511
          scope: DATA
          reserved: 10.119.58.193..10.119.58.201,10.119.58.250..10.119.58.254
        - cidr: 10.119.59.0/26
          gateway: 10.119.59.1
          zone_prp: cn-fz-01a
          vni: 2512
          scope: DATA
          reserved: 10.119.59.1..10.119.59.9,10.119.59.58..10.119.59.62
        - cidr: 10.119.59.64/26
          gateway: 10.119.59.65
          zone_prp: cn-fz-01a
          vni: 2513
          scope: DATA
          reserved: 10.119.59.65..10.119.59.73,10.119.59.122..10.119.59.126
        - cidr: 10.119.59.128/26
          gateway: 10.119.59.129
          zone_prp: cn-fz-01a
          vni: 2514
          scope: DATA
          reserved: 10.119.59.129..10.119.59.137,10.119.59.186..10.119.59.190
        - cidr: 10.119.59.192/26
          gateway: 10.119.59.193
          zone_prp: cn-fz-01a
          vni: 2515
          scope: DATA
          reserved: 10.119.59.193..10.119.59.201,10.119.59.250..10.119.59.254
        - cidr: 10.119.60.0/26
          gateway: 10.119.60.1
          zone_prp: cn-fz-01a
          vni: 2516
          scope: DATA
          reserved: 10.119.60.1..10.119.60.9,10.119.60.58..10.119.60.62
        - cidr: 10.119.60.64/26
          gateway: 10.119.60.65
          zone_prp: cn-fz-01a
          vni: 2517
          scope: DATA
          reserved: 10.119.60.65..10.119.60.73,10.119.60.122..10.119.60.126
        - cidr: 10.119.60.128/26
          gateway: 10.119.60.129
          zone_prp: cn-fz-01a
          vni: 2518
          scope: DATA
          reserved: 10.119.60.129..10.119.60.137,10.119.60.186..10.119.60.190
        - cidr: 10.119.60.192/26
          gateway: 10.119.60.193
          zone_prp: cn-fz-01a
          vni: 2519
          scope: DATA
          reserved: 10.119.60.193..10.119.60.201,10.119.60.250..10.119.60.254
        - cidr: 10.119.61.0/26
          gateway: 10.119.61.1
          zone_prp: cn-fz-01a
          vni: 2520
          scope: DATA
          reserved: 10.119.61.1..10.119.61.9,10.119.61.58..10.119.61.62
        - cidr: 10.119.61.64/26
          gateway: 10.119.61.65
          zone_prp: cn-fz-01a
          vni: 2521
          scope: DATA
          reserved: 10.119.61.65..10.119.61.73,10.119.61.122..10.119.61.126
        - cidr: 10.119.61.128/26
          gateway: 10.119.61.129
          zone_prp: cn-fz-01a
          vni: 2522
          scope: DATA
          reserved: 10.119.61.129..10.119.61.137,10.119.61.186..10.119.61.190
        - cidr: 10.119.61.192/26
          gateway: 10.119.61.193
          zone_prp: cn-fz-01a
          vni: 2523
          scope: DATA
          reserved: 10.119.61.193..10.119.61.201,10.119.61.250..10.119.61.254
        - cidr: 10.119.62.0/26
          gateway: 10.119.62.1
          zone_prp: cn-fz-01a
          vni: 2524
          scope: DATA
          reserved: 10.119.62.1..10.119.62.9,10.119.62.58..10.119.62.62
        - cidr: 10.119.62.64/26
          gateway: 10.119.62.65
          zone_prp: cn-fz-01a
          vni: 2525
          scope: DATA
          reserved: 10.119.62.65..10.119.62.73,10.119.62.122..10.119.62.126
        - cidr: 10.119.62.128/26
          gateway: 10.119.62.129
          zone_prp: cn-fz-01a
          vni: 2526
          scope: DATA
          reserved: 10.119.62.129..10.119.62.137,10.119.62.186..10.119.62.190
        - cidr: 10.119.62.192/26
          gateway: 10.119.62.193
          zone_prp: cn-fz-01a
          vni: 2527
          scope: DATA
          reserved: 10.119.62.193..10.119.62.201,10.119.62.250..10.119.62.254
        - cidr: 10.119.63.0/26
          gateway: 10.119.63.1
          zone_prp: cn-fz-01a
          vni: 2528
          scope: DATA
          reserved: 10.119.63.1..10.119.63.9,10.119.63.58..10.119.63.62
        - cidr: 10.119.63.64/26
          gateway: 10.119.63.65
          zone_prp: cn-fz-01a
          vni: 2529
          scope: DATA
          reserved: 10.119.63.65..10.119.63.73,10.119.63.122..10.119.63.126
        - cidr: 10.119.63.128/26
          gateway: 10.119.63.129
          zone_prp: cn-fz-01a
          vni: 2530
          scope: DATA
          reserved: 10.119.63.129..10.119.63.137,10.119.63.186..10.119.63.190
        - cidr: 10.119.63.192/26
          gateway: 10.119.63.193
          zone_prp: cn-fz-01a
          vni: 2531
          scope: DATA
          reserved: 10.119.63.193..10.119.63.201,10.119.63.250..10.119.63.254
        - cidr: 10.119.192.0/26
          gateway: 10.119.192.1
          zone_prp: cn-fz-01a
          vni: 2500
          scope: SERVICE
          reserved: 10.119.192.1..10.119.192.9,10.119.192.58..10.119.192.62
        - cidr: 10.119.192.64/26
          gateway: 10.119.192.65
          zone_prp: cn-fz-01a
          vni: 2501
          scope: SERVICE
          reserved: 10.119.192.65..10.119.192.73,10.119.192.122..10.119.192.126
        - cidr: 10.119.192.128/26
          gateway: 10.119.192.129
          zone_prp: cn-fz-01a
          vni: 2502
          scope: SERVICE
          reserved: 10.119.192.129..10.119.192.137,10.119.192.186..10.119.192.190
        - cidr: 10.119.192.192/26
          gateway: 10.119.192.193
          zone_prp: cn-fz-01a
          vni: 2503
          scope: SERVICE
          reserved: 10.119.192.193..10.119.192.201,10.119.192.250..10.119.192.254
        - cidr: 10.119.193.0/26
          gateway: 10.119.193.1
          zone_prp: cn-fz-01a
          vni: 2504
          scope: SERVICE
          reserved: 10.119.193.1..10.119.193.9,10.119.193.58..10.119.193.62
        - cidr: 10.119.193.64/26
          gateway: 10.119.193.65
          zone_prp: cn-fz-01a
          vni: 2505
          scope: SERVICE
          reserved: 10.119.193.65..10.119.193.73,10.119.193.122..10.119.193.126
        - cidr: 10.119.193.128/26
          gateway: 10.119.193.129
          zone_prp: cn-fz-01a
          vni: 2506
          scope: SERVICE
          reserved: 10.119.193.129..10.119.193.137,10.119.193.186..10.119.193.190
        - cidr: 10.119.193.192/26
          gateway: 10.119.193.193
          zone_prp: cn-fz-01a
          vni: 2507
          scope: SERVICE
          reserved: 10.119.193.193..10.119.193.201,10.119.193.250..10.119.193.254
        - cidr: 10.119.194.0/26
          gateway: 10.119.194.1
          zone_prp: cn-fz-01a
          vni: 2508
          scope: SERVICE
          reserved: 10.119.194.1..10.119.194.9,10.119.194.58..10.119.194.62
        - cidr: 10.119.194.64/26
          gateway: 10.119.194.65
          zone_prp: cn-fz-01a
          vni: 2509
          scope: SERVICE
          reserved: 10.119.194.65..10.119.194.73,10.119.194.122..10.119.194.126
        - cidr: 10.119.194.128/26
          gateway: 10.119.194.129
          zone_prp: cn-fz-01a
          vni: 2510
          scope: SERVICE
          reserved: 10.119.194.129..10.119.194.137,10.119.194.186..10.119.194.190
        - cidr: 10.119.194.192/26
          gateway: 10.119.194.193
          zone_prp: cn-fz-01a
          vni: 2511
          scope: SERVICE
          reserved: 10.119.194.193..10.119.194.201,10.119.194.250..10.119.194.254
        - cidr: 10.119.195.0/26
          gateway: 10.119.195.1
          zone_prp: cn-fz-01a
          vni: 2512
          scope: SERVICE
          reserved: 10.119.195.1..10.119.195.9,10.119.195.58..10.119.195.62
        - cidr: 10.119.195.64/26
          gateway: 10.119.195.65
          zone_prp: cn-fz-01a
          vni: 2513
          scope: SERVICE
          reserved: 10.119.195.65..10.119.195.73,10.119.195.122..10.119.195.126
        - cidr: 10.119.195.128/26
          gateway: 10.119.195.129
          zone_prp: cn-fz-01a
          vni: 2514
          scope: SERVICE
          reserved: 10.119.195.129..10.119.195.137,10.119.195.186..10.119.195.190
        - cidr: 10.119.195.192/26
          gateway: 10.119.195.193
          zone_prp: cn-fz-01a
          vni: 2515
          scope: SERVICE
          reserved: 10.119.195.193..10.119.195.201,10.119.195.250..10.119.195.254
        - cidr: 10.119.196.0/26
          gateway: 10.119.196.1
          zone_prp: cn-fz-01a
          vni: 2516
          scope: SERVICE
          reserved: 10.119.196.1..10.119.196.9,10.119.196.58..10.119.196.62
        - cidr: 10.119.196.64/26
          gateway: 10.119.196.65
          zone_prp: cn-fz-01a
          vni: 2517
          scope: SERVICE
          reserved: 10.119.196.65..10.119.196.73,10.119.196.122..10.119.196.126
        - cidr: 10.119.196.128/26
          gateway: 10.119.196.129
          zone_prp: cn-fz-01a
          vni: 2518
          scope: SERVICE
          reserved: 10.119.196.129..10.119.196.137,10.119.196.186..10.119.196.190
        - cidr: 10.119.196.192/26
          gateway: 10.119.196.193
          zone_prp: cn-fz-01a
          vni: 2519
          scope: SERVICE
          reserved: 10.119.196.193..10.119.196.201,10.119.196.250..10.119.196.254
        - cidr: 10.119.197.0/26
          gateway: 10.119.197.1
          zone_prp: cn-fz-01a
          vni: 2520
          scope: SERVICE
          reserved: 10.119.197.1..10.119.197.9,10.119.197.58..10.119.197.62
        - cidr: 10.119.197.64/26
          gateway: 10.119.197.65
          zone_prp: cn-fz-01a
          vni: 2521
          scope: SERVICE
          reserved: 10.119.197.65..10.119.197.73,10.119.197.122..10.119.197.126
        - cidr: 10.119.197.128/26
          gateway: 10.119.197.129
          zone_prp: cn-fz-01a
          vni: 2522
          scope: SERVICE
          reserved: 10.119.197.129..10.119.197.137,10.119.197.186..10.119.197.190
        - cidr: 10.119.197.192/26
          gateway: 10.119.197.193
          zone_prp: cn-fz-01a
          vni: 2523
          scope: SERVICE
          reserved: 10.119.197.193..10.119.197.201,10.119.197.250..10.119.197.254
        - cidr: 10.119.198.0/26
          gateway: 10.119.198.1
          zone_prp: cn-fz-01a
          vni: 2524
          scope: SERVICE
          reserved: 10.119.198.1..10.119.198.9,10.119.198.58..10.119.198.62
        - cidr: 10.119.198.64/26
          gateway: 10.119.198.65
          zone_prp: cn-fz-01a
          vni: 2525
          scope: SERVICE
          reserved: 10.119.198.65..10.119.198.73,10.119.198.122..10.119.198.126
        - cidr: 10.119.198.128/26
          gateway: 10.119.198.129
          zone_prp: cn-fz-01a
          vni: 2526
          scope: SERVICE
          reserved: 10.119.198.129..10.119.198.137,10.119.198.186..10.119.198.190
        - cidr: 10.119.198.192/26
          gateway: 10.119.198.193
          zone_prp: cn-fz-01a
          vni: 2527
          scope: SERVICE
          reserved: 10.119.198.193..10.119.198.201,10.119.198.250..10.119.198.254
        - cidr: 10.119.199.0/26
          gateway: 10.119.199.1
          zone_prp: cn-fz-01a
          vni: 2528
          scope: SERVICE
          reserved: 10.119.199.1..10.119.199.9,10.119.199.58..10.119.199.62
        - cidr: 10.119.199.64/26
          gateway: 10.119.199.65
          zone_prp: cn-fz-01a
          vni: 2529
          scope: SERVICE
          reserved: 10.119.199.65..10.119.199.73,10.119.199.122..10.119.199.126
        - cidr: 10.119.199.128/26
          gateway: 10.119.199.129
          zone_prp: cn-fz-01a
          vni: 2530
          scope: SERVICE
          reserved: 10.119.199.129..10.119.199.137,10.119.199.186..10.119.199.190
        - cidr: 10.119.199.192/26
          gateway: 10.119.199.193
          zone_prp: cn-fz-01a
          vni: 2531
          scope: SERVICE
          reserved: 10.119.199.193..10.119.199.201,10.119.199.250..10.119.199.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-fz-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-fz-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-fz-01a
      vpc_default_region: cn-fz-01z
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
