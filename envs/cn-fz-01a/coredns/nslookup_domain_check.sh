#!/usr/bin/env bash
set -beou pipefail
cd "$(dirname "${0}")"
# shellcheck source=/dev/null
source comm_libs.sh

if kubectl get svc -n kube-system kube-dns ; then
    IFS=' ' read -r dns_service_name _  dns_servers _ _ <<< "$(kubectl get svc -n kube-system kube-dns --no-headers)"
elif kubectl get svc -n kube-system coredns ; then
    IFS=' ' read -r dns_service_name _  dns_servers _ _ <<< "$(kubectl get svc -n kube-system coredns --no-headers)"
else
    echo "FATAL: No coredns service not found in either kube-dns or coredns"
    exit 1
fi
echo "${dns_service_name}" "${dns_servers}"
# set specific dns server ip, for example ************ for cn-sh-01a,
# please uncomment and update the line below
#dns_servers="************"
#echo "Uses core dns server IP: ${dns_servers}"

for ns in $(kubectl get ns --no-headers -oname | cut -d "/" -f 2); do
    echo "====== ${ns} ======"
    while read -r svc_name svc_ip; do
        if [[ -z "${svc_name}" ]]; then
            continue
        fi
        echo -e "\e[1:m validate ${svc_name}.${ns}.svc.cluster.local \e[0m"
        domain="${svc_name}.${ns}.svc.cluster.local"
        expected_ips="${svc_ip};"
        check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"
    done <<< "$(kubectl get svc -n "${ns}" --no-headers | grep -v "No resources found" | grep -Ev "ClusterIP +None" | awk '{print $1" "$3}')"
done

echo -e "\e[1:m validate sensecore domain \e[0m"
domain="sensecore.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate public domain \e[0m"
domain="baidu.com"
expected_ips="*************;************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo "========================================================================"
echo "================== validate CoreDNS configured domains ================="
echo "========================================================================"

echo -e "\e[1:m validate domain registry.sensetime.com resolve in hosts plugin. \e[0m"
domain="registry.sensetime.com"
expected_ips="*************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"
