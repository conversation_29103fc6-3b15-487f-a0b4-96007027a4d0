---
# Source: boson-broker/templates/broker-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-broker
  namespace: plat-boson-service
---
# Source: boson-broker/templates/broker-cm.yaml
apiVersion: v1
data:
  boson-broker.yaml: |-
    env: prod-cn-hf-01z
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2
      show_sql: false
    rocketmq:
      default:
        nameServer:
        - "************:9876"
        - "************:9876"
        dcplConsumerGroupName: sensetime-core-network-v1-kk-consumer
        brokerRMProducerGroupName: sensetime-core-network-v1-kk-producer-rm
        brokerBossProducerGroupName: sensetime-core-network-v1-kk-producer-boss
        brokerNoticeProducerGroupName: sensetime-core-network-v1-kk-producer-notice
        retryInterval: 5
        dcplTopic:  sensetime-core-network-dc-v1-cn-hf-01z
        noticeTopic: sensetime-core-message-engine-msg
        rmAckTopic: sensetime-core-rm-resource-state-sync
        bossAckTopic: sensetime-core-resource-operation-result
        instanceName: boson-broker-instance
        accessKey: rocketmq-ak-boson-provider
        secretKey: xxxxxxxxxxxx
      cloudAudit:
        nameServer:
          - "************:9876"
          - "10.115.96.15:9876"
        instanceName: boson-broker-cloudAudit-instance
        topics:
          vpc:
            topic: sensetime-core-trail-vpc-operation
            producerGroupName: sensetime-core-trail-vpc-producer
            accessKey: rocketmq-ak-vpc-servcie
            secretKey: xxxxxxxxxxxx
    dns:
      name_server:
        cn-hf-01a: http://network-internal.cn-hf-01.sensecoreapi.cn
    business:
      iam_auth_addr: https://iam-internal.cn-hf-01.sensecoreapi.cn
    ovn:
      ovn_ic: ""
      ts_subnet_cidr: 169.254.0.0/17
    # Provider的默认参数
    boson_default:
      vpc_default_region: cn-hf-01z
      pg_lock_timeout: 30
      cloud_audit_enable: true
      grpc_port: 51090
      http_port: 51080
      metrics_port: 51030
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000

kind: ConfigMap
metadata:
  name: boson-broker-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-broker/templates/broker-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-broker
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - "*"
  - nonResourceURLs:
      - "*"
    verbs:
      - "*"
---
# Source: boson-broker/templates/broker-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-broker
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-broker
subjects:
  - kind: ServiceAccount
    name: boson-broker
    namespace: plat-boson-service
---
# Source: boson-broker/templates/broker-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-broker-service
  namespace: plat-boson-service
  labels:
    app-name: boson-broker-service
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    boson-broker-master: "true"
  ports:
  - name: http
    port: 51080
    protocol: TCP
  - name: grpc
    port: 51090
    protocol: TCP
  - name: metrics
    port: 51030
    protocol: TCP
---
# Source: boson-broker/templates/broker-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-broker
  namespace: plat-boson-service
  labels:
    name: boson-broker
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-broker-service
  replicas: 3
  template:
    metadata:
      labels:
        app-name: boson-broker-service
    spec:
      priorityClassName: system-cluster-critical
      serviceAccount: boson-broker
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-broker-service
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Equal
        value: "cn-sh-01a-control"
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-broker
        image: "registry.sensetime.com/sensecore-boson/boson-broker:v1.20.0-12-g8c4aee0-20250422205712"
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 51080
          protocol: TCP
        - name: grpc
          containerPort: 51090
          protocol: TCP
        - name: metrics
          containerPort: 51030
          protocol: TCP
        resources:
          limits:
            cpu: 1
            memory: 1Gi
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: ROCKETMQ_GO_LOG_LEVEL
          value: warn
        - name: BOSON_BROKER_VAR_NAME
          value: "prod-cn-hf-01z"
        - name: BOSON_BROKER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_BROKER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_BROKER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_BROKER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /metrics
            port: 51030
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /metrics
            port: 51030
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: config
          mountPath: /boson-broker.yaml
          subPath: path/to/boson-broker.yaml
      volumes:
      - configMap:
          name: boson-broker-config
          items:
          - key: boson-broker.yaml
            path: path/to/boson-broker.yaml
        name: config
---
# Source: boson-broker/templates/broker-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-broker-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-service.cn-hf-01.sensecoreapi.cn
    http:
      paths:
      - path: /network/boson-broker(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: boson-broker-service
            port:
              number: 51080
  tls:
  - hosts:
    - network-service.cn-hf-01.sensecoreapi.cn
    secretName: tls-cnhf01-api
---
# Source: boson-broker/templates/broker-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-broker-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-broker-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
---
# Source: boson-broker/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-broker-hook
  namespace: plat-boson-service
---
# Source: boson-broker/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-broker-hook
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - "*"
  - nonResourceURLs:
      - "*"
    verbs:
      - "*"
---
# Source: boson-broker/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-broker-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-broker-hook
subjects:
  - kind: ServiceAccount
    name: boson-broker-hook
    namespace: plat-boson-service
---
# Source: boson-broker/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-broker-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-broker-pre-hook
    helm.sh/chart: boson-broker-1.19.0
    app.kubernetes.io/name: boson-broker
    app.kubernetes.io/instance: boson-broker
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-broker
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-broker-pre-hook
    spec:
      serviceAccount: boson-broker-hook
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Equal
        value: "cn-sh-01a-control"
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-broker-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923"
        command:
          - /bin/bash
          - -c
          - |
            #!/bin/bash
            set -x
            IP_STRING=
            IP_LIST=($(echo "$IP_STRING" | tr ',' '\n' | awk -F ':' '{print $2}'))
            PORTS=(6645 6646)
            for IP in "${IP_LIST[@]}"
            do
                for PORT in "${PORTS[@]}"
                do
                  if nc -z "${IP#tcp:}" "$PORT" >/dev/null 2>&1; then
                      echo "Port $PORT is open on ${IP#tcp:}"
                  else
                      echo "Port $PORT is closed on ${IP#tcp:}"
                      exit 1
                  fi
                done
            done
            echo "All ports are open on all IPs"
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
