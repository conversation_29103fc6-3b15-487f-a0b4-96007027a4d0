global:
  envName: prod-cn-cq-01a
  bosonInitPoolImage: registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  nodeSelectorTerms: []
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  pgsql:
    host: ************
    port: 32137
    user: boson
    db: boson_service_v2
    password: xxxxxxxxxxxx
  bosonProvider:
    enablePoolCmd: ./syncTables && ./initPools
    defaults:
      zone_name_for_az_resource: cn-cq-01a
      zone_name_for_region_resource: cn-cq-01z
