---
# Source: boson-net-controller/templates/net-controller-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-net-controller
  namespace: plat-boson-service
---
# Source: boson-net-controller/templates/net-controller-cm.yaml
apiVersion: v1
data:
  config.yaml: |-
    boson_default:
      vpc_default_az: cn-cq-01a
      vpc_default_region: cn-cq-01z
    # Disable features: dnat, eip, ipa, shadownode, snat, subnet, vpcnatgateway, bms-lazy-attach
    disableFeatures:
      - snat
      - bms-lazy-attach
    pg:
      host: ************
      port: "32137"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2
    ibManager:
      enable: true
    netDevice:
      enable: true
      serverURL: http://************:5090
      accessKey: WTNsM1VuWnpSa3R4UW1oNlpXNVBDZz09Cg==

    dataNetworkSwitch: msn3420

    bot:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=999c558c-579a-4fe9-a1b2-987337a9d839

    fw:
      globalSnatRuleName: sensecore_prod_vpc_default_snat_rule_cn_cq_01
      globalSnatQosPolicyName: sensecore_prod_default_tenant_snat_qos_policy
      globalDnatQosPolicyName: sensecore_prod_default_tenant_dnat_qos_policy
      sourceZone: Trust
      destinationZone: Untrust
      defaultSecurityPolicyRuleName: Default_policy
      defaultInnerSecurityPolicyRuleName: Sensecore_prod_inter_nat
      minNatAddressGroupID: 10000
      maxNatAddressGroupID: 65535
    gc:
      gcIntervalSec: 86400
      GCPauseSec: 60
      enable: false
    slb:
      enable: false
      deployment:
        image: registry.sensetime.com/sensecore-boson/boson-envoy-dataplane:v0.0.1
        cmd:
          - sh
        args:
          - -c
          - while true; do sleep 1; done
        privileged: true
        allowPrivilegeEscalation: true
        tolerationsConfig: |-
          - effect: NoExecute
            key: diamond.sensetime.com/role-k8s-master
            operator: Equal
            value: enabled
          - effect: NoSchedule
            key: diamond.sensetime.com/role-business-app
            operator: Equal
            value: sensecore
          - effect: NoSchedule
            key: diamond.sensetime.com/belong-resource-prp
            operator: Exists
        affinityConfig: |-
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: diamond.sensetime.com/role-infra-vpc-gw
                  operator: In
                  values:
                  - enabled
          podAntiAffinity:
            preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchExpressions:
                  - key: ovn.kubernetes.io/vpc-nat-gw
                    operator: Exists
                topologyKey: kubernetes.io/hostname
              weight: 100
        readinessProbeConfig: |-
          exec:
            command:
            - sh
            - -c
            - ls
          failureThreshold: 3
          periodSeconds: 5
          successThreshold: 1
          timeoutSeconds: 2
        termTime: 0
        basicNetworkNamespace: kube-system
        basicNetworkNAD: ovn-vpc-external-network
        replica: 1
        resource:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 100Mi
        agent:
          report_server_address: 0.0.0.0
          report_server_port: 52090
          # grpc, http, mertic 和 envoy 启动在同一个pod的同一个ip地址上，需要和 envoy 的端口做好规划
          # envoy 的端口：envoyAdminPort
          admin_http_port: 19500
          admin_grpc_port: 19501
          admin_metrics_port: 19502
          image: registry.sensetime.com/sensecore-boson/boson-envoy-dataplane:v1.27.0-6-g0a49c3e4e6-20240327202832
          cmd:
            - sh
          args:
            - -c
            - while true; do sleep 1; done
          readinessProbeConfig: |-
            exec:
              command:
              - sh
              - -c
              - ls
            failureThreshold: 3
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
      bgpEnable: false
      agentEnable: true
      namespace: plat-boson-infra
      xDSControllerServiceHost: 0.0.0.0
      xDSControllerServicePort: 18000
      xDSServerGrpcKeepaliveTime: 3
      xDSServerGrpcKeepaliveTimeout: 3
      xDSServerGrpcKeepaliveMinTime: 3
      xDSServerGrpcMaxConcurrentStreams: 1000000
      envoyAdminPort: 19000
      envoyConfigTemplatePath: /etc/envoy/envoy.yaml
      grpc_port: 52090
      http_port: 52040
      upgrade:
        - 730
      imagePullSecrets:
      - name: sensecore-boson
    pprof:
      pprofEnable: false
      pprofPort: 52050
      metricsPath: /metrics
      pprofPath: /debug/pprof/

    # Net device config templates
    # define the firewall and switch config
    netDeviceConfig:
      fw__default__set_eip: |-
        # CR: eip/{{.CR}}
        # EIP: {{.EIP}}
        # TenantID: {{.TenantID}}
        # SrSourceIP: {{.SrSourceIP}}
        # AssociationType: {{.AssociationType}}
        # SubnetNetAddr: {{.SubnetNetAddr}}
        # SubnetNetMask: {{.SubnetNetMask}}
        # UpStreamMaxBandwidth: {{.UpStreamEIPMaxBandwidth}}
        # DownStreamMaxBandwidth: {{.DownStreamEIPMaxBandwidth}}
        # MaxConns: {{.EIPMaxConns}}

        # ssh username@{{.MGMTIP}}

        system-view

        nat address-group {{.NatAgID}} name bsn_eip_ag_{{.EIP}}
            address {{.EIP}} {{.EIP}}

        object-group ip address bsn_snat_og_{{.EIP}}
            security-zone {{.SourceZone}}
        {{- if or (eq .AssociationType "NATGW") (eq .AssociationType "NATGW_AND_BM") }}
            0 network subnet {{.SrSourceIP}} 32
        {{- end}}
        {{- if or (eq .AssociationType "BM") (eq .AssociationType "NATGW_AND_BM") }}
            1 network subnet {{.SubnetNetAddr}} {{.SubnetNetMask}}
        {{- end}}

        nat global-policy
        rule name bsn_eip_sr_{{.EIP}}
            source-ip bsn_snat_og_{{.EIP}}
            source-zone {{.SourceZone}}
            destination-zone {{.DestinationZone}}
            action snat address-group name bsn_eip_ag_{{.EIP}}
            counting enable

        {{- if eq .DefaultSnatRule "enabled" }}
        rule move bsn_eip_sr_{{.EIP}} before {{.GlobalSnatRuleName}}
        {{- else}}
        rule move bsn_eip_sr_{{.EIP}} after {{.GlobalSnatRuleName}}
        {{- end}}

        {{- if or (and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "")) (and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "")) (and (ne .EIPMaxConns "0") (ne .EIPMaxConns "")) }}

        object-group ip address bsn_sqos_og_{{.EIP}}
        {{- if or (eq .AssociationType "NATGW") (eq .AssociationType "NATGW_AND_BM") }}
          0 network host address {{.SrSourceIP}}
        {{- end}}
        {{- if or (eq .AssociationType "BM") (eq .AssociationType "NATGW_AND_BM") }}
          1 network subnet {{.SubnetNetAddr}} {{.SubnetNetMask}}
        {{- end}}

        object-group ip address bsn_dqos_og_{{.EIP}}
          0 network host address {{.EIP}}

        traffic-policy
        profile name bsn_nattp_pf_{{.EIP}}
        {{- if and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "") }}
            bandwidth upstream maximum {{.UpStreamEIPMaxBandwidth}}
        {{- end}}
        {{- if and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "") }}
            bandwidth downstream maximum {{.DownStreamEIPMaxBandwidth}}
        {{- end}}
        {{- if and (ne .EIPMaxConns "0") (ne .EIPMaxConns "") }}
            connection-limit count per-rule {{.EIPMaxConns}}
        {{- end}}

        rule name bsn_sqos_rule_{{.EIP}}
            source-zone {{.SourceZone}}
            destination-zone {{.DestinationZone}}
            source-address address-set bsn_sqos_og_{{.EIP}}
            action qos profile bsn_nattp_pf_{{.EIP}}

        rule name bsn_dqos_rule_{{.EIP}}
            source-zone {{.DestinationZone}}
            destination-zone {{.SourceZone}}
            destination-address address-set bsn_dqos_og_{{.EIP}}
            action qos profile bsn_nattp_pf_{{.EIP}}

        {{- if eq .DefaultSnatRule "enabled" }}
        rule move bsn_sqos_rule_{{.EIP}} before {{.GlobalSnatQosPolicyName}}
        rule move bsn_dqos_rule_{{.EIP}} before {{.GlobalDnatQosPolicyName}}
        {{- else}}
        rule move bsn_sqos_rule_{{.EIP}} after {{.GlobalSnatQosPolicyName}}
        rule move bsn_dqos_rule_{{.EIP}} after {{.GlobalDnatQosPolicyName}}
        {{- end}}

        {{- end}}

        save force

        # ./boson-tool -name {{.CR}} --status ACTIVE -type eip

      fw__default__unset_eip: |-
        # CR: eip/{{.CR}}
        # EIP: {{.EIP}}
        # TenantID: {{.TenantID}}
        # SrSourceIP: {{.SrSourceIP}}
        # AssociationType: {{.AssociationType}}
        # SubnetNetAddr: {{.SubnetNetAddr}}
        # SubnetNetMask: {{.SubnetNetMask}}
        # UpStreamMaxBandwidth: {{.UpStreamEIPMaxBandwidth}}
        # DownStreamMaxBandwidth: {{.DownStreamEIPMaxBandwidth}}
        # MaxConns: {{.EIPMaxConns}}

        # ssh username@{{.MGMTIP}}

        system-view

        nat global-policy
        undo rule name bsn_eip_sr_{{.EIP}}
        undo nat address-group {{.NatAgID}}
        undo object-group ip address bsn_snat_og_{{.EIP}}

        {{- if or (and (ne .UpStreamEIPMaxBandwidth "0") (ne .UpStreamEIPMaxBandwidth "")) (and (ne .DownStreamEIPMaxBandwidth "0") (ne .DownStreamEIPMaxBandwidth "")) (and (ne .EIPMaxConns "0") (ne .EIPMaxConns "")) }}
        traffic-policy
        undo rule name bsn_sqos_rule_{{.EIP}}
        undo rule name bsn_dqos_rule_{{.EIP}}
        undo profile name bsn_nattp_pf_{{.EIP}}
        undo object-group ip address bsn_dqos_og_{{.EIP}}
        undo object-group ip address bsn_sqos_og_{{.EIP}}
        {{- end}}

        save force

        # ./boson-tool -name {{.CR}} --status DELETED -type eip

      fw__default__expire_stop_eip: |-
        # CR: eip/{{.CR}}
        # EIP: {{.EIP}}
        # TenantID: {{.TenantID}}
        # SrSourceIP: {{.SrSourceIP}}
        # AssociationType: {{.AssociationType}}
        # UpStreamMaxBandwidth: {{.UpStreamEIPMaxBandwidth}}
        # DownStreamMaxBandwidth: {{.DownStreamEIPMaxBandwidth}}
        # MaxConns: {{.EIPMaxConns}}

        # ssh username@{{.MGMTIP}}

        expire stop eip {{.EIP}}

        # ./boson-tool -name {{.CR}} --status EXPIRESTOPPED -type eip

      fw__default__renew_start_eip: |-
        # CR: eip/{{.CR}}
        # EIP: {{.EIP}}
        # TenantID: {{.TenantID}}
        # SrSourceIP: {{.SrSourceIP}}
        # AssociationType: {{.AssociationType}}
        # UpStreamMaxBandwidth: {{.UpStreamEIPMaxBandwidth}}
        # DownStreamMaxBandwidth: {{.DownStreamEIPMaxBandwidth}}
        # MaxConns: {{.EIPMaxConns}}

        # ssh username@{{.MGMTIP}}

        renew start eip {{.EIP}}

        # ./boson-tool -name {{.CR}} --status ACTIVE -type eip

      fw__default__set_dnat: |-
        # CR: dnat/{{.CR}}
        # Protocol: {{.Protocol}}
        # Port: {{.DnatPort}}

        # ssh username@{{.MGMTIP}}

        system-view

        object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        {{- if eq .Protocol "t" }}
            0 service tcp destination eq {{.DnatPort}}
        {{- else if eq .Protocol "u" }}
            0 service udp destination eq {{.DnatPort}}
        {{- else }}
            0 service tcp destination eq {{.DnatPort}}
            1 service udp destination eq {{.DnatPort}}
        {{- end}}

        nat global-policy
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          description boson_dnat_{{.EIP}}_{{.DnatPort}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone {{.DestinationZone}}
          destination-ip host {{.EIP}}
          action dnat ip-address {{.DnatIP}} local-port {{.InternalPort}}
          counting enable

        rule name bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone Trust
          destination-ip host {{.EIP}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{.DnatIP}} local-port {{.InternalPort}}
          counting enable

        rule move bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before  {{.DefaultInnerSecurityPolicyRuleName}}

        security-policy ip
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action pass
          counting enable
          source-zone {{.DestinationZone}}
          destination-zone {{.SourceZone}}
          destination-ip-host {{.DnatIP}}
        {{- if eq .Protocol "t" }}
          service-port {{.Protocol}} destination eq {{.DnatPort}}
        {{- else if eq .Protocol "u" }}
          service-port {{.Protocol}} destination eq {{.DnatPort}}
        {{- else }}
          service-port tcp destination eq {{.DnatPort}}
          service-port udp destination eq {{.DnatPort}}
        {{- end}}

        move rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before name {{.DefaultSecurityPolicyRuleName}}

        save force

        # ./boson-tool -name {{.CR}} --status CREATED -type dnat

      fw__default__unset_dnat: |-
        # CR: dnat/{{.CR}}
        # EIP: {{.EIP}}
        # Port: {{.DnatPort}}

        # ssh username@{{.MGMTIP}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        undo rule name bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        undo object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        security-policy ip
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        save force

        # ./boson-tool -name {{.CR}} --status DELETED -type dnat

      fw__default__bm_set_dnat: |-
        # CR: dnat/{{.CR}}
        # Protocol: {{.Protocol}}
        # Port: {{.DnatPort}}

        # ssh username@{{.MGMTIP}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        undo object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        security-policy ip
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        {{- if eq .Protocol "t" }}
            0 service tcp destination eq {{.DnatPort}}
        {{- else if eq .Protocol "u" }}
            0 service udp destination eq {{.DnatPort}}
        {{- else }}
            0 service tcp destination eq {{.DnatPort}}
            1 service udp destination eq {{.DnatPort}}
        {{- end}}

        nat global-policy
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          description boson_dnat_{{.EIP}}_{{.DnatPort}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone {{.DestinationZone}}
          destination-ip host {{.EIP}}
          action dnat ip-address {{.DnatIP}} local-port {{.InternalPort}}
          counting enable

        rule name bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone Trust
          destination-ip host {{.EIP}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{.DnatIP}} local-port {{.InternalPort}}
          counting enable

        rule move bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before  {{.DefaultInnerSecurityPolicyRuleName}}

        security-policy ip
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action pass
          counting enable
          source-zone {{.DestinationZone}}
          destination-zone {{.SourceZone}}
          destination-ip-host {{.DnatIP}}
        {{- if eq .Protocol "t" }}
          service-port {{.Protocol}} destination eq {{.InternalPort}}
        {{- else if eq .Protocol "u" }}
          service-port {{.Protocol}} destination eq {{.InternalPort}}
        {{- else }}
          service-port tcp destination eq {{.InternalPort}}
          service-port udp destination eq {{.InternalPort}}
        {{- end}}

        move rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before name {{.DefaultSecurityPolicyRuleName}}

        save force

        # ./boson-tool -name {{.CR}} --status ACTIVE -type dnat

      fw__default__bm_unset_dnat: |-
        # CR: dnat/{{.CR}}
        # EIP: {{.EIP}}
        # Port: {{.DnatPort}}

        # ssh username@{{.MGMTIP}}

        system-view

        nat global-policy
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        undo object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        security-policy ip
        undo rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}

        object-group service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
        {{- if eq .Protocol "t" }}
            0 service tcp destination eq {{.DnatPort}}
        {{- else if eq .Protocol "u" }}
            0 service udp destination eq {{.DnatPort}}
        {{- else }}
            0 service tcp destination eq {{.DnatPort}}
            1 service udp destination eq {{.DnatPort}}
        {{- end}}

        nat global-policy
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          description boson_dnat_{{.EIP}}_{{.DnatPort}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone {{.DestinationZone}}
          destination-ip host {{.EIP}}
          action dnat ip-address {{.DnatIP}} local-port {{.DnatPort}}
          counting enable

        rule name bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          source-zone Trust
          destination-ip host {{.EIP}}
          service bsn_og_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action snat address-group name bsn_inter_snat_ag_default
          action dnat ip-address {{.DnatIP}} local-port {{.DnatPort}}
          counting enable

        rule move bsn_idr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before  {{.DefaultInnerSecurityPolicyRuleName}}

        security-policy ip
        rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}}
          action pass
          counting enable
          source-zone {{.DestinationZone}}
          destination-zone {{.SourceZone}}
          destination-ip-host {{.DnatIP}}
        {{- if eq .Protocol "t" }}
          service-port {{.Protocol}} destination eq {{.DnatPort}}
        {{- else if eq .Protocol "u" }}
          service-port {{.Protocol}} destination eq {{.DnatPort}}
        {{- else }}
          service-port tcp destination eq {{.DnatPort}}
          service-port udp destination eq {{.DnatPort}}
        {{- end}}

        move rule name bsn_dr_{{.Protocol}}_{{.EIP}}_{{.DnatPort}} before name {{.DefaultSecurityPolicyRuleName}}

        save force

        # ./boson-tool -name {{.CR}} --status CREATED -type dnat

      tor__default__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VlanID: {{.VNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}
        # ssh username@{{.MGMTIP}}

        system-view
        disp current-configuration interface {{.NetPortName}}
        vlan {{.VNI}}
        description {{.SubnetName}}
        interface {{.NetPortName}}
        port link-type access
        port access vlan {{.VNI}}
        save force

        {{- if eq .Operate "BIND" }}
        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa
        {{- end}}
        {{- if eq .Operate "UNBIND" }}
        # ./boson-tool -name {{.CR}} --status CREATED -type ipa
        {{- end}}

      roce_leaf__msn3420__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        net show interface {{.NetPortName}} json
        {{- if HasPrefix .NetPortName "bond" }}
        net add bond {{.NetPortName}} bridge access {{.VNI}}
        {{- else}}
        net add interface {{.NetPortName}} bridge access {{.VNI}}
        {{- end}}
        net commit

        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa

      roce_leaf__msn3420__del_netport_from_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        net show interface {{.NetPortName}} json
        {{- if HasPrefix .NetPortName "bond" }}
        net del bond {{.NetPortName}} bridge access {{.VNI}}
        {{- else}}
        net del interface {{.NetPortName}} bridge access {{.VNI}}
        {{- end}}
        net commit

        # ./boson-tool -name {{.CR}} --status CREATED -type ipa

      roce_leaf__s6850__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}
          port link-type access
          service-instance {{.DefaultVNI }}
          undo encapsulation
          service-instance {{.VNI}}
          encapsulation untagged
          xconnect vsi vni_{{.VNI}}
        save force

        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa

      roce_leaf__s6850__del_netport_from_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}
          port link-type access
          service-instance {{.VNI}}
          undo encapsulation
          service-instance {{.DefaultVNI}}
          encapsulation untagged
          xconnect vsi vni_{{.DefaultVNI}}
        save force

        # ./boson-tool -name {{.CR}} --status CREATED -type ipa

      roce_leaf__s9855-24b8d__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}
          port link-type access
          service-instance {{.DefaultVNI }}
          undo encapsulation
          service-instance {{.VNI}}
          encapsulation untagged
          xconnect vsi vni_{{.VNI}}
        save force

        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa

      roce_leaf__s9855-24b8d__del_netport_from_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}
          port link-type access
          service-instance {{.VNI}}
          undo encapsulation
          service-instance {{.DefaultVNI}}
          encapsulation untagged
          xconnect vsi vni_{{.DefaultVNI}}
        save force

        # ./boson-tool -name {{.CR}} --status CREATED -type ipa

      roce_leaf__ce8851__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}.{{.DefaultVNI}} mode l2
        undo encapsulation untag
        commit
        interface {{.NetPortName}}.{{.VNI}} mode l2
        undo encapsulation dot1q vid {{.VNI}}
        commit
        encapsulation untag
        commit

        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa

      roce_leaf__ce8851__del_netport_from_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        interface {{.NetPortName}}.{{.VNI}} mode l2
        undo encapsulation untag
        commit
        encapsulation dot1q vid {{.VNI}}
        commit
        interface {{.NetPortName}}.{{.DefaultVNI}} mode l2
        encapsulation untag
        commit

        # ./boson-tool -name {{.CR}} --status CREATED -type ipa

      roce_leaf__s9827-128dh__add_netport_to_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        disp current-configuration interface {{.NetPortName}}
        interface {{.NetPortName}}
          port link-type access
          port access vlan {{.VNI}}
        save force

        # ./boson-tool -name {{.CR}} --status ACTIVE -type ipa

      roce_leaf__s9827-128dh__del_netport_from_vlan: |-
        # CR: ipa/{{.CR}}
        # NetDeviceID: {{.NetDeviceID}}
        # NetDeviceName: {{.NetDeviceName}}
        # MGMTIP: {{.MGMTIP}}
        # VNI: {{.VNI}}
        # DefaultVNI: {{.DefaultVNI}}
        # SubnetName: {{.SubnetName}}
        # NetPortName: {{.NetPortName}}

        # ssh username@{{.MGMTIP}}

        system-view
        disp current-configuration interface {{.NetPortName}}
        interface {{.NetPortName}}
          port link-type access
          port access vlan {{.DefaultVNI}}
        save force

        # ./boson-tool -name {{.CR}} --status CREATED -type ipa

kind: ConfigMap
metadata:
  name: boson-net-controller-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-net-controller/templates/net-controller-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-net-controller
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-net-controller/templates/net-controller-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-net-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-net-controller
subjects:
- kind: ServiceAccount
  name: boson-net-controller
  namespace: plat-boson-service
---
# Source: boson-net-controller/templates/net-controller-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-net-controller-service
  namespace: plat-boson-service
  labels:
    app-name: boson-net-controller-service
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  clusterIP: **********
  selector:
    app-name: boson-net-controller
  ports:
  - name: http
    port: 52080
    protocol: TCP
  - name: grpc
    port: 52100
    protocol: TCP
  - name: metrics
    port: 52030
    protocol: TCP
---
# Source: boson-net-controller/templates/net-controller-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-net-controller
  namespace: plat-boson-service
  labels:
    name: boson-net-controller
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-net-controller
  replicas: 3
  template:
    metadata:
      labels:
        app-name: boson-net-controller
    spec:
      serviceAccount: boson-net-controller
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-net-controller
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-net-controller
        image: "registry.sensetime.com/sensecore-boson/boson-net-controller:v1.20.0-10-gd7ef4d38-**********0046"
        command: [ "/boson-net-controller", "--conf=/etc/boson-net-controller/config.yaml", "--env=prod-cn-cq-01a", "--leader-elect=true" ]
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 52080
          protocol: TCP
        - name: grpc
          containerPort: 52100
          protocol: TCP
        - name: metrics
          containerPort: 52030
          protocol: TCP
        resources:
          limits:
            cpu: 400m
            memory: 1024Mi
          requests:
            cpu: 100m
            memory: 200Mi
        env:
        - name: BOSON_NET_CONTROLLER_VAR_NAME
          value: "boson-net-controller"
        - name: CONFIG
          value: "/etc/boson-net-controller/config.yaml"
        - name: ENVNAME
          value: prod-cn-cq-01a
        - name: BOSON_NET_CONTROLLER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NET_CONTROLLER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NET_CONTROLLER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NET_CONTROLLER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: boson-net-controller-config
          mountPath: /etc/boson-net-controller
        livenessProbe:
          httpGet:
            path: /healthz
            port: 52080
          periodSeconds: 30
          initialDelaySeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 52080
          periodSeconds: 10
          initialDelaySeconds: 10
      volumes:
      - name: boson-net-controller-config
        configMap:
          defaultMode: 420
          name: boson-net-controller-config
---
# Source: boson-net-controller/templates/net-controller-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-net-controller-service-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-internal.cn-cq-01.cloudaidcapi.com
    http:
      paths:
      - path: /network/boson-net-controller(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: boson-net-controller-service
            port:
              number: 52080
  tls:
  - hosts:
    - network-internal.cn-cq-01.cloudaidcapi.com
    secretName: tls-cncq01-api
---
# Source: boson-net-controller/templates/net-controller-slb-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-slb-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - slb-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-net-controller/templates/net-controller-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-net-controller-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-net-controller-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
---
# Source: boson-net-controller/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-net-controller-hook
  namespace: plat-boson-service
---
# Source: boson-net-controller/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-net-controller-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-net-controller/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-net-controller-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-net-controller-hook
subjects:
- kind: ServiceAccount
  name: boson-net-controller-hook
  namespace: plat-boson-service
---
# Source: boson-net-controller/templates/post-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-net-controller-post-hook
  namespace: plat-boson-service
  labels:
    name: boson-net-controller-post-hook
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": post-upgrade,post-install
    "helm.sh/hook-weight": "3"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 10
  completions: 1
  parallelism: 3
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-net-controller-post-hook
    spec:
      serviceAccount: boson-net-controller-hook
      affinity:
        podAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-net-controller
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-net-controller-post-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "waiting all net-controller replica upgrade finish..."
          rs=$(kubectl describe deploy boson-net-controller -n plat-boson-service | grep "NewReplicaSet:" | awk '{print $2}')
          echo "got NewReplicaSet ${rs}"

          max_attempts=300
          sleep_second=6
          for ((i=0; i<max_attempts; i++)); do
            rs_desired=$(kubectl get rs "${rs}" -n plat-boson-service --no-headers | awk '{print $2}' | xargs)
            rs_ready=$(kubectl get rs "${rs}" -n plat-boson-service --no-headers | awk '{print $4}' | xargs)
            if [[ "${rs_desired}" == "${rs_ready}" ]]; then
              echo "replica ready=${rs_ready}, desired=${rs_desired}, upgrade finish!!!"
              break
            fi

            echo "replica ready=${rs_ready}, desired=${rs_desired}, attempts=${i}, retry"
            sleep $sleep_second
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: check replica timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          max_attempts=300
          sleep_second=6
          url="https://network-internal.cn-cq-01.cloudaidcapi.com/network/boson-net-controller/healthz"
          for ((i=0; i<max_attempts; i++)); do
            echo "health check url '$url'"
            http_code=$(curl -k -s -o /dev/null -w "%{http_code}" "${url}")
            if [[ $http_code -eq 200 ]]; then
              echo "http status code is 200, healthz check success"
              break
            fi

            echo "http status code is ${http_code}, healthz check fail, attempts=${i}, retry"
            sleep ${sleep_second}
          done

          if [[ "${i}" -eq "${max_attempts}" ]]; then
            echo "Error: health check url '${url}' timeout, max_attempts=${max_attempts}, sleep=${sleep_second}s"
            exit 1
          fi

          # sleep a while for net-controller leader acquire lease
          for ((i=0; i<10; i++)); do
            leader_pod=$(kubectl get leases 502abd5a.sensecore.cn -n plat-boson-service --no-headers | awk '{print $2}')
            leader_pod=${leader_pod%%_*}
            if [[ -z ${leader_pod} ]]; then
              echo "Error: boson-net-controller leader pod name not valid"
              exit 1
            fi
            if [[ "${leader_pod}" =~ "${rs}" ]]; then
              echo "got new leader pod ${leader_pod}"
              break
            else
              sleep 6
            fi
          done

          state=$(kubectl get pods "${leader_pod}" -n plat-boson-service -o jsonpath='{.status.phase}')
          if [[ "${state}" == "Running" ]]; then
            echo "check boson-net-controller leader pod state success"
            exit 0
          fi

          echo "Error: timeout while waiting for boson-net-controller leader pod state to Running, state=${state}"
          exit 1
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
---
# Source: boson-net-controller/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-net-controller-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-net-controller-pre-hook
    helm.sh/chart: boson-net-controller-1.17.0
    app.kubernetes.io/name: boson-net-controller
    app.kubernetes.io/instance: boson-net-controller
    app.kubernetes.io/version: 1.17.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-net-controller
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-net-controller-pre-hook
    spec:
      serviceAccount: boson-net-controller-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-net-controller-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          ndcUrl=http://************:5090
          echo "start NDC service check"

          response_code=$(curl -s -o /dev/null -w "%{http_code}" $ndcUrl)
          if [[ $response_code -eq 200 ]]; then
            echo "NDC service check successful"
          else
            echo "Error: NDC service check failed"
            exit 1
          fi

          echo "PG DB connection check"
          PGPASSWORD=xxxxxxxxxxxx psql -h ************ -p 32137 -U boson -d boson_service_v2 -c "select count(*) from vpcs;" > /dev/null;
          if [[ $? -eq 0 ]]; then
            echo "psql connection successful"
          else
            echo "Error: psql connection failed"
            exit 1
          fi

          if kubectl get svc -n plat-boson-service boson-net-controller-service &> /dev/null; then
                  clusterIP=$(kubectl get svc -n plat-boson-service boson-net-controller-service -o=jsonpath='{.spec.clusterIP}')

                  if [[ "$clusterIP" != "**********" ]]; then
                          echo "update service boson-net-controller-service, delete old"
                          kubectl delete svc -n plat-boson-service boson-net-controller-service
                  else
                          echo "service boson-net-controller-service check successful"
                  fi
          else
                  echo "service boson-net-controller-service not exist, check skip"
          fi
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
