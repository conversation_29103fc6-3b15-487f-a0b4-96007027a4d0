---
# Source: rdma-device-plugin/templates/rdma-device-plugin-cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rdma-device-plugin-config
  namespace: kube-system
  labels:
    helm.sh/chart: rdma-device-plugin-1.9.0
    app.kubernetes.io/name: rdma-device-plugin
    app.kubernetes.io/instance: rdma-device-plugin
    app.kubernetes.io/version: 1.9.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: rdma-device-plugin
    app.kubernetes.io/part-of: BosonService
data:
  config.json: |
    {
        "periodicUpdateInterval": 300,
        "configList": [
           {
             "resourceName": "hca",
             "resourcePrefix": "rdma",
             "rdmaHcaMax": 1024,
             "selectors": {
               "ifNames": ["ib0","ib1","ib2","ib3","ib4","ib5","ib6","ib7"]
             }
           },
           {
             "resourceName": "roce",
             "resourcePrefix": "rdma-training",
             "rdmaHcaMax": 1,
             "selectors": {
               "ifNames": ["eth10","eth11","eth12","eth13"]
             }
           }
        ]
    }
---
# Source: rdma-device-plugin/templates/rdma-device-plugin-ds.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: rdma-device-plugin
  namespace: kube-system
  labels:
    name: rdma-device-plugin
    helm.sh/chart: rdma-device-plugin-1.9.0
    app.kubernetes.io/name: rdma-device-plugin
    app.kubernetes.io/instance: rdma-device-plugin
    app.kubernetes.io/version: 1.9.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: rdma-device-plugin
    app.kubernetes.io/part-of: BosonService
spec:
  selector:
    matchLabels:
      name: rdma-device-plugin
  template:
    metadata:
      labels:
        name: rdma-device-plugin
    spec:
      hostNetwork: true
      priorityClassName: system-node-critical
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: diamond.sensetime.com/nic-training-protocol
                operator: In
                values:
                - "IB"
                - "RoCE"
              - key: diamond.sensetime.com/nic-access-mode
                operator: In
                values:
                - "PF"
              - key: diamond.sensetime.com/hardware-net-data-training
                operator: NotIn
                values:
                - "enabled"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-acp
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-cci
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-ailab
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoExecute
        operator: Exists
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: rdma-device-plugin
        image: "registry.sensetime.com/sensecore-boson/mellanox/k8s-rdma-shared-dev-plugin:v1.3.2-6-gf66e638-20231121160647"
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        volumeMounts:
          - name: device-plugin
            mountPath: /var/lib/kubelet/
          - name: device-plugin-data
            mountPath: /data/kubelet/
          - name: rdma-device-plugin-config
            mountPath: /k8s-rdma-shared-dev-plugin
          - name: devs
            mountPath: /dev/
      volumes:
        - name: device-plugin
          hostPath:
            path: /var/lib/kubelet/
        - name: device-plugin-data
          hostPath:
            path: /data/kubelet/
        - name: rdma-device-plugin-config
          configMap:
            name: rdma-device-plugin-config
            items:
            - key: config.json
              path: config.json
        - name: devs
          hostPath:
            path: /dev/
---
# Source: rdma-device-plugin/templates/rdma-device-plugin-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-rdma-device-plugin-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - rdma-device-plugin-*
        namespaces:
        - kube-system
