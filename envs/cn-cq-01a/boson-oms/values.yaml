global:
  region: cn-cq-01
  zone: cn-cq-01a
  namespace: plat-boson-infra
  envName: prod-cn-cq-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal.cn-cq-01.cloudaidcapi.com
  sslSecretName: tls-cncq01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-cq-01
    k8sInfo:
      enabled: False
      region: cn-cq-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-cq-01a
        region: cn-cq-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-cq-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5090
      southDB:
        host: ************
        port: 32137
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: ************
        port: 32137
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-cq-01z
          host: network-internal.cn-cq-01.cloudaidcapi.com
        - zone: cn-cq-01a
          host: network-internal.cn-cq-01.cloudaidcapi.com
