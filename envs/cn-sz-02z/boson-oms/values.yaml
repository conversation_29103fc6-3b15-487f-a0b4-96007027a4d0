global:
  region: cn-sz-02
  zone: cn-sz-02z
  namespace: plat-boson-service
  envName: prod-cn-sz-02z
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  omsDashboardImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.15.0-53ab42c-20240730160253
  imagePullSecret: sensecore-boson
  domainName: boson-oms.cn-sz-02.sensecoreapi.cn
  sslSecretName: tls-cnsz02-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-sz-02
    k8sInfo:
      enabled: False
      region: cn-sz-02
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-sz-02a
        region: cn-sz-02
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sz-02a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: https://network-service.cn-sz-02.sensecoreapi.cn/network/boson-ndc
      southDB:
        host: *************
        port: 35108
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: *************
        port: 35108
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-sz-02z
          host: network-internal.cn-sz-02.sensecoreapi.cn
        - zone: cn-sz-02a
          host: network-internal.cn-sz-02.sensecoreapi.cn
  omsDashboard:
    clickHouse:
      host: clickhouse-higgs.app-higgs-middleware.svc.cluster.local
      port: 9000
      user: boson
      database: boson
      password: RP03PZgeDH
