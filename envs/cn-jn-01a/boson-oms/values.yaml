global:
  region: cn-jn-01
  zone: cn-jn-01a
  namespace: plat-boson-infra
  envName: prod-cn-jn-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal.cn-jn-01.sdjstjnapi.com
  sslSecretName: tls-cnjn01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-jn-01
    k8sInfo:
      enabled: False
      region: cn-jn-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-jn-01a
        region: cn-jn-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-jn-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5091
      southDB:
        host: *************
        port: 46747
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: *************
        port: 46747
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-jn-01z
          host: network-internal.cn-jn-01.sdjstjnapi.com
        - zone: cn-jn-01a
          host: network-internal.cn-jn-01.sdjstjnapi.com
