#!/usr/bin/env bash

# echo and execute bash command
function sys_exec() {
    if [ "$#" -eq 0 ]; then
        echo "FATAL: sys_exec <command>"
        kill $$
    fi
    cmd=${*}
    echo
    echo "# Cmd: ${cmd}"
    ${cmd}
    return $?
}

# check given domain result from dns servers
function check_domain_result() {
    domain="${1}"
    dns_servers="${2}"
    expected="${3}"
    skip_wildcard_domain="${4}"
    for dns_server in ${dns_servers}; do
        # resolve raw domain result
        echo "nslookup ${domain} ${dns_server}"
        set +e
        actual=$(nslookup -timeout=1 "${domain}" "${dns_server}" | grep Address | grep -v '#53' | cut -d ' ' -f 2 | sort | tr '\n' ';')
        set -e
        if [[ "${expected}" == "${actual}" ]]; then
            echo -e "\e[32mCHECK PASS\e[0m: dns server: ${dns_server}, nslookup \e[32m ${domain} \e[0m results are right. Result: \e[32m ${actual} \e[0m"
        else
            echo -e "\e[31mCHECK FAIL\e[0m: dns server: ${dns_server}, nslookup \e[31m ${domain} \e[0m results are wrong. Result: \e[31m ${actual} \e[0m, Expected: ${expected}"
        fi

        if [[ "${skip_wildcard_domain}" == "skip" ]]; then
            continue
        fi

        # resolve wildcard domain result
        echo "nslookup bucket.${domain} ${dns_server}"
        set +e
        actual=$(nslookup -timeout=1 "bucket.${domain}" "${dns_server}" | grep Address | grep -v '#53' | cut -d ' ' -f 2 | sort | tr '\n' ';')
        set -e
        if [[ "${expected}" == "${actual}" ]]; then
            echo -e "\e[32mCHECK PASS\e[0m: dns server: ${dns_server}, nslookup \e[32m bucket.${domain} \e[0m results are right. Result: \e[32m ${actual} \e[0m"
        else
            echo -e "\e[31mCHECK FAIL\e[0m: dns server: ${dns_server}, nslookup \e[31m bucket.${domain} \e[0m results are wrong. Result: \e[31m ${actual} \e[0m, Expected: ${expected}"
        fi
    done
}

# check ip availability
function check_ip_availability() {
    echo "curl --connect-timeout 1 -k -s -o /dev/null -w %{http_code}  https://${1}"
    if [[ $(curl --connect-timeout 1 -k -s -o /dev/null -w "%{http_code}"  https://"${1}") == 200 ]]; then
        echo -e "\e[32mCHECK PASS\e[0m: aoss endpoint \e[32m https://${1}/ \e[0m is available"
    else
        echo -e "\e[31mCHECK FAIL\e[0m: Cannot connect aoss endpoint \e[31m https://${1}/ \e[0m"
    fi
}
