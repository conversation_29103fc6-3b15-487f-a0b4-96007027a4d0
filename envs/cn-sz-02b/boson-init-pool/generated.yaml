---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-sz-02b
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ************/24
        gateway: ************
        ips:
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "10.116.165.48"
        - "10.116.165.49"
        - "10.116.165.50"
        - "10.116.165.51"
        - "10.116.165.52"
        - "10.116.165.53"
        - "10.116.165.54"
        - "10.116.165.55"
        - "10.116.165.56"
        - "10.116.165.57"
        - "10.116.165.58"
        - "10.116.165.59"
        - "10.116.165.60"
        - "10.116.165.61"
        - "10.116.165.62"
        - "10.116.165.63"
        - "10.116.165.64"
        - "10.116.165.65"
        - "10.116.165.66"
        - "10.116.165.67"
        - "10.116.165.68"
        - "10.116.165.69"
        - "10.116.165.70"
        - "10.116.165.71"
        - "10.116.165.72"
        - "10.116.165.73"
        - "10.116.165.74"
        - "10.116.165.75"
        - "10.116.165.76"
        - "10.116.165.77"
        - "10.116.165.78"
        - "10.116.165.79"
        - "10.116.165.80"
        - "10.116.165.81"
        - "10.116.165.82"
        - "10.116.165.83"
        - "10.116.165.84"
        - "10.116.165.85"
        - "10.116.165.86"
        - "10.116.165.87"
        - "10.116.165.88"
        - "10.116.165.89"
        - "10.116.165.90"
        - "10.116.165.91"
        - "10.116.165.92"
        - "10.116.165.93"
        - "10.116.165.94"
        - "10.116.165.95"
        - "10.116.165.96"
        - "10.116.165.97"
        - "10.116.165.98"
        - "10.116.165.99"
        - "************00"
        - "************01"
        - "************02"
        - "************03"
        - "************04"
        - "************05"
        - "************06"
        - "************07"
        - "************08"
        - "************09"
        - "************10"
        - "************11"
        - "************12"
        - "************13"
        - "************14"
        - "************15"
        - "************16"
        - "************17"
        - "************18"
        - "************19"
        - "************20"
        - "************21"
        - "************22"
        - "************23"
        - "************24"
        - "************25"
        - "************26"
        - "************27"
        - "************28"
        - "************29"
        - "************30"
        - "************31"
        - "************32"
        - "************33"
        - "************34"
        - "************35"
        - "************36"
        - "************37"
        - "************38"
        - "************39"
        - "************40"
        - "************41"
        - "************42"
        - "************43"
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        - "************55"
        - "************56"
        - "************57"
        - "************58"
        - "************59"
        - "************60"
        - "************61"
        - "************62"
        - "************63"
        - "************64"
        - "************65"
        - "************66"
        - "************67"
        - "************68"
        - "************69"
        - "************70"
        - "************71"
        - "************72"
        - "************73"
        - "************74"
        - "************75"
        - "************76"
        - "************77"
        - "************78"
        - "************79"
        - "************80"
        - "************81"
        - "************82"
        - "************83"
        - "************84"
        - "************85"
        - "************86"
        - "************87"
        - "************88"
        - "************89"
        - "************90"
        - "************91"
        - "************92"
        - "************93"
        - "************94"
        - "************95"
        - "************96"
        - "************97"
        - "************98"
        - "************99"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
      slbs:
        cidr: ***********/23
        gateway: ***********
        ips:
      eips:
        cidr: *************/27
        gateway: *************
        ips:
        sku: CHINA_MOBILE
      cidr_pools:
        ib:
        - cidr: ************/26
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 2500
          scope: TRAINING
          reserved: ************..************,*************..*************
        - cidr: *************/26
          gateway: *************
          zone_prp: cn-sz-02b
          vni: 2501
          scope: TRAINING
          reserved: *************..*************,************22..************26
        - cidr: ************28/26
          gateway: ************29
          zone_prp: cn-sz-02b
          vni: 2502
          scope: TRAINING
          reserved: ************29..************37,************86..************90
        - cidr: ************92/26
          gateway: ************93
          zone_prp: cn-sz-02b
          vni: 2503
          scope: TRAINING
          reserved: ************93..**************,**************..**************
        - cidr: ************/26
          gateway: 10.110.129.1
          zone_prp: cn-sz-02b
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-sz-02b
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-sz-02b
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-sz-02b
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-sz-02b
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-sz-02b
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-sz-02b
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-sz-02b
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-sz-02b
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-sz-02b
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-sz-02b
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-sz-02b
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-sz-02b
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-sz-02b
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-sz-02b
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-sz-02b
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-sz-02b
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-sz-02b
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-sz-02b
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-sz-02b
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-sz-02b
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-sz-02b
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-sz-02b
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-sz-02b
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-sz-02b
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-sz-02b
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-sz-02b
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-sz-02b
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.120.192.0/26
          gateway: 10.120.192.1
          zone_prp: cn-sz-02b
          vni: 2500
          scope: DATA
          reserved: 10.120.192.1..10.120.192.9,10.120.192.58..10.120.192.62
        - cidr: 10.120.192.64/26
          gateway: 10.120.192.65
          zone_prp: cn-sz-02b
          vni: 2501
          scope: DATA
          reserved: 10.120.192.65..10.120.192.73,10.120.192.122..10.120.192.126
        - cidr: 10.120.192.128/26
          gateway: 10.120.192.129
          zone_prp: cn-sz-02b
          vni: 2502
          scope: DATA
          reserved: 10.120.192.129..10.120.192.137,10.120.192.186..10.120.192.190
        - cidr: 10.120.192.192/26
          gateway: 10.120.192.193
          zone_prp: cn-sz-02b
          vni: 2503
          scope: DATA
          reserved: 10.120.192.193..10.120.192.201,10.120.192.250..10.120.192.254
        - cidr: 10.120.193.0/26
          gateway: 10.120.193.1
          zone_prp: cn-sz-02b
          vni: 2504
          scope: DATA
          reserved: 10.120.193.1..10.120.193.9,10.120.193.58..10.120.193.62
        - cidr: 10.120.193.64/26
          gateway: 10.120.193.65
          zone_prp: cn-sz-02b
          vni: 2505
          scope: DATA
          reserved: 10.120.193.65..10.120.193.73,10.120.193.122..10.120.193.126
        - cidr: 10.120.193.128/26
          gateway: 10.120.193.129
          zone_prp: cn-sz-02b
          vni: 2506
          scope: DATA
          reserved: 10.120.193.129..10.120.193.137,10.120.193.186..10.120.193.190
        - cidr: 10.120.193.192/26
          gateway: 10.120.193.193
          zone_prp: cn-sz-02b
          vni: 2507
          scope: DATA
          reserved: 10.120.193.193..10.120.193.201,10.120.193.250..10.120.193.254
        - cidr: 10.120.194.0/26
          gateway: 10.120.194.1
          zone_prp: cn-sz-02b
          vni: 2508
          scope: DATA
          reserved: 10.120.194.1..10.120.194.9,10.120.194.58..10.120.194.62
        - cidr: 10.120.194.64/26
          gateway: 10.120.194.65
          zone_prp: cn-sz-02b
          vni: 2509
          scope: DATA
          reserved: 10.120.194.65..10.120.194.73,10.120.194.122..10.120.194.126
        - cidr: 10.120.194.128/26
          gateway: 10.120.194.129
          zone_prp: cn-sz-02b
          vni: 2510
          scope: DATA
          reserved: 10.120.194.129..10.120.194.137,10.120.194.186..10.120.194.190
        - cidr: 10.120.194.192/26
          gateway: 10.120.194.193
          zone_prp: cn-sz-02b
          vni: 2511
          scope: DATA
          reserved: 10.120.194.193..10.120.194.201,10.120.194.250..10.120.194.254
        - cidr: 10.120.195.0/26
          gateway: 10.120.195.1
          zone_prp: cn-sz-02b
          vni: 2512
          scope: DATA
          reserved: 10.120.195.1..10.120.195.9,10.120.195.58..10.120.195.62
        - cidr: 10.120.195.64/26
          gateway: 10.120.195.65
          zone_prp: cn-sz-02b
          vni: 2513
          scope: DATA
          reserved: 10.120.195.65..10.120.195.73,10.120.195.122..10.120.195.126
        - cidr: 10.120.195.128/26
          gateway: 10.120.195.129
          zone_prp: cn-sz-02b
          vni: 2514
          scope: DATA
          reserved: 10.120.195.129..10.120.195.137,10.120.195.186..10.120.195.190
        - cidr: 10.120.195.192/26
          gateway: 10.120.195.193
          zone_prp: cn-sz-02b
          vni: 2515
          scope: DATA
          reserved: 10.120.195.193..10.120.195.201,10.120.195.250..10.120.195.254
        - cidr: 10.120.196.0/26
          gateway: 10.120.196.1
          zone_prp: cn-sz-02b
          vni: 2516
          scope: DATA
          reserved: 10.120.196.1..10.120.196.9,10.120.196.58..10.120.196.62
        - cidr: 10.120.196.64/26
          gateway: 10.120.196.65
          zone_prp: cn-sz-02b
          vni: 2517
          scope: DATA
          reserved: 10.120.196.65..10.120.196.73,10.120.196.122..10.120.196.126
        - cidr: 10.120.196.128/26
          gateway: 10.120.196.129
          zone_prp: cn-sz-02b
          vni: 2518
          scope: DATA
          reserved: 10.120.196.129..10.120.196.137,10.120.196.186..10.120.196.190
        - cidr: 10.120.196.192/26
          gateway: 10.120.196.193
          zone_prp: cn-sz-02b
          vni: 2519
          scope: DATA
          reserved: 10.120.196.193..10.120.196.201,10.120.196.250..10.120.196.254
        - cidr: 10.120.197.0/26
          gateway: 10.120.197.1
          zone_prp: cn-sz-02b
          vni: 2520
          scope: DATA
          reserved: 10.120.197.1..10.120.197.9,10.120.197.58..10.120.197.62
        - cidr: 10.120.197.64/26
          gateway: 10.120.197.65
          zone_prp: cn-sz-02b
          vni: 2521
          scope: DATA
          reserved: 10.120.197.65..10.120.197.73,10.120.197.122..10.120.197.126
        - cidr: 10.120.197.128/26
          gateway: 10.120.197.129
          zone_prp: cn-sz-02b
          vni: 2522
          scope: DATA
          reserved: 10.120.197.129..10.120.197.137,10.120.197.186..10.120.197.190
        - cidr: 10.120.197.192/26
          gateway: 10.120.197.193
          zone_prp: cn-sz-02b
          vni: 2523
          scope: DATA
          reserved: 10.120.197.193..10.120.197.201,10.120.197.250..10.120.197.254
        - cidr: 10.120.198.0/26
          gateway: 10.120.198.1
          zone_prp: cn-sz-02b
          vni: 2524
          scope: DATA
          reserved: 10.120.198.1..10.120.198.9,10.120.198.58..10.120.198.62
        - cidr: 10.120.198.64/26
          gateway: 10.120.198.65
          zone_prp: cn-sz-02b
          vni: 2525
          scope: DATA
          reserved: 10.120.198.65..10.120.198.73,10.120.198.122..10.120.198.126
        - cidr: 10.120.198.128/26
          gateway: 10.120.198.129
          zone_prp: cn-sz-02b
          vni: 2526
          scope: DATA
          reserved: 10.120.198.129..10.120.198.137,10.120.198.186..10.120.198.190
        - cidr: 10.120.198.192/26
          gateway: 10.120.198.193
          zone_prp: cn-sz-02b
          vni: 2527
          scope: DATA
          reserved: 10.120.198.193..10.120.198.201,10.120.198.250..10.120.198.254
        - cidr: 10.120.199.0/26
          gateway: 10.120.199.1
          zone_prp: cn-sz-02b
          vni: 2528
          scope: DATA
          reserved: 10.120.199.1..10.120.199.9,10.120.199.58..10.120.199.62
        - cidr: 10.120.199.64/26
          gateway: 10.120.199.65
          zone_prp: cn-sz-02b
          vni: 2529
          scope: DATA
          reserved: 10.120.199.65..10.120.199.73,10.120.199.122..10.120.199.126
        - cidr: 10.120.199.128/26
          gateway: 10.120.199.129
          zone_prp: cn-sz-02b
          vni: 2530
          scope: DATA
          reserved: 10.120.199.129..10.120.199.137,10.120.199.186..10.120.199.190
        - cidr: 10.120.199.192/26
          gateway: 10.120.199.193
          zone_prp: cn-sz-02b
          vni: 2531
          scope: DATA
          reserved: 10.120.199.193..10.120.199.201,10.120.199.250..10.120.199.254
        - cidr: 10.120.128.0/26
          gateway: 10.120.128.1
          zone_prp: cn-sz-02b
          vni: 2500
          scope: SERVICE
          reserved: 10.120.128.1..10.120.128.9,10.120.128.58..10.120.128.62
        - cidr: 10.120.128.64/26
          gateway: 10.120.128.65
          zone_prp: cn-sz-02b
          vni: 2501
          scope: SERVICE
          reserved: 10.120.128.65..10.120.128.73,10.120.128.122..10.120.128.126
        - cidr: 10.120.128.128/26
          gateway: 10.120.128.129
          zone_prp: cn-sz-02b
          vni: 2502
          scope: SERVICE
          reserved: 10.120.128.129..10.120.128.137,10.120.128.186..10.120.128.190
        - cidr: 10.120.128.192/26
          gateway: 10.120.128.193
          zone_prp: cn-sz-02b
          vni: 2503
          scope: SERVICE
          reserved: 10.120.128.193..10.120.128.201,10.120.128.250..10.120.128.254
        - cidr: 10.120.129.0/26
          gateway: 10.120.129.1
          zone_prp: cn-sz-02b
          vni: 2504
          scope: SERVICE
          reserved: 10.120.129.1..10.120.129.9,10.120.129.58..10.120.129.62
        - cidr: 10.120.129.64/26
          gateway: 10.120.129.65
          zone_prp: cn-sz-02b
          vni: 2505
          scope: SERVICE
          reserved: 10.120.129.65..10.120.129.73,10.120.129.122..10.120.129.126
        - cidr: 10.120.129.128/26
          gateway: 10.120.129.129
          zone_prp: cn-sz-02b
          vni: 2506
          scope: SERVICE
          reserved: 10.120.129.129..10.120.129.137,10.120.129.186..10.120.129.190
        - cidr: 10.120.129.192/26
          gateway: 10.120.129.193
          zone_prp: cn-sz-02b
          vni: 2507
          scope: SERVICE
          reserved: 10.120.129.193..10.120.129.201,10.120.129.250..10.120.129.254
        - cidr: 10.120.130.0/26
          gateway: 10.120.130.1
          zone_prp: cn-sz-02b
          vni: 2508
          scope: SERVICE
          reserved: 10.120.130.1..10.120.130.9,10.120.130.58..10.120.130.62
        - cidr: 10.120.130.64/26
          gateway: 10.120.130.65
          zone_prp: cn-sz-02b
          vni: 2509
          scope: SERVICE
          reserved: 10.120.130.65..10.120.130.73,10.120.130.122..10.120.130.126
        - cidr: 10.120.130.128/26
          gateway: 10.120.130.129
          zone_prp: cn-sz-02b
          vni: 2510
          scope: SERVICE
          reserved: 10.120.130.129..10.120.130.137,10.120.130.186..10.120.130.190
        - cidr: 10.120.130.192/26
          gateway: 10.120.130.193
          zone_prp: cn-sz-02b
          vni: 2511
          scope: SERVICE
          reserved: 10.120.130.193..10.120.130.201,10.120.130.250..10.120.130.254
        - cidr: 10.120.131.0/26
          gateway: 10.120.131.1
          zone_prp: cn-sz-02b
          vni: 2512
          scope: SERVICE
          reserved: 10.120.131.1..10.120.131.9,10.120.131.58..10.120.131.62
        - cidr: 10.120.131.64/26
          gateway: 10.120.131.65
          zone_prp: cn-sz-02b
          vni: 2513
          scope: SERVICE
          reserved: 10.120.131.65..10.120.131.73,10.120.131.122..10.120.131.126
        - cidr: 10.120.131.128/26
          gateway: 10.120.131.129
          zone_prp: cn-sz-02b
          vni: 2514
          scope: SERVICE
          reserved: 10.120.131.129..10.120.131.137,10.120.131.186..10.120.131.190
        - cidr: 10.120.131.192/26
          gateway: 10.120.131.193
          zone_prp: cn-sz-02b
          vni: 2515
          scope: SERVICE
          reserved: 10.120.131.193..10.120.131.201,10.120.131.250..10.120.131.254
        - cidr: 10.120.132.0/26
          gateway: 10.120.132.1
          zone_prp: cn-sz-02b
          vni: 2516
          scope: SERVICE
          reserved: 10.120.132.1..10.120.132.9,10.120.132.58..10.120.132.62
        - cidr: 10.120.132.64/26
          gateway: 10.120.132.65
          zone_prp: cn-sz-02b
          vni: 2517
          scope: SERVICE
          reserved: 10.120.132.65..10.120.132.73,10.120.132.122..10.120.132.126
        - cidr: 10.120.132.128/26
          gateway: 10.120.132.129
          zone_prp: cn-sz-02b
          vni: 2518
          scope: SERVICE
          reserved: 10.120.132.129..10.120.132.137,10.120.132.186..10.120.132.190
        - cidr: 10.120.132.192/26
          gateway: 10.120.132.193
          zone_prp: cn-sz-02b
          vni: 2519
          scope: SERVICE
          reserved: 10.120.132.193..10.120.132.201,10.120.132.250..10.120.132.254
        - cidr: 10.120.133.0/26
          gateway: 10.120.133.1
          zone_prp: cn-sz-02b
          vni: 2520
          scope: SERVICE
          reserved: 10.120.133.1..10.120.133.9,10.120.133.58..10.120.133.62
        - cidr: 10.120.133.64/26
          gateway: 10.120.133.65
          zone_prp: cn-sz-02b
          vni: 2521
          scope: SERVICE
          reserved: 10.120.133.65..10.120.133.73,10.120.133.122..10.120.133.126
        - cidr: 10.120.133.128/26
          gateway: 10.120.133.129
          zone_prp: cn-sz-02b
          vni: 2522
          scope: SERVICE
          reserved: 10.120.133.129..10.120.133.137,10.120.133.186..10.120.133.190
        - cidr: 10.120.133.192/26
          gateway: 10.120.133.193
          zone_prp: cn-sz-02b
          vni: 2523
          scope: SERVICE
          reserved: 10.120.133.193..10.120.133.201,10.120.133.250..10.120.133.254
        - cidr: 10.120.134.0/26
          gateway: 10.120.134.1
          zone_prp: cn-sz-02b
          vni: 2524
          scope: SERVICE
          reserved: 10.120.134.1..10.120.134.9,10.120.134.58..10.120.134.62
        - cidr: 10.120.134.64/26
          gateway: 10.120.134.65
          zone_prp: cn-sz-02b
          vni: 2525
          scope: SERVICE
          reserved: 10.120.134.65..10.120.134.73,10.120.134.122..10.120.134.126
        - cidr: 10.120.134.128/26
          gateway: 10.120.134.129
          zone_prp: cn-sz-02b
          vni: 2526
          scope: SERVICE
          reserved: 10.120.134.129..10.120.134.137,10.120.134.186..10.120.134.190
        - cidr: 10.120.134.192/26
          gateway: 10.120.134.193
          zone_prp: cn-sz-02b
          vni: 2527
          scope: SERVICE
          reserved: 10.120.134.193..10.120.134.201,10.120.134.250..10.120.134.254
        - cidr: 10.120.135.0/26
          gateway: 10.120.135.1
          zone_prp: cn-sz-02b
          vni: 2528
          scope: SERVICE
          reserved: 10.120.135.1..10.120.135.9,10.120.135.58..10.120.135.62
        - cidr: 10.120.135.64/26
          gateway: 10.120.135.65
          zone_prp: cn-sz-02b
          vni: 2529
          scope: SERVICE
          reserved: 10.120.135.65..10.120.135.73,10.120.135.122..10.120.135.126
        - cidr: 10.120.135.128/26
          gateway: 10.120.135.129
          zone_prp: cn-sz-02b
          vni: 2530
          scope: SERVICE
          reserved: 10.120.135.129..10.120.135.137,10.120.135.186..10.120.135.190
        - cidr: 10.120.135.192/26
          gateway: 10.120.135.193
          zone_prp: cn-sz-02b
          vni: 2531
          scope: SERVICE
          reserved: 10.120.135.193..10.120.135.201,10.120.135.250..10.120.135.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02b
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1500
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1501
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1502
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1503
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1504
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1505
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1506
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1507
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1508
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1509
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1510
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1511
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1512
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1513
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1514
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1515
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1516
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1517
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1518
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1519
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1520
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1521
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1522
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1523
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1524
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1525
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1526
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1527
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1528
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1529
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1530
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02b
          vni: 1531
          scope: TRAINING
          reserved: ************..************,**************..**************

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-sz-02b
      vpc_default_region: cn-sz-02b
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
