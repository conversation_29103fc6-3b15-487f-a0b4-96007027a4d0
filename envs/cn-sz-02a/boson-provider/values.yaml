global:
  envName: prod-cn-sz-02a
  bosonProviderImage: registry.sensetime.com/sensecore-boson/boson-provider:v1.20.0-21-g07bb81fc-20250521160632
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-internal.cn-sz-02.sensecoreapi.cn
  sslSecretName: tls-cnsz02-api
  nodeSelectorTerms: []
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  pgsql:
    host: *************
    port: 35108
    user: boson
    db: boson_service_v2
    password: xxxxxxxxxxxx
  rocketmq:
    default:
      nameServers:
      - ************:9876
      - ************:9876
      instanceName: sensetime-core-network-v1-cn-sz-02z
      topic: sensetime-core-network-vpc-v1-cn-sz-02z
      eip_topic: sensetime-core-network-eip-v1-cn-sz-02a
      rmAckTopic: sensetime-core-rm-resource-state-sync
      bossAckTopic: sensetime-core-resource-operation-result
      brokerConsumerGroupName: sensetime-core-network-v1-cn-sz-02a-consumer
      eip_consumer_group_name: sensetime-core-network-v1-cn-sz-02a-consumer-eip
      brokerRMProducerGroupName: sensetime-core-network-v1-cn-sz-02a-producer-rm
      brokerBossProducerGroupName: sensetime-core-network-v1-cn-sz-02a-producer-boss
      brokerNoticeProducerGroupName: sensetime-core-network-v1-cn-sz-02a-producer-Notice
      slb_topic: sensetime-core-network-slb-v1-cn-sz-02a
      slb_consumer_group_name: sensetime-core-network-v1-cn-sz-02a-consumer-slb
      accessKey: rocketmq-ak-boson-provider
      secretKey: xxxxxxxxxxxx
    cloudAudit:
      nameServers:
      - ************:9876
      - ************:9876
      instanceName: sensetime-core-networkCloudAudit-v1-cn-sz-02z
      topics:
        eip:
          topic: sensetime-core-trail-eip-operation
          producerGroupName: sensetime-core-trail-eip-producer
          accessKey: rocketmq-ak-eip-servcie
          secretKey: xxxxxxxxxxxx
        vpc:
          topic: sensetime-core-trail-vpc-operation
          producerGroupName: sensetime-core-trail-vpc-producer
          accessKey: rocketmq-ak-vpc-servcie
          secretKey: xxxxxxxxxxxx
        dc:
          topic: sensetime-core-trail-dc-operation
          producerGroupName: sensetime-core-trail-dc-producer
          accessKey: rocketmq-ak-dc-service
          secretKey: xxxxxxxxxxxx
        slb:
          topic: sensetime-core-trail-slb-operation
          producerGroupName: sensetime-core-trail-slb-producer
          accessKey: rocketmq-ak-slb-service
          secretKey: xxxxxxxxxxxx
  bosonProvider:
    generate_deployment_annotations_timestamp: true
    defaults:
      vpc_cidr: **********/16
      geneve_subnet_cidr: **********/21
      dc_vxlan_cidr: **********/24
      dc_console_cidr: ？？？
      zone_name_for_az_resource: cn-sz-02a
      zone_name_for_region_resource: cn-sz-02z
      region: cn-sz-02
      az: cn-sz-02a
      prp: cn-sz-02a-prp01
      dgw:
        enable: true
        policy_cidr: ***********/21,**********/32,**********/32,**********/32
      training_networks:
        vlan_acl_nic_max_count: 0
        kube_ovn:
        - if_id: roce_0
          gw: **********
        bms:
          gws:
          - ************
      bms_master_nic: bond1
      cloud_audit_enable: true
      vpc_default_acls: []
      ts_gateway_nodes: null
      vpc_acls:
      - dest_ip: ************/29
        protocol: tcp
        dest_port: 5000,8080,2049
        priority: 31150
        action: allow
        description: TK满足客户拷盘SOP能力，支持访问临时NAS文件存储 5000、8080、2049 个别端口
      - dest_ip: ************/29
        protocol: tcp
        dest_port: 18000:18010
        priority: 31100
        action: allow
        description: TK满足客户拷盘SOP能力，支持访问临时NAS文件存储 18000~18010 端口范围
      - dest_ip: **********/32
        src_ip: ************/21
        protocol: all
        dest_port: ''
        priority: 31050
        action: deny
        description: H系列GPU裸金属屏蔽公有云科学上网
      - dest_ip: **************/32,***************/32,**************/32
        protocol: tcp
        dest_port: 3128,10128
        priority: 31000
        action: allow
        description: H系列GPU放行马来公网***************和内网代理**************，以及国内代理**************
      - dest_ip: *************/31,*************/31
        protocol: tcp
        dest_port: '443'
        priority: 30950
        action: allow
        description: 租户自定义监控从VPC向KK上传用户数据访问higgs-iap-teleport.sensecoreapi.cn服务443，计算实例访问CCR内网IP端口加速
      - dest_ip: **********/32,***********/24,**********/32,**********/32
        protocol: all
        dest_port: ''
        priority: 30900
        action: allow
        description: vpc-nat-gw 放行发卡 subnet、coredns、vpc-nat-gw 代理服务的三个 IPs
      - dest_ip: *************/32
        protocol: tcp
        dest_port: 11010,11011,53
        priority: 30850
        action: allow
        description: 访问 TK 的 DNS Gateway 53端口的 tcp，访问算力池 TK 的 Transom 服务 11010 和 11011 端口的 tcp
      - dest_ip: *************/32
        protocol: udp
        dest_port: '53'
        priority: 30800
        action: allow
        description: 访问 TK 的 DNS Gateway 53端口的 udp 协议
      - dest_ip: *************/31
        protocol: tcp
        dest_port: '30636'
        priority: 30750
        action: allow
        description: 云管 IAM 的 POSIX 权限认证需要访问 KK Ingress 的 ldap 服务的 node port 端口
      - dest_ip: *************/31
        protocol: tcp
        dest_port: 33080,33023,33024,33025,32200,38080,30080,33200,30843
        priority: 30700
        action: allow
        description: 开发机pod 通过 aicl-proxy-internal.[region].[domain].com 访问 iap 服务33080和33023~25，通过aicl-audit-internal.[region].[domain].cn访问audit服务32200，通过ams-inference-gateway-internal-data.sensecoreapi.cn服务38080，通过ams-inference-gateway-internal-data.sensecoreapi.cn服务v2版本的30080、ams-mq-proxy-data.sensecoreapi.cn服务33200、租户iap agent访问higgs-iap-teleport.sensecoreapi.cn服务30843
      - dest_ip: *************/32
        protocol: tcp
        dest_port: '20001'
        priority: 30650
        action: allow
        description: 开发机pod 通过 cci-proxy-internal.[region].[domain].com 访问 dps 服务
      - dest_ip: ***********/24
        protocol: all
        dest_port: ''
        priority: 30600
        action: allow
        description: 租户访问所有zone文件存储数据网Gateway入口
      - dest_ip: *************/32,*************/31,*************/31,**************/31
        protocol: tcp
        dest_port: 80,443
        priority: 30550
        action: allow
        description: 租户访问所有zone对象存储external、business、data数据网入口
      - dest_ip: *************/32,*************/31,*************/31,**************/31
        protocol: icmp
        dest_port: ''
        priority: 30500
        action: allow
        description: 租户ping所有zone对象存储external、business、data数据网入口
      - dest_ip: *************/31
        protocol: tcp
        dest_port: 30033,30037,30039,30034,30035,30040
        priority: 30450
        action: allow
        description: PushGateway，裸金属的 quarkfs-client 访问 30033，裸金属的新版 quarkfs-client 访问 30037/30039，裸金属接云监控访问 30034，AMS 服务访问 30035，ECS的带内监控用30040
      - dest_ip: *************/31
        protocol: tcp
        dest_port: 51808,32766
        priority: 30400
        action: allow
        description: KK 裸金属云助手 VIP, 裸金属镜像下载备用策略访问 32766
      - dest_ip: *************/31
        protocol: tcp
        dest_port: '53'
        priority: 30350
        action: allow
        description: 访问 boson 在 KK 部署的 internal DNS 的 tcp 协议
      - dest_ip: *************/31
        protocol: udp
        dest_port: '53'
        priority: 30300
        action: allow
        description: 访问 boson 在 KK 部署的 internal DNS 的 udp 协议
      - dest_ip: ********/31
        protocol: tcp
        dest_port: '53'
        priority: 30250
        action: allow
        description: 访问企信部 DNS 的 tcp 协议
      - dest_ip: ********/31
        protocol: udp
        dest_port: '53'
        priority: 30200
        action: allow
        description: 访问企信部 DNS 的 udp 协议
      - dest_ip: **********/32
        protocol: tcp
        dest_port: '3128'
        priority: 30150
        action: allow
        description: 公有云科学上网
      - dest_ip: 10.0.0.0/8,**********/12,***********/16
        protocol: all
        dest_port: ''
        priority: 30100
        action: deny
        description: 商汤内网所有服务主机禁止访问 外部通过vpn连入的商汤的网段没有考虑
      - dest_ip: *************/27
        src_ip: ************/21
        protocol: all
        dest_port: ''
        priority: 30050
        action: allow
        description: H系列GPU裸金属放通集群EIP，配合规则gpu_h_bms_drop_other_internet_access，限制公网访问
      - src_ip: ************/21
        protocol: all
        dest_port: ''
        priority: 30000
        action: deny
        description: H系列GPU裸金属屏蔽除 gpu_h_bms_allow_eip 定义之外的其他公网访问
