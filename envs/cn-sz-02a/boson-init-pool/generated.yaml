---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-sz-02a
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ***********/24
        gateway: ***********
        ips:
        - "***********0"
        - "***********1"
        - "***********2"
        - "***********3"
        - "***********4"
        - "***********5"
        - "***********6"
        - "***********7"
        - "***********8"
        - "***********9"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "10.115.69.49"
        - "10.115.69.50"
        - "10.115.69.51"
        - "10.115.69.52"
        - "10.115.69.53"
        - "10.115.69.54"
        - "10.115.69.55"
        - "10.115.69.56"
        - "10.115.69.57"
        - "10.115.69.58"
        - "10.115.69.59"
        - "10.115.69.60"
        - "10.115.69.61"
        - "10.115.69.62"
        - "10.115.69.63"
        - "10.115.69.64"
        - "10.115.69.65"
        - "10.115.69.66"
        - "10.115.69.67"
        - "10.115.69.68"
        - "10.115.69.69"
        - "10.115.69.70"
        - "10.115.69.71"
        - "10.115.69.72"
        - "10.115.69.73"
        - "10.115.69.74"
        - "10.115.69.75"
        - "10.115.69.76"
        - "10.115.69.77"
        - "10.115.69.78"
        - "10.115.69.79"
        - "10.115.69.80"
        - "10.115.69.81"
        - "10.115.69.82"
        - "10.115.69.83"
        - "10.115.69.84"
        - "10.115.69.85"
        - "10.115.69.86"
        - "10.115.69.87"
        - "10.115.69.88"
        - "10.115.69.89"
        - "10.115.69.90"
        - "10.115.69.91"
        - "10.115.69.92"
        - "10.115.69.93"
        - "10.115.69.94"
        - "10.115.69.95"
        - "10.115.69.96"
        - "10.115.69.97"
        - "10.115.69.98"
        - "10.115.69.99"
        - "***********00"
        - "***********01"
        - "***********02"
        - "***********03"
        - "***********04"
        - "***********05"
        - "***********06"
        - "***********07"
        - "***********08"
        - "***********09"
        - "***********10"
        - "***********11"
        - "***********12"
        - "***********13"
        - "***********14"
        - "***********15"
        - "***********16"
        - "***********17"
        - "***********18"
        - "***********19"
        - "***********20"
        - "***********21"
        - "***********22"
        - "***********23"
        - "***********24"
        - "***********25"
        - "***********26"
        - "***********27"
        - "***********28"
        - "***********29"
        - "***********30"
        - "***********31"
        - "***********32"
        - "***********33"
        - "***********34"
        - "***********35"
        - "***********36"
        - "***********37"
        - "***********38"
        - "***********39"
        - "***********40"
        - "***********41"
        - "***********42"
        - "***********43"
        - "***********44"
        - "***********45"
        - "***********46"
        - "***********47"
        - "***********48"
        - "***********49"
        - "***********50"
        - "***********51"
        - "***********52"
        - "***********53"
        - "***********54"
        - "***********55"
        - "***********56"
        - "***********57"
        - "***********58"
        - "***********59"
        - "***********60"
        - "***********61"
        - "***********62"
        - "***********63"
        - "***********64"
        - "***********65"
        - "***********66"
        - "***********67"
        - "***********68"
        - "***********69"
        - "***********70"
        - "***********71"
        - "***********72"
        - "***********73"
        - "***********74"
        - "***********75"
        - "***********76"
        - "***********77"
        - "***********78"
        - "***********79"
        - "***********80"
        - "***********81"
        - "***********82"
        - "***********83"
        - "***********84"
        - "***********85"
        - "***********86"
        - "***********87"
        - "***********88"
        - "***********89"
        - "***********90"
        - "***********91"
        - "***********92"
        - "***********93"
        - "***********94"
        - "***********95"
        - "***********96"
        - "***********97"
        - "***********98"
        - "***********99"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
      slbs:
        cidr: ***********/23
        gateway: ***********
        ips:
        - "***********0"
        - "************"
        - "***********2"
        - "************"
        - "***********4"
        - "***********5"
        - "***********6"
        - "***********7"
        - "***********8"
        - "***********9"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "10.115.70.49"
        - "10.115.70.50"
        - "10.115.70.51"
        - "10.115.70.52"
        - "10.115.70.53"
        - "10.115.70.54"
        - "10.115.70.55"
        - "10.115.70.56"
        - "10.115.70.57"
        - "10.115.70.58"
        - "10.115.70.59"
        - "10.115.70.60"
        - "10.115.70.61"
        - "10.115.70.62"
        - "10.115.70.63"
        - "10.115.70.64"
        - "10.115.70.65"
        - "10.115.70.66"
        - "10.115.70.67"
        - "10.115.70.68"
        - "10.115.70.69"
        - "10.115.70.70"
        - "10.115.70.71"
        - "10.115.70.72"
        - "10.115.70.73"
        - "10.115.70.74"
        - "10.115.70.75"
        - "10.115.70.76"
        - "10.115.70.77"
        - "10.115.70.78"
        - "10.115.70.79"
        - "10.115.70.80"
        - "10.115.70.81"
        - "10.115.70.82"
        - "10.115.70.83"
        - "10.115.70.84"
        - "10.115.70.85"
        - "10.115.70.86"
        - "10.115.70.87"
        - "10.115.70.88"
        - "10.115.70.89"
        - "10.115.70.90"
        - "10.115.70.91"
        - "10.115.70.92"
        - "10.115.70.93"
        - "10.115.70.94"
        - "10.115.70.95"
        - "10.115.70.96"
        - "10.115.70.97"
        - "10.115.70.98"
        - "10.115.70.99"
        - "***********00"
        - "***********01"
        - "***********02"
        - "***********03"
        - "***********04"
        - "***********05"
        - "***********06"
        - "***********07"
        - "***********08"
        - "***********09"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "***********20"
        - "***********21"
        - "***********22"
        - "***********23"
        - "***********24"
        - "***********25"
        - "***********26"
        - "***********27"
        - "***********28"
        - "***********29"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "***********40"
        - "***********41"
        - "***********42"
        - "***********43"
        - "***********44"
        - "***********45"
        - "***********46"
        - "***********47"
        - "***********48"
        - "***********49"
        - "***********50"
        - "***********51"
        - "***********52"
        - "***********53"
        - "***********54"
        - "***********55"
        - "***********56"
        - "***********57"
        - "***********58"
        - "***********59"
        - "***********60"
        - "***********61"
        - "***********62"
        - "***********63"
        - "***********64"
        - "***********65"
        - "***********66"
        - "***********67"
        - "***********68"
        - "***********69"
        - "***********70"
        - "***********71"
        - "***********72"
        - "***********73"
        - "***********74"
        - "***********75"
        - "***********76"
        - "***********77"
        - "***********78"
        - "***********79"
        - "***********80"
        - "***********81"
        - "***********82"
        - "***********83"
        - "***********84"
        - "***********85"
        - "***********86"
        - "***********87"
        - "***********88"
        - "***********89"
        - "***********90"
        - "***********91"
        - "***********92"
        - "***********93"
        - "***********94"
        - "***********95"
        - "***********96"
        - "***********97"
        - "***********98"
        - "***********99"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
      eips:
        cidr: *************/27
        gateway: *************
        ips:
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        - "**************"
        sku: CHINA_MOBILE
      cidr_pools:
        ib:
        - cidr: ************/26
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 2500
          scope: TRAINING
          reserved: ************..************,*************..*************
        - cidr: *************/26
          gateway: *************
          zone_prp: cn-sz-02a
          vni: 2501
          scope: TRAINING
          reserved: *************..*************,************22..**************
        - cidr: ************28/26
          gateway: ************29
          zone_prp: cn-sz-02a
          vni: 2502
          scope: TRAINING
          reserved: ************29..************37,************86..************90
        - cidr: ************92/26
          gateway: ************93
          zone_prp: cn-sz-02a
          vni: 2503
          scope: TRAINING
          reserved: ************93..10.110.128.201,10.110.128.250..10.110.128.254
        - cidr: 10.110.129.0/26
          gateway: 10.110.129.1
          zone_prp: cn-sz-02a
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-sz-02a
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-sz-02a
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-sz-02a
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-sz-02a
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-sz-02a
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-sz-02a
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-sz-02a
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-sz-02a
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-sz-02a
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-sz-02a
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-sz-02a
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-sz-02a
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-sz-02a
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-sz-02a
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-sz-02a
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-sz-02a
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-sz-02a
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-sz-02a
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-sz-02a
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-sz-02a
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-sz-02a
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-sz-02a
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-sz-02a
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-sz-02a
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-sz-02a
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-sz-02a
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-sz-02a
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.120.136.0/26
          gateway: 10.120.136.1
          zone_prp: cn-sz-02a
          vni: 2500
          scope: DATA
          reserved: 10.120.136.1..10.120.136.9,10.120.136.58..10.120.136.62
        - cidr: 10.120.136.64/26
          gateway: 10.120.136.65
          zone_prp: cn-sz-02a
          vni: 2501
          scope: DATA
          reserved: 10.120.136.65..10.120.136.73,10.120.136.122..10.120.136.126
        - cidr: 10.120.136.128/26
          gateway: 10.120.136.129
          zone_prp: cn-sz-02a
          vni: 2502
          scope: DATA
          reserved: 10.120.136.129..10.120.136.137,10.120.136.186..10.120.136.190
        - cidr: 10.120.136.192/26
          gateway: 10.120.136.193
          zone_prp: cn-sz-02a
          vni: 2503
          scope: DATA
          reserved: 10.120.136.193..10.120.136.201,10.120.136.250..10.120.136.254
        - cidr: 10.120.137.0/26
          gateway: 10.120.137.1
          zone_prp: cn-sz-02a
          vni: 2504
          scope: DATA
          reserved: 10.120.137.1..10.120.137.9,10.120.137.58..10.120.137.62
        - cidr: 10.120.137.64/26
          gateway: 10.120.137.65
          zone_prp: cn-sz-02a
          vni: 2505
          scope: DATA
          reserved: 10.120.137.65..10.120.137.73,10.120.137.122..10.120.137.126
        - cidr: 10.120.137.128/26
          gateway: 10.120.137.129
          zone_prp: cn-sz-02a
          vni: 2506
          scope: DATA
          reserved: 10.120.137.129..10.120.137.137,10.120.137.186..10.120.137.190
        - cidr: 10.120.137.192/26
          gateway: 10.120.137.193
          zone_prp: cn-sz-02a
          vni: 2507
          scope: DATA
          reserved: 10.120.137.193..10.120.137.201,10.120.137.250..10.120.137.254
        - cidr: 10.120.138.0/26
          gateway: 10.120.138.1
          zone_prp: cn-sz-02a
          vni: 2508
          scope: DATA
          reserved: 10.120.138.1..10.120.138.9,10.120.138.58..10.120.138.62
        - cidr: 10.120.138.64/26
          gateway: 10.120.138.65
          zone_prp: cn-sz-02a
          vni: 2509
          scope: DATA
          reserved: 10.120.138.65..10.120.138.73,10.120.138.122..10.120.138.126
        - cidr: 10.120.138.128/26
          gateway: 10.120.138.129
          zone_prp: cn-sz-02a
          vni: 2510
          scope: DATA
          reserved: 10.120.138.129..10.120.138.137,10.120.138.186..10.120.138.190
        - cidr: 10.120.138.192/26
          gateway: 10.120.138.193
          zone_prp: cn-sz-02a
          vni: 2511
          scope: DATA
          reserved: 10.120.138.193..10.120.138.201,10.120.138.250..10.120.138.254
        - cidr: 10.120.139.0/26
          gateway: 10.120.139.1
          zone_prp: cn-sz-02a
          vni: 2512
          scope: DATA
          reserved: 10.120.139.1..10.120.139.9,10.120.139.58..10.120.139.62
        - cidr: 10.120.139.64/26
          gateway: 10.120.139.65
          zone_prp: cn-sz-02a
          vni: 2513
          scope: DATA
          reserved: 10.120.139.65..10.120.139.73,10.120.139.122..10.120.139.126
        - cidr: 10.120.139.128/26
          gateway: 10.120.139.129
          zone_prp: cn-sz-02a
          vni: 2514
          scope: DATA
          reserved: 10.120.139.129..10.120.139.137,10.120.139.186..10.120.139.190
        - cidr: 10.120.139.192/26
          gateway: 10.120.139.193
          zone_prp: cn-sz-02a
          vni: 2515
          scope: DATA
          reserved: 10.120.139.193..10.120.139.201,10.120.139.250..10.120.139.254
        - cidr: 10.120.140.0/26
          gateway: 10.120.140.1
          zone_prp: cn-sz-02a
          vni: 2516
          scope: DATA
          reserved: 10.120.140.1..10.120.140.9,10.120.140.58..10.120.140.62
        - cidr: 10.120.140.64/26
          gateway: 10.120.140.65
          zone_prp: cn-sz-02a
          vni: 2517
          scope: DATA
          reserved: 10.120.140.65..10.120.140.73,10.120.140.122..10.120.140.126
        - cidr: 10.120.140.128/26
          gateway: 10.120.140.129
          zone_prp: cn-sz-02a
          vni: 2518
          scope: DATA
          reserved: 10.120.140.129..10.120.140.137,10.120.140.186..10.120.140.190
        - cidr: 10.120.140.192/26
          gateway: 10.120.140.193
          zone_prp: cn-sz-02a
          vni: 2519
          scope: DATA
          reserved: 10.120.140.193..10.120.140.201,10.120.140.250..10.120.140.254
        - cidr: 10.120.141.0/26
          gateway: 10.120.141.1
          zone_prp: cn-sz-02a
          vni: 2520
          scope: DATA
          reserved: 10.120.141.1..10.120.141.9,10.120.141.58..10.120.141.62
        - cidr: 10.120.141.64/26
          gateway: 10.120.141.65
          zone_prp: cn-sz-02a
          vni: 2521
          scope: DATA
          reserved: 10.120.141.65..10.120.141.73,10.120.141.122..10.120.141.126
        - cidr: 10.120.141.128/26
          gateway: 10.120.141.129
          zone_prp: cn-sz-02a
          vni: 2522
          scope: DATA
          reserved: 10.120.141.129..10.120.141.137,10.120.141.186..10.120.141.190
        - cidr: 10.120.141.192/26
          gateway: 10.120.141.193
          zone_prp: cn-sz-02a
          vni: 2523
          scope: DATA
          reserved: 10.120.141.193..10.120.141.201,10.120.141.250..10.120.141.254
        - cidr: 10.120.142.0/26
          gateway: 10.120.142.1
          zone_prp: cn-sz-02a
          vni: 2524
          scope: DATA
          reserved: 10.120.142.1..10.120.142.9,10.120.142.58..10.120.142.62
        - cidr: 10.120.142.64/26
          gateway: 10.120.142.65
          zone_prp: cn-sz-02a
          vni: 2525
          scope: DATA
          reserved: 10.120.142.65..10.120.142.73,10.120.142.122..10.120.142.126
        - cidr: 10.120.142.128/26
          gateway: 10.120.142.129
          zone_prp: cn-sz-02a
          vni: 2526
          scope: DATA
          reserved: 10.120.142.129..10.120.142.137,10.120.142.186..10.120.142.190
        - cidr: 10.120.142.192/26
          gateway: 10.120.142.193
          zone_prp: cn-sz-02a
          vni: 2527
          scope: DATA
          reserved: 10.120.142.193..10.120.142.201,10.120.142.250..10.120.142.254
        - cidr: 10.120.143.0/26
          gateway: 10.120.143.1
          zone_prp: cn-sz-02a
          vni: 2528
          scope: DATA
          reserved: 10.120.143.1..10.120.143.9,10.120.143.58..10.120.143.62
        - cidr: 10.120.143.64/26
          gateway: 10.120.143.65
          zone_prp: cn-sz-02a
          vni: 2529
          scope: DATA
          reserved: 10.120.143.65..10.120.143.73,10.120.143.122..10.120.143.126
        - cidr: 10.120.143.128/26
          gateway: 10.120.143.129
          zone_prp: cn-sz-02a
          vni: 2530
          scope: DATA
          reserved: 10.120.143.129..10.120.143.137,10.120.143.186..10.120.143.190
        - cidr: 10.120.143.192/26
          gateway: 10.120.143.193
          zone_prp: cn-sz-02a
          vni: 2531
          scope: DATA
          reserved: 10.120.143.193..10.120.143.201,10.120.143.250..10.120.143.254
        - cidr: 10.120.72.0/26
          gateway: 10.120.72.1
          zone_prp: cn-sz-02a
          vni: 2500
          scope: SERVICE
          reserved: 10.120.72.1..10.120.72.9,10.120.72.58..10.120.72.62
        - cidr: 10.120.72.64/26
          gateway: 10.120.72.65
          zone_prp: cn-sz-02a
          vni: 2501
          scope: SERVICE
          reserved: 10.120.72.65..10.120.72.73,10.120.72.122..10.120.72.126
        - cidr: 10.120.72.128/26
          gateway: 10.120.72.129
          zone_prp: cn-sz-02a
          vni: 2502
          scope: SERVICE
          reserved: 10.120.72.129..10.120.72.137,10.120.72.186..10.120.72.190
        - cidr: 10.120.72.192/26
          gateway: 10.120.72.193
          zone_prp: cn-sz-02a
          vni: 2503
          scope: SERVICE
          reserved: 10.120.72.193..10.120.72.201,10.120.72.250..10.120.72.254
        - cidr: 10.120.73.0/26
          gateway: 10.120.73.1
          zone_prp: cn-sz-02a
          vni: 2504
          scope: SERVICE
          reserved: 10.120.73.1..10.120.73.9,10.120.73.58..10.120.73.62
        - cidr: 10.120.73.64/26
          gateway: 10.120.73.65
          zone_prp: cn-sz-02a
          vni: 2505
          scope: SERVICE
          reserved: 10.120.73.65..10.120.73.73,10.120.73.122..10.120.73.126
        - cidr: 10.120.73.128/26
          gateway: 10.120.73.129
          zone_prp: cn-sz-02a
          vni: 2506
          scope: SERVICE
          reserved: 10.120.73.129..10.120.73.137,10.120.73.186..10.120.73.190
        - cidr: 10.120.73.192/26
          gateway: 10.120.73.193
          zone_prp: cn-sz-02a
          vni: 2507
          scope: SERVICE
          reserved: 10.120.73.193..10.120.73.201,10.120.73.250..10.120.73.254
        - cidr: 10.120.74.0/26
          gateway: 10.120.74.1
          zone_prp: cn-sz-02a
          vni: 2508
          scope: SERVICE
          reserved: 10.120.74.1..10.120.74.9,10.120.74.58..10.120.74.62
        - cidr: 10.120.74.64/26
          gateway: 10.120.74.65
          zone_prp: cn-sz-02a
          vni: 2509
          scope: SERVICE
          reserved: 10.120.74.65..10.120.74.73,10.120.74.122..10.120.74.126
        - cidr: 10.120.74.128/26
          gateway: 10.120.74.129
          zone_prp: cn-sz-02a
          vni: 2510
          scope: SERVICE
          reserved: 10.120.74.129..10.120.74.137,10.120.74.186..10.120.74.190
        - cidr: 10.120.74.192/26
          gateway: 10.120.74.193
          zone_prp: cn-sz-02a
          vni: 2511
          scope: SERVICE
          reserved: 10.120.74.193..10.120.74.201,10.120.74.250..10.120.74.254
        - cidr: 10.120.75.0/26
          gateway: 10.120.75.1
          zone_prp: cn-sz-02a
          vni: 2512
          scope: SERVICE
          reserved: 10.120.75.1..10.120.75.9,10.120.75.58..10.120.75.62
        - cidr: 10.120.75.64/26
          gateway: 10.120.75.65
          zone_prp: cn-sz-02a
          vni: 2513
          scope: SERVICE
          reserved: 10.120.75.65..10.120.75.73,10.120.75.122..10.120.75.126
        - cidr: 10.120.75.128/26
          gateway: 10.120.75.129
          zone_prp: cn-sz-02a
          vni: 2514
          scope: SERVICE
          reserved: 10.120.75.129..10.120.75.137,10.120.75.186..10.120.75.190
        - cidr: 10.120.75.192/26
          gateway: 10.120.75.193
          zone_prp: cn-sz-02a
          vni: 2515
          scope: SERVICE
          reserved: 10.120.75.193..10.120.75.201,10.120.75.250..10.120.75.254
        - cidr: 10.120.76.0/26
          gateway: 10.120.76.1
          zone_prp: cn-sz-02a
          vni: 2516
          scope: SERVICE
          reserved: 10.120.76.1..10.120.76.9,10.120.76.58..10.120.76.62
        - cidr: 10.120.76.64/26
          gateway: 10.120.76.65
          zone_prp: cn-sz-02a
          vni: 2517
          scope: SERVICE
          reserved: 10.120.76.65..10.120.76.73,10.120.76.122..10.120.76.126
        - cidr: 10.120.76.128/26
          gateway: 10.120.76.129
          zone_prp: cn-sz-02a
          vni: 2518
          scope: SERVICE
          reserved: 10.120.76.129..10.120.76.137,10.120.76.186..10.120.76.190
        - cidr: 10.120.76.192/26
          gateway: 10.120.76.193
          zone_prp: cn-sz-02a
          vni: 2519
          scope: SERVICE
          reserved: 10.120.76.193..10.120.76.201,10.120.76.250..10.120.76.254
        - cidr: 10.120.77.0/26
          gateway: 10.120.77.1
          zone_prp: cn-sz-02a
          vni: 2520
          scope: SERVICE
          reserved: 10.120.77.1..10.120.77.9,10.120.77.58..10.120.77.62
        - cidr: 10.120.77.64/26
          gateway: 10.120.77.65
          zone_prp: cn-sz-02a
          vni: 2521
          scope: SERVICE
          reserved: 10.120.77.65..10.120.77.73,10.120.77.122..10.120.77.126
        - cidr: 10.120.77.128/26
          gateway: 10.120.77.129
          zone_prp: cn-sz-02a
          vni: 2522
          scope: SERVICE
          reserved: 10.120.77.129..10.120.77.137,10.120.77.186..10.120.77.190
        - cidr: 10.120.77.192/26
          gateway: 10.120.77.193
          zone_prp: cn-sz-02a
          vni: 2523
          scope: SERVICE
          reserved: 10.120.77.193..10.120.77.201,10.120.77.250..10.120.77.254
        - cidr: 10.120.78.0/26
          gateway: 10.120.78.1
          zone_prp: cn-sz-02a
          vni: 2524
          scope: SERVICE
          reserved: 10.120.78.1..10.120.78.9,10.120.78.58..10.120.78.62
        - cidr: 10.120.78.64/26
          gateway: 10.120.78.65
          zone_prp: cn-sz-02a
          vni: 2525
          scope: SERVICE
          reserved: 10.120.78.65..10.120.78.73,10.120.78.122..10.120.78.126
        - cidr: 10.120.78.128/26
          gateway: 10.120.78.129
          zone_prp: cn-sz-02a
          vni: 2526
          scope: SERVICE
          reserved: 10.120.78.129..10.120.78.137,10.120.78.186..10.120.78.190
        - cidr: 10.120.78.192/26
          gateway: 10.120.78.193
          zone_prp: cn-sz-02a
          vni: 2527
          scope: SERVICE
          reserved: 10.120.78.193..10.120.78.201,10.120.78.250..10.120.78.254
        - cidr: 10.120.79.0/26
          gateway: 10.120.79.1
          zone_prp: cn-sz-02a
          vni: 2528
          scope: SERVICE
          reserved: 10.120.79.1..10.120.79.9,10.120.79.58..10.120.79.62
        - cidr: 10.120.79.64/26
          gateway: 10.120.79.65
          zone_prp: cn-sz-02a
          vni: 2529
          scope: SERVICE
          reserved: 10.120.79.65..10.120.79.73,10.120.79.122..10.120.79.126
        - cidr: 10.120.79.128/26
          gateway: 10.120.79.129
          zone_prp: cn-sz-02a
          vni: 2530
          scope: SERVICE
          reserved: 10.120.79.129..10.120.79.137,10.120.79.186..10.120.79.190
        - cidr: 10.120.79.192/26
          gateway: 10.120.79.193
          zone_prp: cn-sz-02a
          vni: 2531
          scope: SERVICE
          reserved: 10.120.79.193..10.120.79.201,10.120.79.250..10.120.79.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-02a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1500
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1501
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1502
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1503
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1504
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1505
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1506
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1507
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1508
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1509
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1510
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1511
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1512
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1513
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1514
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1515
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1516
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1517
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1518
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1519
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1520
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1521
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1522
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1523
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1524
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1525
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1526
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1527
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1528
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1529
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1530
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sz-02a
          vni: 1531
          scope: TRAINING
          reserved: ************..************,10.110.128.250..10.110.128.254

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-sz-02a
      vpc_default_region: cn-sz-02z
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
