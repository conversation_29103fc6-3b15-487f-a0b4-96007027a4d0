global:
  region: cn-sz-01
  zone: cn-sz-01z
  namespace: plat-boson-service
  envName: prod-cn-sz-01z
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  omsDashboardImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.15.0-53ab42c-20240730160253
  imagePullSecret: sensecore-boson
  domainName: boson-oms.cn-sz-01.qhsgaiccapi.com
  sslSecretName: tls-cnsz01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: cn-sz-01
    k8sInfo:
      enabled: False
      region: cn-sz-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: cn-sz-01a
        region: cn-sz-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/cn-sz-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5092
      southDB:
        host: *************
        port: 37321
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: *************
        port: 37321
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: cn-sz-01z
          host: network-internal.cn-sz-01.qhsgaiccapi.com
        - zone: cn-sz-01a
          host: network-internal.cn-sz-01.qhsgaiccapi.com
  omsDashboard:
    clickHouse:
      host: clickhouse-higgs.app-higgs-middleware.svc.cluster.local
      port: 9000
      user: boson
      database: boson
      password: RP03PZgeDH
