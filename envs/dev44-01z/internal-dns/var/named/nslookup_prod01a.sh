#!/usr/bin/env bash
set -beou pipefail
cd "$(dirname "${0}")"
# shellcheck source=/dev/null
source comm_libs.sh

if [[ -z "${1-}" || ! "${1}" =~ ^(master|slave|all)$ ]]; then
    echo "Usage: $0 <master|slave|all>"
    exit 1
fi
if [[ "${1}" == "master" ]]; then
    # master dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ ************"
elif [[ "${1}" == "slave" ]]; then
    # slave dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ ************"
elif [[ "${1}" == "all" ]]; then
    # master and slave dns vip and server ip
    dns_server_type="${1}"
    dns_servers="************ ************ ************ ************"
fi

echo "start domain validation for ${dns_server_type} dns servers: ${dns_servers}"

echo -e "\e[1:m validate console sensecore domain \e[0m"
domain="console.sensecore.cn"
check_domain_result "${domain}" "${dns_servers}" "*************;************;" "skip"

echo -e "\e[1:m validate sensecore domain \e[0m"
domain="sensecore.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate public domain \e[0m"
domain="baidu.com"
expected_ips="*************;************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo "========================================================================"
echo "================== validate InternalDNS configured domains ================="
echo "========================================================================"

check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"

echo -e "\e[1:m validate domain aoss-internal.cn-sh-01b.sensecoreapi-oss.cn resolve for InternalDNS. \e[0m"
domain="aoss-internal.cn-sh-01b.sensecoreapi-oss.cn"
expected_ips="**************;**************;**************;**************;**************;**************;*************;*************;*************;*************;*************;*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" ""

check_ip_availability "*************"
check_ip_availability "*************"

echo -e "\e[1:m validate domain aoss-internal.cn-sh-01c.sensecoreapi-oss.cn resolve for InternalDNS. \e[0m"
domain="aoss-internal.cn-sh-01c.sensecoreapi-oss.cn"
expected_ips="*************;*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" ""

check_ip_availability "*************"
check_ip_availability "*************"

echo -e "\e[1:m validate domain aoss-internal.cn-sh-01d.sensecoreapi-oss.cn resolve for InternalDNS. \e[0m"
domain="aoss-internal.cn-sh-01d.sensecoreapi-oss.cn"
expected_ips="*************;*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" ""

check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "**************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"
check_ip_availability "*************"

echo -e "\e[1:m validate domain archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn resolve for InternalDNS. \e[0m"
domain="archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn"
expected_ips="**************;**************;**************;**************;**************;**************;*************;*************;*************;*************;*************;*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" ""
