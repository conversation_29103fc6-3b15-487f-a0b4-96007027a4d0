# cat /etc/named.conf
//
// named.conf
//
// Provided by Red Hat bind package to configure the ISC BIND named(8) DNS
// server as a caching only nameserver (as a localhost DNS resolver only).
//
// See /usr/share/doc/bind*/sample/ for example named configuration files.
//

statistics-channels { inet 127.0.0.1 port 8053 ; };


key "default" {
	algorithm hmac-sha256;
	secret "RfA6htxY8Vr2+33nFIM92Af5Qi6vjvAMKJgGsEpOUFg=";
};

key "prod01a" {
	algorithm hmac-sha256;
	secret "PI6lQLIgHUho4ZCfqNd5FK5rqtLgXeHBpURfGtEzDXM=";
};

key "prod01b" {
	algorithm hmac-sha256;
	secret "SPbj56CbkJQD4Nsm62gGScLpGCiHLsvNSeNp9bIh/Ak=";
};

key "prod01c" {
	algorithm hmac-sha256;
	secret "XxJOXO0jridL4P/ubrR6EGUUvpM/FAhfLTTKXg1C3dw=";
};

key "prod01d" {
	algorithm hmac-sha256;
	secret "HsdMnHqHnF+snUyacOtV2+crcdjAgdiiP9nhzp4Kj7k=";
};

key "prod01e" {
	algorithm hmac-sha256;
	secret "+5njgw5aQwPgH7maLj+xkYpqYcytIkrWpL716T6pl4Q=";
};


options {
	listen-on port 53 { any; };
	listen-on-v6 port 53 { ::1; };
	directory	"/var/named";
	dump-file	"/var/named/data/cache_dump.db";
	statistics-file "/var/named/data/named_stats.txt";
	memstatistics-file "/var/named/data/named_mem_stats.txt";
	secroots-file	"/var/named/data/named.secroots";
	recursing-file	"/var/named/data/named.recursing";
	allow-query	{ any; };
	allow-transfer  { any; };

	forwarders {
		********;
		********;
	};

	recursion yes;

	dnssec-validation no;

	managed-keys-directory "/var/named/dynamic";
	geoip-directory "/usr/share/GeoIP";

	pid-file "/run/named/named.pid";
	session-keyfile "/run/named/session.key";

	/* https://fedoraproject.org/wiki/Changes/CryptoPolicy */
	include "/etc/crypto-policies/back-ends/bind.config";
};

logging {
	channel default_debug {
		file "data/named.run";
		severity dynamic;
	};
};


acl default {
	!key prod01a;
	!**********/22;
	!**********/23;
	!***********/24;
	!***********/22;
	!************/32;
	!key prod01b;
	!**********/24;
	!***********/24;
	!************/32;
	!key prod01c;
	!***********/19;
	!************/32;
	!key prod01d;
	!***********/19;
	!************/32;
	!key prod01e;
	!***********/20;
	!************/32;
	key default;
	any;
};

acl prod01a {
	!key default;
	!key prod01b;
	!**********/24;
	!***********/24;
	!************/32;
	!key prod01c;
	!***********/19;
	!************/32;
	!key prod01d;
	!***********/19;
	!************/32;
	!key prod01e;
	!***********/20;
	!************/32;
	key prod01a;
	**********/22;
	**********/23;
	***********/24;
	***********/22;
	************/32;
};

acl prod01b {
	!key default;
	!key prod01a;
	!**********/22;
	!**********/23;
	!***********/24;
	!***********/22;
	!************/32;
	!key prod01c;
	!***********/19;
	!************/32;
	!key prod01d;
	!***********/19;
	!************/32;
	!key prod01e;
	!***********/20;
	!************/32;
	key prod01b;
	**********/24;
	***********/24;
	************/32;
};

acl prod01c {
	!key default;
	!key prod01a;
	!**********/22;
	!**********/23;
	!***********/24;
	!***********/22;
	!************/32;
	!key prod01b;
	!**********/24;
	!***********/24;
	!************/32;
	!key prod01d;
	!***********/19;
	!************/32;
	!key prod01e;
	!***********/20;
	!************/32;
	key prod01c;
	***********/19;
	************/32;
};

acl prod01d {
	!key default;
	!key prod01a;
	!**********/22;
	!**********/23;
	!***********/24;
	!***********/22;
	!************/32;
	!key prod01b;
	!**********/24;
	!***********/24;
	!************/32;
	!key prod01c;
	!***********/19;
	!************/32;
	!key prod01e;
	!***********/20;
	!************/32;
	key prod01d;
	***********/19;
	************/32;
};

acl prod01e {
	!key default;
	!key prod01a;
	!**********/22;
	!**********/23;
	!***********/24;
	!***********/22;
	!************/32;
	!key prod01b;
	!**********/24;
	!***********/24;
	!************/32;
	!key prod01c;
	!***********/19;
	!************/32;
	!key prod01d;
	!***********/19;
	!************/32;
	key prod01e;
	***********/20;
	************/32;
};



view default {
	match-clients { default; };
	allow-query   { any; };
	allow-update { key default; };
	allow-transfer  { key default; };
};

view prod01a {
	match-clients { prod01a; };
	allow-query   { any; };
	allow-update { key prod01a; };
	allow-transfer  { key prod01a; };
	zone "aoss-internal.cn-sh-01b.sensecoreapi-oss.cn" IN {
		file "zones/prod01a-aoss-internal.cn-sh-01b.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01a; };
	};
	zone "aoss-internal.cn-sh-01c.sensecoreapi-oss.cn" IN {
		file "zones/prod01a-aoss-internal.cn-sh-01c.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01a; };
	};
	zone "aoss-internal.cn-sh-01d.sensecoreapi-oss.cn" IN {
		file "zones/prod01a-aoss-internal.cn-sh-01d.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01a; };
	};
	zone "archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01a-archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01a; };
	};
};

view prod01b {
	match-clients { prod01b; };
	allow-query   { any; };
	allow-update { key prod01b; };
	allow-transfer  { key prod01b; };
	zone "aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01b-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01b; };
	};
	zone "aoss-internal.cn-sh-01c.sensecoreapi-oss.cn" IN {
		file "zones/prod01b-aoss-internal.cn-sh-01c.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01b; };
	};
	zone "aoss-internal.cn-sh-01d.sensecoreapi-oss.cn" IN {
		file "zones/prod01b-aoss-internal.cn-sh-01d.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01b; };
	};
	zone "archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01b-archive-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01b; };
	};
};

view prod01c {
	match-clients { prod01c; };
	allow-query   { any; };
	allow-update { key prod01c; };
	allow-transfer  { key prod01c; };
	zone "aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01c-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01c; };
	};
	zone "aoss-internal.cn-sh-01b.sensecoreapi-oss.cn" IN {
		file "zones/prod01c-aoss-internal.cn-sh-01b.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01c; };
	};
	zone "aoss-internal.cn-sh-01d.sensecoreapi-oss.cn" IN {
		file "zones/prod01c-aoss-internal.cn-sh-01d.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01c; };
	};
};

view prod01d {
	match-clients { prod01d; };
	allow-query   { any; };
	allow-update { key prod01d; };
	allow-transfer  { key prod01d; };
	zone "aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01d-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01d; };
	};
	zone "aoss-internal.cn-sh-01b.sensecoreapi-oss.cn" IN {
		file "zones/prod01d-aoss-internal.cn-sh-01b.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01d; };
	};
	zone "aoss-internal.cn-sh-01c.sensecoreapi-oss.cn" IN {
		file "zones/prod01d-aoss-internal.cn-sh-01c.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01d; };
	};
};

view prod01e {
	match-clients { prod01e; };
	allow-query   { any; };
	allow-update { key prod01e; };
	allow-transfer  { key prod01e; };
	zone "aoss-internal.cn-sh-01.sensecoreapi-oss.cn" IN {
		file "zones/prod01e-aoss-internal.cn-sh-01.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01e; };
	};
	zone "aoss-internal.cn-sh-01b.sensecoreapi-oss.cn" IN {
		file "zones/prod01e-aoss-internal.cn-sh-01b.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01e; };
	};
	zone "aoss-internal.cn-sh-01c.sensecoreapi-oss.cn" IN {
		file "zones/prod01e-aoss-internal.cn-sh-01c.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01e; };
	};
	zone "aoss-internal.cn-sh-01d.sensecoreapi-oss.cn" IN {
		file "zones/prod01e-aoss-internal.cn-sh-01d.sensecoreapi-oss.cn.db";
		type master;
		notify yes;
		also-notify { 10.105.9.219 key prod01e; };
	};
};
