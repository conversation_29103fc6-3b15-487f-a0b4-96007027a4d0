---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-oms
  namespace: plat-boson-service
---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-oms-dashboard
  namespace: plat-boson-service
---
# Source: boson-oms/templates/boson-oms-cm.yaml
apiVersion: v1
data:
  boson-oms-dashboard.yaml: |-
    region: cn-sh-01
    http_port: 55983

    south_db_pg:
      host: *************
      port: 34150
      user: boson
      password: b5bioOt5sPuJm2
      db: boson_service_dev44

    click_house:
      host: ************
      port: 31000
      user: boson
      password: THZyGRj51T
      database: boson
  boson-oms.yaml: |-
    region: cn-sh-01
    oms_server:
      grpc_port: 55990
      http_port: 55980
      metrics_port: 55982
      #log_file: "./log/boson_oms.log"
    # ndc server url and access key
    ndc:
      server_url: http://************:5050
      access_key: WTNsM1VuWnpSa3R4UW1oNlpXNVBDZz09Cg==
    # ip query configuration
    k8sInfo:
      enabled: false
      env: dev44
      region: cn-sh-01
      dryrun: false
      kubeConfigs:
      kubectlTemplateStrs:
        vpc_nat_gw_ip: |-
          ip={{ .IP }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          for hagw in $(kubectl get hanatgws.network.sensecore.cn | grep hagw | awk  '{print $1}')
          do
            {
              kubectl get hanatgws.network.sensecore.cn $hagw -o json | jq .spec.extendInfos | grep -q -w $ip
            } && {
              # 如果在hanatgw中拿到了这个信息，则返回该hanatgw所在的vpc的信息
              kubectl get hanatgws.network.sensecore.cn $hagw -o json | jq '.metadata.ownerReferences | .[0].name' | tr -d '"\n'
              find=1
              break
            };
          done
          if [ $find -eq 0 ]; then
            echo "not found hagw with ip $ip"
            exit 0
          fi
        vpc_pod_ip: |-
          ip={{ .IP }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          tenant_regex="^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$"
          tenantId={{ .TENANT_ID }}
          { echo "$tenantId" | grep -Eq "$tenant_regex"; } || {
            echo "tenantId $tenantId is not valid.";
            exit 1;
          }
          nsList=$(kubectl get vpcs.kubeovn.io | grep $tenantId | grep ns | awk '{print $4}' | sort -u | tr -d '["]')
          find=0
          for ns in $nsList
          do
            if [ "$ns" == "" ]; then
              ns="default"
              vpc=ovn-cluster
            else
              vpc=$(kubectl get vpcs.kubeovn.io | grep $ns | awk '{print $1}')
            fi
            pods=$(kubectl get pod -oname -n $ns | awk -F '/' '{print $2}')
            for pod in $pods
            do
              kubectl get pod $pod -owide -n $ns | grep -q -w $ip
              if [ "$?" == "0" ]; then
                # 如果在ns中的pod中查到了ip信息，则返回该pod的简要信息
                #kubectl get pod $pod -n $ns -ojson
                echo "$vpc;$pod" | tr -d '"'
                find=1
                break
              fi
            done
          done
          if [ $find -eq 0 ]; then
            echo "not found pod with $ip"
            exit 0
          fi
        vpc_bms_ip: |-
          ip={{ .IP }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          subnets=$(kubectl get ipas.network.sensecore.cn | grep -w "$ip" | grep -o "sn-.*-.*")
          for subnet in $subnets
          do
            vpc=$(kubectl get subnets.network.sensecore.cn $subnet -ojson |  jq '.spec.vpc')
            if [ "$?" == "0" ]; then
              echo $vpc | tr -d '"\n'
              find=1
              break
            fi
          done
          if [ $find -eq 0 ]; then
            echo "not found bms with $ip"
            exit 0
          fi
        eip: |-
          ip={{ .IP }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          eip=$(kubectl get eips.network.sensecore.cn | grep -w $ip | awk '{print $1}')
          if [ "$eip" != "" ]; then
            vpc=$(kubectl get eips.network.sensecore.cn $eip -ojson | jq '.metadata.ownerReferences | .[0].name');
            if [ "$vpc" != "" ]; then
              echo $vpc | tr -d '"\n'
              find=1
            fi
          fi
          if [ $find -eq 0 ]; then
            echo "not found eip with $ip"
            exit 0
          fi
        service_ip: |-
          ip={{ .IP }}
          ip_regex="^([0-9]{1,3}\.){3}[0-9]{1,3}$"
          { echo "$ip" | grep -Eq "$ip_regex"; } || {
            echo "IP address $ip is not valid.";
            exit 1;
          }
          find=0
          service=$(kubectl get services -A | grep -w $ip);
          if [ "$service" != "" ]; then
          {
            ns=$(echo $service | awk '{print $1}')
            if [ "$ns" != "" ]; then
              vpc=$(kubectl get vpcs.kubeovn.io | grep $ns | awk '{print $1}')
              if [ "$vpc" != "" ]; then
                echo $vpc | tr -d '"\n'
                find=1
              else
                echo "no vpc found, but find service $ip in ns $ns"
                find=1
              fi
            fi
          }
          fi
          if [ $find -eq 0 ]; then
            echo "not found service with $ip"
            exit 0
          fi
    # zone name to env map
    zone_to_env:
      - zone: dev44
        env: dev44
      - zone: cn-tech-01a
        env: test
      - zone: cn-stage-01a
        env: prod-cn-stage-01
      - zone: st-sh-01a
        env: prod-st-sh-01
      - zone: cn-sh-01a
        env: prod
      - zone: cn-sh-01b
        env: prod-cn-sh-01b
      - zone: cn-sh-01c
        env: prod-cn-sh-01c
      - zone: cn-sh-01d
        env: prod-cn-sh-01d
      - zone: cn-gz-01a
        env: prod-gz
      - zone: cn-cq-01a
        env: prod-cn-cq-01a
      - zone: cn-sz-01a
        env: prod-cn-sz-01a
      - zone: ms-sc-01a
        env: prod-ms-sc-01a
      - zone: cn-fj-01a
        env: prod-cn-fj-01a
    south_db_pg:
      host: *************
      port: "34150"
      user: boson
      password: b5bioOt5sPuJm2
      db: boson_service_dev44
    oms_db_pg:
      host: *************
      port: "34150"
      user: boson
      password: b5bioOt5sPuJm2
      db: boson_oms
    dcos_server:
      domain: "dcos-api.sensetime.com"
      default_key: "4f68b359-3935-4724-81de-d8c495e133d7"
      apis:
        - name: "list_network_device"
          ak: "ada5b7ad-3b9b-4e3e-b919-dc9ec7064db6"
        - name: "get_server_by_ip"
          ak: "securityServerWyeq1One"
        - name: "list_sensecore_servers"
          ak: "4f68b359-3935-4724-81de-d8c495e133d7"
        - name: "list_physical_servers"
          ak: "db6b890e-a8e7-4def-bae0-e177f538a3a0"
        - name: "get_tor"
          ak: "torQueryList"
        - name: "switch_to_switch"
          ak: "lineQueryList"
    dcos_tracker:
      # dcos tacker interval in hours
      interval: 24
    provider:
      provider_addrs:
        - zone: cn-sh-01z
          addr: network-internal.cn-sh-01.sensecoreapi.dev
        - zone: cn-sh-01a
          addr: network-internal.cn-sh-01.sensecoreapi.dev
        - zone: cn-sh-01b
          addr: network-internal.cn-sh-01b.sensecoreapi.dev
        - zone: cn-sh-01p
          addr: network-internal.cn-sh-01p.sensecoreapi.dev
        - zone: cn-sh-01e
          addr: network-internal.cn-sh-01e.sensecoreapi.dev
kind: ConfigMap
metadata:
  name: boson-oms-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-oms
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-oms-dashboard
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-oms
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-oms
subjects:
- kind: ServiceAccount
  name: boson-oms
  namespace: plat-boson-service

# OMS Dashboard service account
---
# Source: boson-oms/templates/boson-oms-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-oms-dashboard
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-oms-dashboard
subjects:
- kind: ServiceAccount
  name: boson-oms-dashboard
  namespace: plat-boson-service
---
# Source: boson-oms/templates/boson-oms-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-oms-service
  namespace: plat-boson-service
  labels:
    app-name: boson-oms-service
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-oms
  ports:
  - name: http
    port: 55980
    protocol: TCP
  - name: grpc
    port: 55990
    protocol: TCP
  - name: metrics
    port: 55982
    protocol: TCP
---
# Source: boson-oms/templates/boson-oms-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-oms
  namespace: plat-boson-service
  labels:
    name: boson-oms
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-oms
  replicas: 1
  template:
    metadata:
      labels:
        app-name: boson-oms
    spec:
      serviceAccount: boson-oms
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-oms
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-oms
        image: "registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-oms
        - -c
        - /boson/conf/boson-oms.yaml
        ports:
        - name: http
          containerPort: 55980
          protocol: TCP
        - name: grpc
          containerPort: 55990
          protocol: TCP
        - name: metrics
          containerPort: 55982
          protocol: TCP
        resources:
          limits:
            cpu: 300m
            memory: 300Mi
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: BOSON_OMS_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_OMS_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_OMS_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_OMS_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: 55980
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: 55980
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/boson-oms.yaml
          subPath: path/to/boson-oms.yaml
      volumes:
      - name: config
        configMap:
          name: boson-oms-config
          items:
          - key: boson-oms.yaml
            path: path/to/boson-oms.yaml
---
# Source: boson-oms/templates/boson-oms-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-oms-dashboard
  namespace: plat-boson-service
  labels:
    name: boson-oms-dashboard
    helm.sh/chart: boson-oms-dashboard-1.16.0
    app.kubernetes.io/name: boson-oms-dashboard
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms-dashboard
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-oms-dashboard
  replicas: 1
  template:
    metadata:
      labels:
        app-name: boson-oms-dashboard
    spec:
      serviceAccount: boson-oms-dashboard
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-oms-dashboard
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-oms-dashboard
        image: "registry.sensetime.com/sensecore-boson/boson-oms:v1.15.0-53ab42c-20240730160253"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/oms-dashboard
        - -c
        - /boson/conf/boson-oms-dashboard.yaml
        resources:
          limits:
            cpu: 300m
            memory: 300Mi
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: BOSON_OMS_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_OMS_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_OMS_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_OMS_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/healthz"
            port: 55983
          periodSeconds: 10
          initialDelaySeconds: 30
        readinessProbe:
          httpGet:
            path: "/healthz"
            port: 55983
          periodSeconds: 10
          initialDelaySeconds: 10
        volumeMounts:
        - name: config
          mountPath: /boson/conf/boson-oms-dashboard.yaml
          subPath: path/to/boson-oms-dashboard.yaml
      volumes:
      - name: config
        configMap:
          name: boson-oms-config
          items:
          - key: boson-oms-dashboard.yaml
            path: path/to/boson-oms-dashboard.yaml
---
# Source: boson-oms/templates/boson-oms-grpc-ing.yaml
# HTTP ingress host and path couldn't be same to GRPC ingress, and GRPC ingress path must be /, so this path is /network.
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-oms-service-ingress-grpc
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/ssl-redirect: "True"
  labels:
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: boson-oms.cn-sh-01.sensecoreapi.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: boson-oms-service
            port:
              number: 55990
  tls:
  - hosts:
    - boson-oms.cn-sh-01.sensecoreapi.dev
    secretName: tls-cnsh01-api
---
# Source: boson-oms/templates/boson-oms-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-oms-service-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "PUT, GET, POST, OPTIONS, DELETE"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
  labels:
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: boson-oms.cn-sh-01.sensecoreapi.dev
    http:
      paths:
      - path: /v1
        pathType: Prefix
        backend:
          service:
            name: boson-oms-service
            port:
              number: 55980
  tls:
  - hosts:
    - boson-oms.cn-sh-01.sensecoreapi.dev
    secretName: tls-cnsh01-api
---
# Source: boson-oms/templates/boson-oms-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-oms-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-oms-1.16.0
    app.kubernetes.io/name: boson-oms
    app.kubernetes.io/instance: boson-oms
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-oms
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-oms-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
