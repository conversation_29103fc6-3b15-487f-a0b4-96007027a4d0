global:
  bosonBrokerImage: registry.sensetime.com/sensecore-boson/boson-broker:v1.20.0-12-g8c4aee0-20250422205712
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-service.cn-sh-01.sensecoreapi.dev
  sslSecretName: tls-cnsh01-api
  nodeSelectorTerms:
    - key: diamond.sensetime.com/role-business-app
      values:
        - sensecore
  tolerations:
    - effect: NoSchedule
      key: diamond.sensetime.com/role-business-app
      operator: Equal
      value: sensecore
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-resource-prp
      operator: Equal
      value: cn-sh-01a-control
    - effect: NoSchedule
      key: diamond.sensetime.com/belong-ns-layer
      operator: Equal
      value: iaas
    - effect: NoExecute
      key: diamond.sensetime.com/role-k8s-master
      operator: Equal
      value: enabled
  envName: dev-dev44-01z
  pgsql:
    host: *************
    port: 34150
    user: boson
    db: boson_service_dev44
    password: b5bioOt5sPuJm2
  rocketmq:
    default:
      nameServers:
       - *************:9876
       - ***********:9876
      dcplConsumerGroupName: sensetime-core-network-v1-kk-consumer
      brokerRMProducerGroupName: sensetime-core-network-v1-kk-producer-rm
      brokerBossProducerGroupName: sensetime-core-network-v1-kk-producer-boss
      brokerNoticeProducerGroupName: sensetime-core-network-v1-kk-producer-notice
      retryInterval: 5
      dcplTopic:  sensetime-core-network-dc-v1-cn-sh-01z
      rmAckTopic: sensetime-core-rm-resource-state-sync
      bossAckTopic: sensetime-core-resource-operation-result
      instanceName: boson-broker-instance
      accessKey: rocketmq-ak-boson-provider
      secretKey: e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce
    cloudAudit:
      nameServers:
       - ***********:9876
       - *************:9876
      instanceName: boson-broker-cloudAudit-instance
      topics:
        vpc:
          topic: sensetime-core-trail-vpc-operation
          producerGroupName: sensetime-core-trail-vpc-producer
          accessKey: rocketmq-ak-vpc-servcie
          secretKey: xxxxxxxxxxxx
  dns:
    name_server:
      cn-sh-01a: http://network-internal.cn-sh-01.sensecoreapi.dev
      cn-sh-01b: http://network-internal.cn-sh-01b.sensecoreapi.dev
      cn-sh-01e: http://network-internal.cn-sh-01e.sensecoreapi.dev
  business:
    iam_auth_addr: https://iam-internal.sensecoreapi.dev
  ovn:
    ovn_ic: tcp:10.142.49.104:6645,tcp:10.142.50.198:6645,tcp:10.142.52.252:6645
    ts_subnet_cidr: "169.254.0.0/17"
  bosonBroker:
    generate_deployment_annotations_timestamp: true
    defaults:
      vpc_default_region: cn-sh-01z
      pg_lock_timeout: 30
      cloud_audit_enable: true
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        mq_request_rt: 1000
        db_request_rt: 1000
        k8s_request_rt: 1000
