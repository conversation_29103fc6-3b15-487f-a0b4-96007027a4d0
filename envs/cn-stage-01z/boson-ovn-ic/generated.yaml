---
# Source: boson-ovn-ic/templates/ovn-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-ovn-ic
  namespace: kube-system
---
# Source: boson-ovn-ic/templates/ovn-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-ovn-ic
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - "*"
  - nonResourceURLs:
      - "*"
    verbs:
      - "*"
---
# Source: boson-ovn-ic/templates/ovn-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-ovn-ic
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-ovn-ic
subjects:
  - kind: ServiceAccount
    name: boson-ovn-ic
    namespace: kube-system
---
# Source: boson-ovn-ic/templates/ic-monitor-svc.yaml
kind: Service
apiVersion: v1
metadata:
  name: ovn-ic-monitor
  namespace: kube-system
  labels:
    name: ovn-ic-monitor
    helm.sh/chart: boson-ovn-ic-1.16.0
    app.kubernetes.io/name: boson-ovn-ic
    app.kubernetes.io/instance: boson-ovn-ic
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ovn-ic
    app.kubernetes.io/part-of: BosonService
spec:
  ports:
    - name: metrics
      port: 10561
  clusterIP: None
  selector:
    app: ovn-ic-monitor
  sessionAffinity: None
---
# Source: boson-ovn-ic/templates/ic-monitor-deploy.yaml
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ovn-ic-monitor
  namespace: kube-system
  labels:
    name: ovn-ic-monitor
    helm.sh/chart: boson-ovn-ic-1.16.0
    app.kubernetes.io/name: boson-ovn-ic
    app.kubernetes.io/instance: boson-ovn-ic
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ovn-ic
    app.kubernetes.io/part-of: BosonService
spec:
  replicas: 3
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  selector:
    matchLabels:
      app: ovn-ic-monitor
  template:
    metadata:
      labels:
        app: ovn-ic-monitor
        component: network
        type: infra
    spec:
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: ovn-ic-monitor
              topologyKey: kubernetes.io/hostname
      priorityClassName: system-cluster-critical
      serviceAccountName: boson-ovn-ic
      hostNetwork: true
      imagePullSecrets:
       - name: sensecore-boson
      containers:
        - name: ovn-ic-monitor
          image: registry.sensetime.com/sensecore-boson/kube-ovn/kube-ovn:v1.9.3.14-**************
          imagePullPolicy: IfNotPresent
          command: ["/kube-ovn/kube-ovn-ic-monitor"]
          securityContext:
            capabilities:
              add: ["SYS_NICE"]
          env:
            - name: NODE_IPS
              value: ************,************,************
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          resources:
            limits:
              cpu: 3
              memory: 4Gi
            requests:
              cpu: 300m
              memory: 300Mi
          volumeMounts:
            - mountPath: /var/run/openvswitch
              name: host-run-ovs
            - mountPath: /var/run/ovn
              name: host-run-ovn
            - mountPath: /sys
              name: host-sys
              readOnly: true
            - mountPath: /etc/openvswitch
              name: host-config-openvswitch
            - mountPath: /etc/ovn
              name: host-config-ovn
            - mountPath: /var/log/openvswitch
              name: host-log-ovs
            - mountPath: /var/log/ovn
              name: host-log-ovn
            - mountPath: /etc/localtime
              name: localtime
          readinessProbe:
            exec:
              command:
              - cat
              - /var/run/ovn/ovn_ic_nb_db.pid
            periodSeconds: 10
            timeoutSeconds: 45
          livenessProbe:
            exec:
              command:
              - cat
              - /var/run/ovn/ovn_ic_nb_db.pid
            initialDelaySeconds: 30
            periodSeconds: 10
            failureThreshold: 5
            timeoutSeconds: 45
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-boson-ovn-ic-db: enabled
      volumes:
        - name: host-run-ovs
          hostPath:
            path: /run/openvswitch
        - name: host-run-ovn
          hostPath:
            path: /run/ovn
        - name: host-sys
          hostPath:
            path: /sys
        - name: host-config-openvswitch
          hostPath:
            path: /etc/origin/openvswitch
        - name: host-config-ovn
          hostPath:
            path: /etc/origin/ovn
        - name: host-log-ovs
          hostPath:
            path: /var/log/openvswitch
        - name: host-log-ovn
          hostPath:
            path: /var/log/ovn
        - name: localtime
          hostPath:
            path: /etc/localtime
---
# Source: boson-ovn-ic/templates/ic-server-deploy.yaml
kind: Deployment
apiVersion: apps/v1
metadata:
  name: ovn-ic-server
  namespace: kube-system
  annotations:
    kubernetes.io/description: |
      OVN IC Server
  labels:
    name: ovn-ic-server
    helm.sh/chart: boson-ovn-ic-1.16.0
    app.kubernetes.io/name: boson-ovn-ic
    app.kubernetes.io/instance: boson-ovn-ic
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ovn-ic
    app.kubernetes.io/part-of: BosonService
spec:
  replicas: 3
  strategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
  selector:
    matchLabels:
      app: ovn-ic-server
  template:
    metadata:
      labels:
        app: ovn-ic-server
        component: network
        type: infra
    spec:
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - effect: NoExecute
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app: ovn-ic-server
              topologyKey: kubernetes.io/hostname
      priorityClassName: system-cluster-critical
      serviceAccountName: boson-ovn-ic
      hostNetwork: true
      imagePullSecrets:
        - name: sensecore-boson
      containers:
        - name: ovn-ic-server
          image: registry.sensetime.com/sensecore-boson/kube-ovn/kube-ovn:v1.9.3.14-**************
          imagePullPolicy: IfNotPresent
          command:
            - bash
            - /kube-ovn/start-ic-db.sh
          securityContext:
            capabilities:
              add: ["SYS_NICE"]
          env:
            - name: ENABLE_SSL
              value: "false"
            - name: TS_NUM
              value: "3"
            - name: NODE_IPS
              value: ************,************,************
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: OVN_LEADER_PROBE_INTERVAL
              value: "5"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IPS
              valueFrom:
                fieldRef:
                  fieldPath: status.podIPs
          resources:
            requests:
              cpu: 300m
              memory: 200Mi
            limits:
              cpu: 3
              memory: 1Gi
          volumeMounts:
            - mountPath: /var/run/ovn
              name: host-run-ovn
            - mountPath: /etc/ovn
              name: host-config-ovn
            - mountPath: /var/log/ovn
              name: host-log-ovn
            - mountPath: /etc/localtime
              name: localtime
          readinessProbe:
            exec:
              command:
                - bash
                - /kube-ovn/ovn-ic-healthcheck.sh
            periodSeconds: 15
            timeoutSeconds: 45
          livenessProbe:
            exec:
              command:
                - bash
                - /kube-ovn/ovn-ic-healthcheck.sh
            initialDelaySeconds: 30
            periodSeconds: 15
            failureThreshold: 5
            timeoutSeconds: 4
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-boson-ovn-ic-db: enabled
      volumes:
        - name: host-run-ovn
          hostPath:
            path: /run/ovn
        - name: host-config-ovn
          hostPath:
            path: /etc/origin/ovn
        - name: host-log-ovn
          hostPath:
            path: /var/log/ovn
        - name: localtime
          hostPath:
            path: /etc/localtime
---
# Source: boson-ovn-ic/templates/ic-monitor-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  annotations:
  name: ovn-ic-monitor
  namespace: kube-system
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-ovn-ic-1.16.0
    app.kubernetes.io/name: boson-ovn-ic
    app.kubernetes.io/instance: boson-ovn-ic
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ovn-ic
    app.kubernetes.io/part-of: BosonService
spec:
  endpoints:
  - interval: 15s
    port: metrics
  namespaceSelector:
    matchNames:
    - kube-system
  selector:
    matchLabels:
      app: ovn-ic-monitor
