global:
  bosonEIPExporterImage: registry.sensetime.com/sensecore-boson/boson-eip-exporter:v1.0.5-5c8f01e-20250523144124
  imagePullSecret: sensecore-boson
  nodeSelectorTerms:
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-dps
    operator: Equal
    value: enabled

  bosonEIPExporter:
    metrics: "55999"
    DB:
        host: ************
        port: 43801
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
    collector:
      workers: 3
      interval: 15
      delay: 30
      duration: 15
      page_size: 5
      url: https://*************/api/v2/query/getData/ipAddr
      secretKey: 9fcfaedd366c5582427b9e038f1e4ff38686b426
