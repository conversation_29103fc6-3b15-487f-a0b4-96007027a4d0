---
# Source: boson-assistant/templates/assistant-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-assistant
  namespace: plat-boson-service
---
# Source: boson-assistant/templates/assistant-cm.yaml
apiVersion: v1
data:
  runtime-config.json: |-
    {
        "env_vars": {
            "auth_base": "http://localhost:55080/api/v1",
            "region": "cn-sh-01",
            "zone": "cn-sh-01b",
            "prp": "cn-sh-01b-prp01",
            "mailing": {
                "enable": true,
                "type": "outlook",
                "host": "smtp.partner.outlook.cn",
                "port": 587,
                "username": "<EMAIL>",
                "password": "xxx_send_from_password_xxx",
                "sender": "<EMAIL>",
                "to": "<EMAIL>"
            }
        },
        "initial_delay_seconds": 15,
        "case_trigger_interval": 900,
        "test_case_config": "test_case_config.json"
    }
kind: ConfigMap
metadata:
  name: boson-assistant-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-assistant/templates/assistant-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-assistant
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-assistant/templates/assistant-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-assistant
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-assistant
subjects:
- kind: ServiceAccount
  name: boson-assistant
  namespace: plat-boson-service
---
# Source: boson-assistant/templates/assistant-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-assistant-service
  namespace: plat-boson-service
  labels:
    app-name: boson-assistant-service
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-assistant
  ports:
  - name: http
    port: 55080
    protocol: TCP
  - name: grpc
    port: 55090
    protocol: TCP
  - name: metrics
    port: 55030
    protocol: TCP
---
# Source: boson-assistant/templates/assistant-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-assistant
  namespace: plat-boson-service
  labels:
    name: boson-assistant
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-assistant
  replicas: 1
  template:
    metadata:
      labels:
        app-name: boson-assistant
    spec:
      serviceAccount: boson-assistant
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-assistant
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-assistant
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.16.0-4-g2de2a6d-**************"
        imagePullPolicy: IfNotPresent
        command:
        - python3
        - api-server.py
        - --serve_mode
        - --url-base
        - "/api/v1"
        ports:
        - name: http
          containerPort: 55080
          protocol: TCP
        - name: grpc
          containerPort: 55090
          protocol: TCP
        - name: metrics
          containerPort: 55030
          protocol: TCP
        resources:
          limits:
            cpu: 300m
            memory: 300Mi
          requests:
            cpu: 150m
            memory: 150Mi
        env:
        - name: SERVICE_ENV_NAME
          value: "k8s"
        - name: SERVICE_RUNTIME_CONFIG
          value: "runtime-config.json"
        - name: BOSON_ASSISTANT_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_ASSISTANT_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_ASSISTANT_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_ASSISTANT_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: "/api/v1/health/check"
            port: 55080
          periodSeconds: 600
          initialDelaySeconds: 10
        volumeMounts:
        - name: runtime-config
          mountPath: /root/boson-assistant/runtime-config.json
          subPath: path/to/runtime-config.json
        - name: boson-provider-config
          mountPath: /root/boson-assistant/config/k8s-boson-provider.yaml
          subPath: path/to/boson-provider.yaml
      volumes:
      - name: runtime-config
        configMap:
          name: boson-assistant-config
          items:
          - key: runtime-config.json
            path: path/to/runtime-config.json
      - name: boson-provider-config
        configMap:
          name: boson-provider-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
---
# Source: boson-assistant/templates/assistant-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-assistant-service-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-internal.cn-sh-01b.sensecoreapi.cn
    http:
      paths:
      - path: /network/boson-assistant(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: boson-assistant-service
            port:
              number: 55080
  tls:
  - hosts:
    - network-internal.cn-sh-01b.sensecoreapi.cn
    secretName: tls-cnsh01b-api
---
# Source: boson-assistant/templates/assistant-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-assistant-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-assistant-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
---
# Source: boson-assistant/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-assistant-hook
  namespace: plat-boson-service
---
# Source: boson-assistant/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-assistant-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-assistant/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-assistant-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-assistant-hook
subjects:
- kind: ServiceAccount
  name: boson-assistant-hook
  namespace: plat-boson-service
---
# Source: boson-assistant/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-assistant-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-assistant-pre-hook
    helm.sh/chart: boson-assistant-1.18.0
    app.kubernetes.io/name: boson-assistant
    app.kubernetes.io/instance: boson-assistant
    app.kubernetes.io/version: 1.18.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-assistant
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-assistant-pre-hook
    spec:
      serviceAccount: boson-assistant-hook
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-business-app"
                operator: In
                values:
                - "sensecore"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-assistant-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.16.0-4-g2de2a6d-**************"
        command:
          - /bin/bash
          - -c
          - |
            sm=$(kubectl get servicemonitor  -n plat-diamond-metric | grep prometheus-node-exporter | awk '{print  $1}')
            if [[ -z $sm ]]; then
              echo "not found prometheus-node-exporter in ns plat-diamond-metric."
              exit 1
            fi

            echo "got servicemonitor $sm in plat-diamond-metric namespace"
            exist=$(kubectl get servicemonitor -n plat-diamond-metric $sm -o json |jq '.spec.endpoints[].metricRelabelings[] | select(.regex == "node_infiniband_port_data_transmitted_bytes_total;mlx._.")|has("regex")')
            if [ $exist == true ]; then
                echo "has metricRelabelings and received/transmitted replacements."
                exit 0
            fi

            echo "not found metricRelabelings, patch metricRelabelings"
            spec=$(kubectl get servicemonitor $sm -n plat-diamond-metric -o json | jq '
              .spec.endpoints |= map(
                .metricRelabelings = [
                  {
                    "regex": "node_infiniband_port_data_received_bytes_total;mlx._.",
                    "replacement": "training",
                    "sourceLabels": ["__name__", "device"],
                    "targetLabel": "device_scope"
                  },
                  {
                    "regex": "node_infiniband_port_data_received_bytes_total;mlx._bond_.",
                    "replacement": "data",
                    "sourceLabels": ["__name__", "device"],
                    "targetLabel": "device_scope"
                  },
                  {
                    "regex": "node_infiniband_port_data_transmitted_bytes_total;mlx._.",
                    "replacement": "training",
                    "sourceLabels": ["__name__", "device"],
                    "targetLabel": "device_scope"
                  },
                  {
                    "regex": "node_infiniband_port_data_transmitted_bytes_total;mlx._bond_.",
                    "replacement": "data",
                    "sourceLabels": ["__name__", "device"],
                    "targetLabel": "device_scope"
                  }
                ]
              ) | {spec: .spec}')
            echo "generate spec patch: $spec"

            kubectl patch servicemonitor $sm -n plat-diamond-metric --type='merge' -p "$spec"
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
