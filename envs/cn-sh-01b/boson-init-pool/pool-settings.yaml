pool_settings:
  cidr_pools:
    # 所有子网的掩码位数
    prefix: 26
    # 每个子网最前的保留 IP 数，本网地址和网关地址也算在其内
    start_reserve: 10
    # 每个子网最后的保留 IP 数，网关地址也算在期内
    end_reserve: 6
    # 01b 区的裸金属 IB/Service/Data vni 从 2500 后错了 256 个虽然不需要，从 2756 开始，已经分了 64 个，从 2756~2819
    # 第二段 64 个裸金属 vni，从 2820~2883
    vni_base: 2820
    # 01b 区的 RoCE 容器和裸金属 vni 从 1500 开始，已经分了 64 个，从 1500~1563
    # 第二段 64 个裸金属 vni，从 1564~1627
    roce_training_vni_base: 1564
    zone_prp: cn-sh-01b
    ib:
    - scope: TRAINING
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
    vlan:
    - scope: DATA
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
    - scope: SERVICE
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: 10.120.96.0/24
      # 子网 C 段结束地址
      end: 10.120.111.0/24
    # 专门给容器和裸金属训练网配置的网段
    training:
    - scope: TRAINING
      # type: pf_vlan 为固定网段和prefix的容器overlay网段配置
      type: pf_vlan
      # nic_count: 4 根据配置服务器的训练网卡数配置，一般配置2/4/8三种
      nic_count: 2
    - scope: TRAINING
      # type: bms_vlan 为固定网段和prefix的容器underlay网段配置
      type: bms_vlan
      # nic_count: 4 根据配置服务器的训练网卡数配置，一般配置2/4/8三种
      nic_count: 2
  eips:
    cidr: xxx.xxx.xxx.xxx/xx
    gateway: xxx.xxx.xxx.x
    sku: xxx
    ips: []
  nat_gateways:
    cidr: ************/23
    gateway: ************
    ips: []
  slbs:
    cidr: ************/23
    gateway: ************
    ips: []
