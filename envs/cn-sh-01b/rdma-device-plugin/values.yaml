global:
  rdmaDevicePluginImage: registry.sensetime.com/sensecore-boson/mellanox/k8s-rdma-shared-dev-plugin:v1.3.2-6-gf66e638-20231121160647
  imagePullSecret: sensecore-boson
  nodeSelectorTerms:
  - matchExpressions:
    - key: diamond.sensetime.com/nic-training-protocol
      values:
      - IB
      - RoCE
    - key: diamond.sensetime.com/nic-access-mode
      values:
      - PF
    - key: diamond.sensetime.com/hardware-net-data-training
      operator: NotIn
      values:
      - enabled
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-acp
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-cci
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/role-business-ailab
    operator: Equal
    value: enabled
  - effect: NoExecute
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  rdmaDevicePlugin:
    devices: ["ib0", "ib1", "ib2", "ib3", "ib4", "ib5", "ib6", "ib7"]
