
/*
# 生成云联电信 EIP 数据库插入语句
for eip_d in $(seq 18 21); do
    echo "INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('$(uuidgen)','220.250.27.${eip_d}','CHINA_UNICOM',false,'*************','*************/29');"
done
*/

\c boson_service_v2;

-- 检查插入前的数据记录，返回 4 条数据
SELECT * from eip_pools;

-- 插入 EIP 数据
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('f310bd27-4c6b-4f57-91f5-5ead553ee6c4','*************','CHINA_UNICOM',false,'*************','*************/29');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('31a5f9d1-dc3a-4db7-96fc-51e82e3dc967','*************','CHINA_UNICOM',false,'*************','*************/29');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('075ecb4f-a667-4df3-bcd5-1691d06226ba','*************','CHINA_UNICOM',false,'*************','*************/29');
INSERT INTO eip_pools (eip_pool_id,eip_ip,sku,allocated,gateway,cidr) VALUES ('f5a1fd99-597b-4f52-ba7e-962f69b6069d','*************','CHINA_UNICOM',false,'*************','*************/29');

-- 检查插入后的数据记录，返回 8 条数据
SELECT * from eip_pools;
