 /*
# 生成中国联通 EIP 数据库插入语句
 for ((i=64; i< 92; i++)); do echo "insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('$(uuidgen)', '112.111.7.$i', false, '***********', '***********/24','CHINA_UNICOM');";
 done
*/

\c boson_service_v2;

-- 检查插入前的数据记录，返回 4 条数据
SELECT * from eip_pools;

-- 插入 EIP 数据
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('8723d09a-5bca-4b54-ac18-6bd8f6816a8a', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('86371604-c677-47c4-826c-393b2773e5b9', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('55cdb2e4-56f7-4b45-9c33-861dbd79b9e9', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('fc644916-9af6-4e17-ba9f-7a5027b25a36', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('89a6cf55-eb30-4a45-99ef-f20c0e345355', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('026ac656-6e98-43b8-9b7f-8ed435e04ddf', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('1e486c15-8a30-4f5d-aaf7-acf2a1044f01', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('8da6c026-59d4-473d-b9a4-384147f3f039', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('0a057587-912a-49cb-ad04-24ecab411f5f', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('4707bb4c-cf80-4ae1-9cd4-3c71ed9af47c', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('c07dafde-7c09-4b1e-b9eb-9c7761a054a6', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('45f71bf5-c0c0-446a-ade0-ce56c0614464', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('8eea5571-1bca-4fb3-bacc-506d013d447c', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('7e1cb6f5-a9b9-4a34-a9b6-b12ff7073258', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('ebea070a-fc6a-4ca1-85a2-d9ce14b55548', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('40bca156-8588-445f-b98c-8d24f39327b6', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('a74bf017-949b-4bc9-a0af-519dd21784b4', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('da8a896f-2ce0-459e-826b-e9a6ae233aca', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('b4c487cf-0fec-4c69-aafc-6b4dfbbc2a12', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('66cbc6ef-1ec8-4b20-aa5c-1000bab58f3c', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('e1864dee-2103-47c7-82b3-24bc9ad669e4', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('2136cdd9-cb46-49e4-b3df-07f3e25c9d0c', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('cd51fe55-7cdf-4e26-9a07-9cca2b24c932', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('fd238d10-5b7a-4f0f-a215-cac425263ba4', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('45090dfd-4b46-403d-aa7c-1fc4095dc5fe', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('323c86e8-4bc2-4c1b-b5b2-9db35a3f14dc', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('6fe774a3-18bd-4234-8df7-4d0c8ddd0c12', '************', false, '***********', '***********/24','CHINA_UNICOM');
insert into eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku) values ('d2e91a24-a976-4832-8921-fe47a5ebb961', '************', false, '***********', '***********/24','CHINA_UNICOM');

-- 检查插入后的数据记录，返回 32 条数据
SELECT * from eip_pools;
