---
# Source: rdma-device-plugin/templates/rdma-device-plugin-cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rdma-device-plugin-4090-config
  namespace: kube-system
  labels:
    helm.sh/chart: rdma-device-plugin-1.9.0
    app.kubernetes.io/name: rdma-device-plugin-4090
    app.kubernetes.io/instance: rdma-device-plugin-4-datanic
    app.kubernetes.io/version: 1.9.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: rdma-device-plugin-4090
    app.kubernetes.io/part-of: BosonService
data:
  config.json: |
    {
        "periodicUpdateInterval": 300,
        "configList": [
           {
             "resourceName": "roce",
             "resourcePrefix": "rdma-training",
             "rdmaHcaMax": 1,
             "selectors": {
               "vendors": ["15b3"],
               "deviceIDs": ["1017"]
             }
           }
        ]
    }
---
# Source: rdma-device-plugin/templates/rdma-device-plugin-ds.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: rdma-device-plugin-4090
  namespace: kube-system
  labels:
    name: rdma-device-plugin-4090
    helm.sh/chart: rdma-device-plugin-1.9.0
    app.kubernetes.io/name: rdma-device-plugin-4090
    app.kubernetes.io/instance: rdma-device-plugin-4-datanic
    app.kubernetes.io/version: 1.9.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: rdma-device-plugin-4090
    app.kubernetes.io/part-of: BosonService
spec:
  selector:
    matchLabels:
      name: rdma-device-plugin-4090
  template:
    metadata:
      labels:
        name: rdma-device-plugin-4090
    spec:
      hostNetwork: true
      priorityClassName: system-node-critical
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: diamond.sensetime.com/nic-training-protocol
                operator: In
                values:
                - "RoCE"
              - key: diamond.sensetime.com/nic-access-mode
                operator: In
                values:
                - "PF"
              - key: diamond.sensetime.com/hardware-net-data-training
                operator: In
                values:
                - "enabled"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-acp
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-cci
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-ailab
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoExecute
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: rdma-device-plugin-4090
        image: "registry.sensetime.com/sensecore-boson/mellanox/k8s-rdma-shared-dev-plugin:v1.3.2-6-gf66e638-20231121160647"
        imagePullPolicy: IfNotPresent
        securityContext:
          privileged: true
        volumeMounts:
          - name: device-plugin
            mountPath: /var/lib/kubelet/
          - name: device-plugin-data
            mountPath: /data/kubelet/
          - name: rdma-device-plugin-4090-config
            mountPath: /k8s-rdma-shared-dev-plugin
          - name: devs
            mountPath: /dev/
      volumes:
        - name: device-plugin
          hostPath:
            path: /var/lib/kubelet/
        - name: device-plugin-data
          hostPath:
            path: /data/kubelet/
        - name: rdma-device-plugin-4090-config
          configMap:
            name: rdma-device-plugin-4090-config
            items:
            - key: config.json
              path: config.json
        - name: devs
          hostPath:
            path: /dev/
---
# Source: rdma-device-plugin/templates/rdma-device-plugin-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-rdma-device-plugin-4090-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - rdma-device-plugin-4090-*
        namespaces:
        - kube-system
