\c boson_service_v2;

/*
# 生成上移 EIP 数据库插入语句
for eip_d in $(seq 40 43); do
    echo "INSERT INTO eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku, line) values ('$(uuidgen)', '183.195.153.${eip_d}', false, '', '', 'SHANGYI_SPECIAL', 'SHANGYI_SPECIAL');";
done
*/

/*
# 导入EIP之前查询，应该没有任何输出
SELECT * FROM eip_pools WHERE sku='SHANGYI_SPECIAL';
*/

-- 执行EIP数据导入
INSERT INTO eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku, line) values ('62dfa52b-a785-4114-9aae-264996f1c87d', '**************', false, '', '', 'SHANGYI_SPECIAL', 'SHANGYI_SPECIAL');
INSERT INTO eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku, line) values ('186ed975-04a1-4064-9e67-69584170bfaa', '**************', false, '', '', 'SHANGYI_SPECIAL', 'SHANGYI_SPECIAL');
INSERT INTO eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku, line) values ('ac98dc78-419d-4125-93ec-36c39113cf8f', '**************', false, '', '', 'SHANGYI_SPECIAL', 'SHANGYI_SPECIAL');
INSERT INTO eip_pools (eip_pool_id, eip_ip, allocated, gateway, cidr, sku, line) values ('a3c2e53f-0ae3-4c5e-80e1-55b498aaa5aa', '**************', false, '', '', 'SHANGYI_SPECIAL', 'SHANGYI_SPECIAL');


/*
# 导入之后，验证结果，有4条EIP输出记录
SELECT * FROM eip_pools WHERE sku='SHANGYI_SPECIAL';
*/

/*
# 回滚步骤
DELETE FROM eip_pools WHERE sku='SHANGYI_SPECIAL';
*/
