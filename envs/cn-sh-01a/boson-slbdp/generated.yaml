---
# Source: boson-slbdp/templates/slbdp-cm.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: boson-slbdp
  namespace: plat-boson-infra
data:
  host-10-118-31-10-10.118.31.10_keepalived.conf: |-
    local_address_group host1_localip {
      ************-101 eth60
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host1_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
  host-10-118-31-11-10.118.31.11_keepalived.conf: |-
    local_address_group host2_localip {
      **************-229 eth60
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host2_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
  host-10-118-31-12-10.118.31.12_keepalived.conf: |-
    local_address_group host3_localip {
      ************-101 eth61
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host3_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
  host-10-118-31-13-10.118.31.13_keepalived.conf: |-
    local_address_group host4_localip {
      **************-229 eth61
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.36 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.37 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.38 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.39 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.40 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.41 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.42 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.43 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.44 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.45 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.46 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.47 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************ 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 80 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************** 80 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.24 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.25 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.26 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.27 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.28 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.29 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
    virtual_server ************** 443 {
      delay_loop 6
      lb_algo wlc
      lb_kind FNAT
      connect_timeout 3000
      protocol TCP
      laddr_group_name host4_localip
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************* 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.79 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.80 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.81 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.82 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.83 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.84 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.85 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.86 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.104 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.105 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.106 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.107 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.108 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.109 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.110 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.111 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.112 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.113 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.114 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.115 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.116 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.117 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.118 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.119 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.120 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.121 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.122 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.123 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.124 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server 10.117.164.125 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
      real_server ************** 443 {
        weight 100
        connect_timeout 3000
        inhibit_on_failure true
        TCP_CHECK {
          connect_timeout 3
          retry 3
          delay_before_retry 3
        }
      }
    }
  host-10-118-31-10-10.118.31.10_slb-dp.yaml: |-
    host:
      ports:
      - pci: "0000:31:00.0"
        type: wan
        dpvs_name: eth50
        kni_name: eth50.kni
      - pci: "0000:b1:00.0"
        type: lan
        dpvs_name: eth60
        kni_name: eth60.kni
      cpu_mask: 0xffffffff
    dpvs:
      log_level: INFO
      log_file: /var/log/dpvs.log
      log_async_mode: on
      kni: on
    env:
      inter_connect_ip:
        eth50.kni: *************/30
        eth60.kni: *************/30
      hc_ip:
        ip: ************/32
        nexthop: *************
        port: eth60.kni
    route:
      kernel:
      dpvs:
      - (************/24,*************,eth60)
      - (************/24,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
    rule:
      kernel:
      - (all, ************/24, 200)
      - (all, ************/24, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, *************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
    agent:
      listen_ip: 0.0.0.0
      listen_port: 8080
    metric:
      listen_ip: 0.0.0.0
      listen_port: 5000
  host-10-118-31-11-10.118.31.11_slb-dp.yaml: |-
    host:
      ports:
      - pci: "0000:31:00.0"
        type: wan
        dpvs_name: eth50
        kni_name: eth50.kni
      - pci: "0000:b1:00.0"
        type: lan
        dpvs_name: eth60
        kni_name: eth60.kni
      cpu_mask: 0xffffffff
    dpvs:
      log_level: INFO
      log_file: /var/log/dpvs.log
      log_async_mode: on
      kni: on
    env:
      inter_connect_ip:
        eth50.kni: *************/30
        eth60.kni: *************/30
      hc_ip:
        ip: ************29/32
        nexthop: *************
        port: eth60.kni
    route:
      kernel:
      dpvs:
      - (************/24,*************,eth60)
      - (************/24,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (*************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
      - (**************/32,*************,eth60)
    rule:
      kernel:
      - (all, ************/24, 200)
      - (all, ************/24, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, *************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
    agent:
      listen_ip: 0.0.0.0
      listen_port: 8080
    metric:
      listen_ip: 0.0.0.0
      listen_port: 5000
  host-10-118-31-12-10.118.31.12_slb-dp.yaml: |-
    host:
      ports:
      - pci: "0000:31:00.1"
        type: wan
        dpvs_name: eth51
        kni_name: eth51.kni
      - pci: "0000:b1:00.1"
        type: lan
        dpvs_name: eth61
        kni_name: eth61.kni
      cpu_mask: 0xffffffff
    dpvs:
      log_level: INFO
      log_file: /var/log/dpvs.log
      log_async_mode: on
      kni: on
    env:
      inter_connect_ip:
        eth51.kni: *************/30
        eth61.kni: *************/30
      hc_ip:
        ip: ************/32
        nexthop: *************
        port: eth61.kni
    route:
      kernel:
      dpvs:
      - (************/24,*************,eth61)
      - (************/24,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
    rule:
      kernel:
      - (all, ************/24, 200)
      - (all, ************/24, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, *************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
    agent:
      listen_ip: 0.0.0.0
      listen_port: 8080
    metric:
      listen_ip: 0.0.0.0
      listen_port: 5000
  host-10-118-31-13-10.118.31.13_slb-dp.yaml: |-
    host:
      ports:
      - pci: "0000:31:00.1"
        type: wan
        dpvs_name: eth51
        kni_name: eth51.kni
      - pci: "0000:b1:00.1"
        type: lan
        dpvs_name: eth61
        kni_name: eth61.kni
      cpu_mask: 0xffffffff
    dpvs:
      log_level: INFO
      log_file: /var/log/dpvs.log
      log_async_mode: on
      kni: on
    env:
      inter_connect_ip:
        eth51.kni: *************/30
        eth61.kni: *************/30
      hc_ip:
        ip: ************29/32
        nexthop: *************
        port: eth61.kni
    route:
      kernel:
      dpvs:
      - (************/24,*************,eth61)
      - (************/24,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (*************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
      - (**************/32,*************,eth61)
    rule:
      kernel:
      - (all, ************/24, 200)
      - (all, ************/24, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, *************/32, 200)
      - (all, ************/32, 200)
      - (all, ************/32, 200)
      - (all, *************/32, 200)
      - (all, *************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
      - (all, **************/32, 200)
    agent:
      listen_ip: 0.0.0.0
      listen_port: 8080
    metric:
      listen_ip: 0.0.0.0
      listen_port: 5000
---
# Source: boson-slbdp/templates/slbdp-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-slbdp-service
  namespace: plat-boson-infra
  labels:
    app-name: boson-slbdp-service
    helm.sh/chart: boson-slbdp-1.16.0
    app.kubernetes.io/name: boson-slbdp
    app.kubernetes.io/instance: boson-slbdp
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  selector:
    app-name: boson-slbdp
  ports:
  - name: metrics
    port: 5000
    protocol: TCP
---
# Source: boson-slbdp/templates/slbdp-ds.yaml
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: boson-slbdp
  namespace: plat-boson-infra
  labels:
    app-name: boson-slbdp
spec:
  selector:
    matchLabels:
      app-name: boson-slbdp
  updateStrategy:
    type: OnDelete
  template:
    metadata:
      labels:
        app-name: boson-slbdp
    spec:
      hostNetwork: true
      nodeSelector:
        kubernetes.io/os: "linux"
        diamond.sensetime.com/role-infra-slb-platform-dedicated: enabled
      tolerations:
      - effect: NoExecute
        key: diamond.sensetime.com/role-infra-slb-platform-dedicated
        operator: Equal
        value: enabled
      containers:
      - name: slbdp
        image: registry.sensetime.com/sensecore-boson/slbdp:v1.0-12-lower-cpu
        imagePullPolicy: IfNotPresent
        env:
        - name: K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        livenessProbe:
          exec:
            command:
            - /slbdp/bin/dpip
            - link
            - show
          initialDelaySeconds: 30
          timeoutSeconds: 30
          periodSeconds: 60
        readinessProbe:
          exec:
            command:
            - /slbdp/bin/dpip
            - link
            - show
          initialDelaySeconds: 30
          timeoutSeconds: 30
          periodSeconds: 60
        resources:
          limits:
            hugepages-2Mi: 64Gi
            memory: 16Gi
        securityContext:
          runAsUser: 0
          privileged: true
        volumeMounts:
        - name: usr
          mountPath: /usr
        - name: bin
          mountPath: /bin
        - name: dev
          mountPath: /dev
        - name: log
          mountPath: /var/log
        - name: mnt
          mountPath: /mnt
        - name: sys
          mountPath: /sys
        - name: etc
          mountPath: /etc
        - name: conf
          mountPath: /slbdp/conf/
      volumes:
      - name: usr
        hostPath:
          path: /usr
      - name: bin
        hostPath:
          path: /bin
      - name: dev
        hostPath:
          path: /dev
      - name: log
        hostPath:
          path: /var/log
      - name: mnt
        hostPath:
          path: /mnt
      - name: sys
        hostPath:
          path: /sys
      - name: etc
        hostPath:
          path: /etc
      - name: conf
        configMap:
          name: boson-slbdp
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      serviceAccount: default
      serviceAccountName: default
      priorityClassName: system-cluster-critical
---
# Source: boson-slbdp/templates/slbdp-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-slbdp-host-path
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-slbdp-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-slbdp/templates/slbdp-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-slbdp-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-slbdp-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-slbdp/templates/slbdp-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-slbdp-servicemonitor
  namespace: plat-boson-infra
  labels:
    prometheus: prometheus
    helm.sh/chart: boson-slbdp-1.16.0
    app.kubernetes.io/name: boson-slbdp
    app.kubernetes.io/instance: boson-slbdp
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-slbdp-service
  namespaceSelector:
    matchNames:
    - plat-boson-infra
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
