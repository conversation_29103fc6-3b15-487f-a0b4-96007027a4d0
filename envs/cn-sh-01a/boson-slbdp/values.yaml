global:
  enableServiceMonitor: true
  bosonSlbdpImage: registry.sensetime.com/sensecore-boson/slbdp:v1.0-12-lower-cpu
  namespace: plat-boson-infra
  name: boson-slbdp
  podName: slbdp
  memory: 16Gi
  hugepage2m: 64Gi
  configmap:
    name: boson-slbdp
  service:
    ports:
      metrics: 5000
instance:
  - host: host-10-118-31-10-************
    name: keepalived.conf
    local_ip:
      name: host1_localip
      member:
      - range: ************-101
        port: eth60
    vs_list:
    - name: oss_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
  - host: host-10-118-31-11-************
    name: keepalived.conf
    local_ip:
      name: host2_localip
      member:
      - range: ************30-229
        port: eth60
    vs_list:
    - name: oss_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
  - host: host-10-118-31-12-************
    name: keepalived.conf
    local_ip:
      name: host3_localip
      member:
      - range: 10.117.179.2-101
        port: eth61
    vs_list:
    - name: oss_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
  - host: host-10-118-31-13-************
    name: keepalived.conf
    local_ip:
      name: host4_localip
      member:
      - range: **************-229
        port: eth61
    vs_list:
    - name: oss_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
      - rip: **************
        rport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: ************
        rport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
      - rip: **************
        rport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: **************
        rport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
      proto: TCP
      lb_algo: wlc
      rs:
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: *************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
      - rip: **************
        rport: 443
slbdp:
  - host: host-10-118-31-10-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:31:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        - pci: "0000:b1:00.0"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth50.kni: *************/30
          eth60.kni: *************/30
        hc_ip:
          ip: ************/32
          nexthop: *************
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (************/24,*************,eth60)
        - (************/24,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
      rule:
        kernel:
        - (all, ************/24, 200)
        - (all, ************/24, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, *************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
  - host: host-10-118-31-11-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:31:00.0"
          type: wan
          dpvs_name: eth50
          kni_name: eth50.kni
        - pci: "0000:b1:00.0"
          type: lan
          dpvs_name: eth60
          kni_name: eth60.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth50.kni: *************/30
          eth60.kni: *************/30
        hc_ip:
          ip: ************29/32
          nexthop: *************
          port: eth60.kni
      route:
        kernel:
        dpvs:
        - (************/24,*************,eth60)
        - (************/24,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (*************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
        - (**************/32,*************,eth60)
      rule:
        kernel:
        - (all, ************/24, 200)
        - (all, ************/24, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, *************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
  - host: host-10-118-31-12-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:31:00.1"
          type: wan
          dpvs_name: eth51
          kni_name: eth51.kni
        - pci: "0000:b1:00.1"
          type: lan
          dpvs_name: eth61
          kni_name: eth61.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth51.kni: *************/30
          eth61.kni: *************/30
        hc_ip:
          ip: ************/32
          nexthop: *************
          port: eth61.kni
      route:
        kernel:
        dpvs:
        - (************/24,*************,eth61)
        - (************/24,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
      rule:
        kernel:
        - (all, ************/24, 200)
        - (all, ************/24, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, *************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
  - host: host-10-118-31-13-************
    name: slb-dp.yaml
    values: |-
      host:
        ports:
        - pci: "0000:31:00.1"
          type: wan
          dpvs_name: eth51
          kni_name: eth51.kni
        - pci: "0000:b1:00.1"
          type: lan
          dpvs_name: eth61
          kni_name: eth61.kni
        cpu_mask: 0xffffffff
      dpvs:
        log_level: INFO
        log_file: /var/log/dpvs.log
        log_async_mode: on
        kni: on
      env:
        inter_connect_ip:
          eth51.kni: *************/30
          eth61.kni: *************/30
        hc_ip:
          ip: ************29/32
          nexthop: *************
          port: eth61.kni
      route:
        kernel:
        dpvs:
        - (************/24,*************,eth61)
        - (************/24,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (*************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
        - (**************/32,*************,eth61)
      rule:
        kernel:
        - (all, ************/24, 200)
        - (all, ************/24, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, *************/32, 200)
        - (all, ************/32, 200)
        - (all, ************/32, 200)
        - (all, *************/32, 200)
        - (all, *************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
        - (all, **************/32, 200)
      agent:
        listen_ip: 0.0.0.0
        listen_port: 8080
      metric:
        listen_ip: 0.0.0.0
        listen_port: 5000
