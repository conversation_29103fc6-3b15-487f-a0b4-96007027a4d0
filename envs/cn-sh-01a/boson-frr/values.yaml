global:
  enableServiceMonitor: true
  namespace: plat-boson-infra
  name: boson-frr
  podName: boson-frr
  configmap:
    name: boson-frr-conf
  serviceInfo:
    envName: prod-cn-sh-01a
    region: cn-sh-01
    az: cn-sh-01a
    bosonFrrImage: registry.sensetime.com/sensecore-boson/quay.io/frrouting/frr:9.0.1_installed_tools_01
  service:
    ports:
      metrics: 5000
  oss_vip: **************/25
instance:
  - host: host-10-118-31-10-************
    values:
    oss_vip_cidr: **************/25
    oss_vips:
    - name: oss_http_vs1
      vip: **************
      vport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
    lvs_local_ip: ************/25
    lvs_rs_hc_ip: ************/32
    frr_host_name: HOST-10-118-31-10
    frr_bgp_as: 65511
    frr_bgp_router_id: ************
    nics:
    - nic: eth50
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65500
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth60
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65501
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
  - host: host-10-118-31-11-************
    values:
    oss_vip_cidr: **************/25
    oss_vips:
    - name: oss_http_vs1
      vip: **************
      vport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
    lvs_local_ip: **************/25
    lvs_rs_hc_ip: **************/32
    frr_host_name: HOST-10-118-31-11
    frr_bgp_as: 65512
    frr_bgp_router_id: ************
    nics:
    - nic: eth50
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65500
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth60
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65501
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
  - host: host-10-118-31-12-************
    values:
    oss_vip_cidr: **************/25
    oss_vips:
    - name: oss_http_vs1
      vip: **************
      vport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
    lvs_local_ip: 10.117.179.0/25
    lvs_rs_hc_ip: 10.117.179.1/32
    frr_host_name: HOST-10-118-31-12
    frr_bgp_as: 65513
    frr_bgp_router_id: ************
    nics:
    - nic: eth51
      local_kni_ip: 10.118.191.33/30
      peer_ip: 10.118.191.34
      peer_bgp_as: 65502
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth61
      local_kni_ip: 10.117.177.33/30
      peer_ip: 10.117.177.34
      peer_bgp_as: 65503
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
  - host: host-10-118-31-13-************
    values:
    oss_vip_cidr: **************/25
    oss_vips:
    - name: oss_http_vs1
      vip: **************
      vport: 80
    - name: oss_http_vs2
      vip: **************
      vport: 80
    - name: oss_http_vs3
      vip: **************
      vport: 80
    - name: oss_http_vs4
      vip: **************
      vport: 80
    - name: oss_http_vs5
      vip: **************
      vport: 80
    - name: oss_http_vs6
      vip: **************
      vport: 80
    - name: oss_http_vs7
      vip: **************
      vport: 80
    - name: oss_http_vs8
      vip: **************
      vport: 80
    - name: auto_http_vs1
      vip: **************
      vport: 80
    - name: auto_http_vs2
      vip: **************
      vport: 80
    - name: sh30_http_vs
      vip: **************
      vport: 80
    - name: sh34_http_vs
      vip: **************
      vport: 80
    - name: sh36_http_vs
      vip: **************
      vport: 80
    - name: sh40_http_vs
      vip: **************
      vport: 80
    - name: sh41_http_vs
      vip: **************
      vport: 80
    - name: sh1984_http_vs
      vip: **************
      vport: 80
    - name: sh1986_http_vs
      vip: **************
      vport: 80
    - name: sh1988_http_vs
      vip: **************
      vport: 80
    - name: 1024ssd_http_vs
      vip: **************
      vport: 80
    - name: 1024hdd_http_vs
      vip: **************
      vport: 80
    - name: crawler_http_vs
      vip: **************
      vport: 80
    - name: sh1424ssd_http_vs
      vip: **************
      vport: 80
    - name: sh1424hdd_http_vs
      vip: **************
      vport: 80
    - name: bj17_http_vs
      vip: **************
      vport: 80
    - name: bj30_http_vs
      vip: **************
      vport: 80
    - name: sz20_http_vs
      vip: **************
      vport: 80
    - name: sdcoss2_http_vs
      vip: **************
      vport: 80
    - name: lg_http_vs
      vip: **************
      vport: 80
    - name: oss_https_vs1
      vip: **************
      vport: 443
    - name: oss_https_vs2
      vip: **************
      vport: 443
    - name: oss_https_vs3
      vip: **************
      vport: 443
    - name: oss_https_vs4
      vip: **************
      vport: 443
    - name: oss_https_vs7
      vip: **************
      vport: 443
    - name: oss_https_vs8
      vip: **************
      vport: 443
    lvs_local_ip: **************/25
    lvs_rs_hc_ip: **************/32
    frr_host_name: HOST-10-118-31-13
    frr_bgp_as: 65514
    frr_bgp_router_id: ************
    nics:
    - nic: eth51
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65502
      route_policy_export: public_vip_valid_export
      route_policy_import: public_vip_valid_import
    - nic: eth61
      local_kni_ip: *************/30
      peer_ip: *************
      peer_bgp_as: 65503
      route_policy_export: private_localip_valid_export
      route_policy_import: private_localip_valid_import
