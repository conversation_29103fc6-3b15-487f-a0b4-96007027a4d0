global:
  envName: prod-cn-sh-01a
  bosonNetControllerImage: registry.sensetime.com/sensecore-boson/boson-net-controller:v1.20.0-10-gd7ef4d38-20250522170046
  hookImage: registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-20231224181923
  imagePullSecret: sensecore-boson
  domainName: network-internal.cn-sh-01.sensecoreapi.cn
  sslSecretName: tls-cnsh01-api
  nodeSelectorTerms:
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  wxURL: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=999c558c-579a-4fe9-a1b2-987337a9d839
  pgsql:
    host: ************
    port: 61831
    user: boson
    db: boson_service_v2
    password: xxxxxxxxxxxx
  boson_default:
    zone_name_for_az_resource: cn-sh-01a
    zone_name_for_region_resource: cn-sh-01z
  bosonNetController:
    memLimits: 2000Mi
    config:
      disableFeatures:
      - snat
      - bms-lazy-attach
      gcEnable: false
      netDeviceEnable: true
      netDeviceServerURL: http://************:5070
      globalSnatRuleName: sensecore_prod_vpc_default_snat_rule_cn_sh_01
      globalSnatQosPolicyName: sensecore_prod_default_tenant_snat_qos_policy
    service:
      clusterIP: **********
      ports:
        xds: 18000
        slbHttp: 52040
        slbGrpc: 52090
