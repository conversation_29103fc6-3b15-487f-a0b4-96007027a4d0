global:
  region: ms-sc-01
  zone: ms-sc-01a
  namespace: plat-boson-infra
  envName: prod-ms-sc-01a
  bosonOmsImage: registry.sensetime.com/sensecore-boson/boson-oms:v1.17.0-353d93b-20241120142756
  imagePullSecret: sensecore-boson
  domainName: boson-oms-internal.ms-sc-01.maoshanwangtechapi.com
  sslSecretName: tls-mssc01-api
  tolerations:
  - effect: NoExecute
    key: diamond.sensetime.com/role-k8s-master
    operator: Equal
    value: enabled
  - effect: NoSchedule
    key: diamond.sensetime.com/role-business-app
    operator: Equal
    value: sensecore
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-resource-prp
    operator: Exists
  - effect: NoSchedule
    key: diamond.sensetime.com/belong-ns-layer
    operator: Equal
    value: iaas
  bosonOMS:
    region: ms-sc-01
    k8sInfo:
      enabled: False
      region: ms-sc-01
      dryrun: False
      env: prod
      kubeConfigs:
      - zone: ms-sc-01a
        region: ms-sc-01
        env: prod
        enabled: True
        kubeConfigFile: /boson/ms-sc-01a/bosonOmsOp.kubeconfig
    omsConfig:
      ndcServer:
        serverURL: http://************:5093
      southDB:
        host: *************
        port: 55380
        user: boson
        db: boson_service_v2
        password: xxxxxxxxxxxx
      omsDB:
        host: *************
        port: 55380
        user: boson
        db: boson_oms
        password: xxxxxxxxxxxx
      providerAddr:
        - zone: ms-sc-01z
          host: network-internal.ms-sc-01.maoshanwangtechapi.com
        - zone: ms-sc-01a
          host: network-internal.ms-sc-01.maoshanwangtechapi.com
