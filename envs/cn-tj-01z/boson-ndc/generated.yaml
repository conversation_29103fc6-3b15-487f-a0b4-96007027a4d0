---
# Source: boson-ndc/templates/ndc-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-ndc
  namespace: plat-boson-service
---
# Source: boson-ndc/templates/ndc-cm.yaml
apiVersion: v1
data:
  ndc.yaml: |-
    ndc:
      http_port: 56080
      grpc_port: 56090
      metrics_port: 56030
      key: PYCxYKLLDXC5BaaXyuU8bOT1d1PcStQ3sHwwHiKH9eY=
      token_key: gAAAAABk3HLDLyvZ-w3IffBfqD01l5CBLFowlLucx7RLOTAZNozmnRt11AEtyWKd0sGXJcFBKVzpIyFwd7IQIFZKMc65QVaWUA==
      debug: false
      log:
        ndc_name: ndc.log
        ndc_level: DEBUG
        ndc_backupcount: 0
        history_name: config_history.log
        history_backupcount: 0
      netconf:
        port: 830
        user: sensecore
        password: gAAAAABmVtcPIftgD3Fm5itkMqEpsA3LY8egonASH3J6LjH9KbV17rub_cl6zSKjcSSDsTINKi3dc2HiDad0uxsmgGHTByxQIwcrFUkCadu5pI6J7rhxenk=
      ssh:
        port: 22
        user: sensecore
        password: gAAAAABmVtcPIftgD3Fm5itkMqEpsA3LY8egonASH3J6LjH9KbV17rub_cl6zSKjcSSDsTINKi3dc2HiDad0uxsmgGHTByxQIwcrFUkCadu5pI6J7rhxenk=
      database:
        enabled: true
        password_text: text
        dbname: boson_service_v2
        host: *************
        port: 35108
        username: boson
        password: xxxxxxxxxxxx
kind: ConfigMap
metadata:
  name: boson-ndc-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-ndc-1.16.0
    app.kubernetes.io/name: boson-ndc
    app.kubernetes.io/instance: boson-ndc
    app.kubernetes.io/version: 1.2.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ndc
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-ndc/templates/ndc-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-ndc
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-ndc/templates/ndc-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-ndc
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-ndc
subjects:
- kind: ServiceAccount
  name: boson-ndc
  namespace: plat-boson-service
---
# Source: boson-ndc/templates/ndc-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-ndc-service
  namespace: plat-boson-service
  labels:
    app-name: boson-ndc-service
    helm.sh/chart: boson-ndc-1.16.0
    app.kubernetes.io/name: boson-ndc
    app.kubernetes.io/instance: boson-ndc
    app.kubernetes.io/version: 1.2.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ndc
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-ndc
  ports:
  - name: http
    port: 56080
    protocol: TCP
  - name: grpc
    port: 56090
    protocol: TCP
  - name: metrics
    port: 56030
    protocol: TCP
---
# Source: boson-ndc/templates/ndc-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-ndc
  namespace: plat-boson-service
  labels:
    name: boson-ndc
    helm.sh/chart: boson-ndc-1.16.0
    app.kubernetes.io/name: boson-ndc
    app.kubernetes.io/instance: boson-ndc
    app.kubernetes.io/version: 1.2.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ndc
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-ndc
  replicas: 2
  template:
    metadata:
      labels:
        app-name: boson-ndc
    spec:
      serviceAccount: boson-ndc
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-ndc
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/role-infra-ndc"
                operator: In
                values:
                - "enabled"
      tolerations:
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Equal
        value: "cn-sh-01a-control"
      - key: diamond.sensetime.com/belong-ns-layer
        effect: NoSchedule
        operator: Equal
        value: "iaas"
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-dps
        effect: NoSchedule
        operator: Equal
        value: "enabled"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-ndc
        image: "registry.sensetime.com/sensecore-boson/boson-ndc:v1.20.0-22-g0d39c15-20250520203559"
        command: [ "/usr/bin/python3", "main.py" ]
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 56080
          protocol: TCP
        - name: grpc
          containerPort: 56090
          protocol: TCP
        - name: metrics
          containerPort: 56030
          protocol: TCP
        resources:
          limits:
            cpu: 400m
            memory: 1024Mi
          requests:
            cpu: 100m
            memory: 200Mi
        env:
        - name: BOSON_NDC_VAR_NAME
          value: "boson-ndc"
        - name: CONFIG
          value: "/root/boson-ndc/conf/ndc.yaml"
        - name: ENVNAME
          value: prod-cn-tj-01z
        - name: BOSON_NDC_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NDC_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NDC_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NDC_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: boson-ndc-config
          mountPath: /root/boson-ndc/conf/ndc.yaml
          subPath: ndc.yaml
        livenessProbe:
          httpGet:
            path: /healthz
            port: 56080
          periodSeconds: 30
          initialDelaySeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 56080
          periodSeconds: 10
          initialDelaySeconds: 10
      volumes:
      - name: boson-ndc-config
        configMap:
          defaultMode: 420
          name: boson-ndc-config
---
# Source: boson-ndc/templates/ndc-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-ndc-service-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-ndc-1.16.0
    app.kubernetes.io/name: boson-ndc
    app.kubernetes.io/instance: boson-ndc
    app.kubernetes.io/version: 1.2.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ndc
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: network-service.cn-tj-01.sensecoreapi.cn
    http:
      paths:
      - path: /network/boson-ndc(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: boson-ndc-service
            port:
              number: 56080
  tls:
  - hosts:
    - network-service.cn-tj-01.sensecoreapi.cn
    secretName: tls-cntj01-api
---
# Source: boson-ndc/templates/ndc-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-ndc-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-ndc-1.16.0
    app.kubernetes.io/name: boson-ndc
    app.kubernetes.io/instance: boson-ndc
    app.kubernetes.io/version: 1.2.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ndc
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-ndc-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
