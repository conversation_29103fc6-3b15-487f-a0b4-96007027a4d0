---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-sh-01e
    pg:
      host: ************
      port: "61831"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ************/24
        gateway: ************
        ips:
      slbs:
        cidr: ************/23
        gateway: ************
        ips:
      eips:
        cidr: xxx.xxx.xxx.xxx/xx
        gateway: xxx.xxx.xxx.x
        ips:
        sku: xxx
      cidr_pools:
        ib:
        vlan:
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1532
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1533
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1534
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1535
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1536
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1537
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1538
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1539
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1540
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1541
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1542
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1543
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1544
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1545
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1546
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1547
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1548
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1549
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1550
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1551
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1552
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1553
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1554
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1555
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1556
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1557
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1558
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1559
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1560
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1561
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1562
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: **********/20
          gateway: **********
          zone_prp: cn-sh-01e
          vni: 1563
          scope: TRAINING
          reserved: **********..**********,*************..*************
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1532
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1533
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1534
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1535
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1536
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1537
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1538
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1539
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1540
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1541
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1542
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1543
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1544
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1545
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1546
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1547
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1548
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1549
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1550
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1551
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1552
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1553
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1554
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1555
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1556
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1557
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1558
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1559
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1560
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1561
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1562
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sh-01e
          vni: 1563
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-sh-01e
      vpc_default_region: cn-sh-01e
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
