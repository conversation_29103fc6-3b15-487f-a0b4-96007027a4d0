---
# Source: boson-ib-manager/templates/ib-manager-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-ibm-ib-cluster-02
  namespace: plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-cm.yaml
apiVersion: v1
data:
  boson-ib-manager.yaml: |-
    boson_default:
      log_level: info
      grpc_port: 53902
      http_port: 53802
      metrics_port: 53302
      env: prod-cn-sh-01e
      region: cn-sh-01
      az: cn-sh-01e
      prp: cn-sh-01e-prp01
      slow_ops_threshold_milli_seconds_config:
        http_request_rt: 1000
        k8s_request_rt: 1000
      ibClusterType: opensm
      enableSharp: true
      SharpCheckFileList: /var/log/opensm-smdb.dump
      SharpInitialDelaySeconds: 5
      SharpCheckFileListRetryIntervalSecs: 3
      SharpCheckFileListRetryTimes: 1
    opensm:
      smConfigFile: /etc/opensm/opensm.conf
      pkConfigFile: /etc/opensm/partitions.conf
      k8sNs: plat-boson-service
      leaseLockName: "boson-ib-manager-ib-cluster-02"
      configMapName: "partitions-ib-cluster-02.conf"
      configMapKeyName: partitions.conf
      pNamePrefix: "pk_"
      enableOpensmLog: true
      opensmLogRatelimitEnabled: true
      opensmLogRateLimitMinute: 1000
kind: ConfigMap
metadata:
  name: boson-ibm-ib-cluster-02-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-ib-manager/templates/ib-manager-cm.yaml
apiVersion: v1
data:
  opensm.conf: |-
    m_key 0xf8b5fb2f6e93386f
    m_key_protection_level 1
    m_key_lookup true
    sm_key 0xf8b5fb2f6e93386f
    allow_both_pkeys true
    sm_priority 15
    master_sm_priority 15
    routing_engine ar_updn
    root_guid_file /etc/opensm/boson/root_guid.conf
    scatter_ports 8
    use_ucast_cache TRUE

    # Number of threads to be used for parallel minhop/updn/dor/dfp/AR calculations.
    # If 0, the number of threads will be equal to the number of processors.
    # Default: 0x1，Recommended value is 0x0 (all cores)
    routing_threads_num 0

    # AR enablement in the switches
    # 0 – AR disabled
    # 1 – enable AR
    # 2 – enable AR with notifications
    # 3 – Default, auto (SM will apply supported mode automatically based on routing engine capabilities)
    ar_mode 3

    # SHIELD mode options
    # 0 – SHIELD disabled
    # 1 – enable SHIELD
    # 2 – enable SHIELD with notifications
    # 3 - Default, auto (SHIELD support is determined by the routing engine capabilities)
    shield_mode 3

    # Bitmask of service levels (SLs) on which the AR is enabled. Least Significant Bit indicates SL0, etc. | Default: 0xFFFF
    ar_sl_mask 0xFFFF

    # none – no advanced routing
    # ar_lag – port groups are created out of "parallel" links. Links that connect the same pair of switches.
    # ar_tree – all the ports with minimal hops to destination are in the same group. Must run together with UPDN/FTREE routing engines.
    # auto – Default, the AR engine is selected based on routing engine. Works for ar_updn, ar_ftree, ar_torus, ar_dor engines.
    adv_routing_engine auto

    # AR Transport mask - indicates which transport types are enabled
    # Bit 0 = UD; Bit 1 = RC; Bit 2 = UC; Bit 3 = DCT; Bits 4-7 are reserved; Default: 0xA
    ar_transport_mask 0xA

    # Enable adaptive routing only for devices supporting packet reordering.
    # TRUE: Default, Set only static ARLFT entries for LIDs of the HCAs which do not support packet reordering.
    # FALSE: ARLFT remains as determined by the routing engine.
    enable_ar_by_device_cap TRUE

kind: ConfigMap
metadata:
  name: boson-ibm-ib-cluster-02-opensm-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-ib-manager/templates/ib-manager-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-ibm-ib-cluster-02
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-ib-manager/templates/ib-manager-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-ibm-ib-cluster-02
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-ibm-ib-cluster-02
subjects:
- kind: ServiceAccount
  name: boson-ibm-ib-cluster-02
  namespace: plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-ibm-ib-cluster-02-service
  namespace: plat-boson-service
  labels:
    app-name: boson-ibm-ib-cluster-02-service
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-ibm-ib-cluster-02-service
  ports:
  - name: http
    port: 53802
    protocol: TCP
  - name: grpc
    port: 53902
    protocol: TCP
  - name: metrics
    port: 53302
    protocol: TCP
---
# Source: boson-ib-manager/templates/ib-manager-dpl.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: boson-ibm-ib-cluster-02
  namespace: plat-boson-service
  labels:
    name: boson-ibm-ib-cluster-02
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
spec:
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 0%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app-name: boson-ibm-ib-cluster-02-service
  replicas: 2
  template:
    metadata:
      labels:
        app-name: boson-ibm-ib-cluster-02-service
    spec:
      priorityClassName: system-cluster-critical
      hostNetwork: true
      serviceAccount: boson-ibm-ib-cluster-02
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app-name
                operator: In
                values:
                - boson-ibm-ib-cluster-02-service
            topologyKey: kubernetes.io/hostname
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: "diamond.sensetime.com/belong-resource-training"
                operator: In
                values:
                - "ib-cluster-02"
              - key: "diamond.sensetime.com/role-infra-vpc-ibm"
                operator: In
                values:
                - "enabled"
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      - key: diamond.sensetime.com/role-infra-vpc-ibm
        effect: NoExecute
        operator: Equal
        value: "enabled"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-ibm-ib-cluster-02
        image: "registry.sensetime.com/sensecore-boson/boson-ib-manager:v1.17.0-12-g7c2c15b-20241017162744-ndr"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/dumb-init
        - /boson/boson-ib-manager
        securityContext:
          privileged: true
        ports:
        - name: http
          containerPort: 53802
          protocol: TCP
        - name: grpc
          containerPort: 53902
          protocol: TCP
        - name: metrics
          containerPort: 53302
          protocol: TCP
        resources:
          limits:
            cpu: 16
            memory: 32Gi
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: BOSON_IB_MANAGER_VAR_NAME
          value: "boson-ib-manager"
        - name: BOSON_IB_MANAGER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_IB_MANAGER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_IB_MANAGER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_IB_MANAGER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: BOSON_OPENSM_CONFIG_FILE_PATH
          value: "/boson/config/opensm/"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 53802
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 30
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          tcpSocket:
            port: 53902
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: config
          mountPath: /boson/boson-ib-manager.yaml
          subPath: path/to/boson-ib-manager.yaml
        - name: opensm-config
          mountPath: /boson/config/opensm/
        - mountPath: /etc/opensm/boson/
          name: root-guid-config
        - name: localtime-volume
          mountPath: /etc/localtime
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: boson-ibm-ib-cluster-02-config
          items:
          - key: boson-ib-manager.yaml
            path: path/to/boson-ib-manager.yaml
      - name: opensm-config
        configMap:
          name: boson-ibm-ib-cluster-02-opensm-config
      - configMap:
          name: boson-ibm-ib-cluster-02-root-guid-config
        name: root-guid-config
      - configMap:
        name: localtime-volume
        hostPath:
          path: /etc/localtime
---
# Source: boson-ib-manager/templates/ib-manager-grpc-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-ibm-ib-cluster-02-grpc-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "GRPC"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
  labels:
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: ib-cluster-02-network-internal.cn-sh-01e.sensecoreapi.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: boson-ibm-ib-cluster-02-service
            port:
              number: 53902
  tls:
  - hosts:
    - ib-cluster-02-network-internal.cn-sh-01e.sensecoreapi.cn
    secretName: tls-cnsh01e-api
---
# Source: boson-ib-manager/templates/ib-manager-ing.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: boson-ibm-ib-cluster-02-ingress
  namespace: plat-boson-service
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/force-ssl-redirect: "False"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "False"
    nginx.ingress.kubernetes.io/proxy-body-size: 1M
    nginx.ingress.kubernetes.io/proxy-read-timeout: "120"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "120"
  labels:
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
spec:
  rules:
  - host: ib-cluster-02-network-internal.cn-sh-01e.sensecoreapi.cn
    http:
      paths:
      - path: /network/boson-ibm-ib-cluster-02
        pathType: Prefix
        backend:
          service:
            name: boson-ibm-ib-cluster-02-service
            port:
              number: 53802
  tls:
  - hosts:
    - ib-cluster-02-network-internal.cn-sh-01e.sensecoreapi.cn
    secretName: tls-cnsh01e-api
---
# Source: boson-ib-manager/templates/ib-manager-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-ibm-ib-cluster-02-disallow-capabilities
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-capabilities
    ruleNames:
    - adding-capabilities
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-ibm-ib-cluster-02-*
        namespaces:
        - plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-ibm-ib-cluster-02-host-namespaces
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-ibm-ib-cluster-02-*
        namespaces:
        - plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-ibm-ib-cluster-02-host-path
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-ibm-ib-cluster-02-*
        namespaces:
        - plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-ibm-ib-cluster-02-host-ports
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-ports
    ruleNames:
    - host-ports-none
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-ibm-ib-cluster-02-*
        namespaces:
        - plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-ibm-ib-cluster-02-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-ibm-ib-cluster-02-*
        namespaces:
        - plat-boson-service
---
# Source: boson-ib-manager/templates/ib-manager-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-ibm-ib-cluster-02-servicemonitor
  namespace: plat-boson-service
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-ib-manager-1.22.0
    app.kubernetes.io/name: boson-ib-manager
    app.kubernetes.io/instance: boson-ib-manager-ib-cluster-02
    app.kubernetes.io/version: 1.22.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-ib-manager
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-ibm-ib-cluster-02-service
  namespaceSelector:
    matchNames:
    - plat-boson-service
  endpoints:
  - port: metrics
    interval: 30s
    honorLabels: true
