---
# Source: boson-node-agent/templates/boson-node-agent-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-node-agent
  namespace: plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-cm.yaml
apiVersion: v1
data:
  daemon.yaml: |-
    region: cn-sh-01
    az: cn-sh-01e
    logLevel: info
    dgwNS: plat-boson-infra
    # NOTE: port used by http and metrics
    httpPort: 55800
    grpcPort: 55900
    dryRun: false
    enablePprof: true
    replayDir: /boson/replay-dir

    dataIpCidr: **********/16
    dgwBrName: br-data

    dgwNodeConf:
      scriptVersion: |-
        # template 必须execute，所以将预期的版本放在这里
        SCRIPT_VERSION=v0.0.1
      addOrUpdateDgwForThisVpc: |-
        #!/bin/bash
        # --encode:utf-8
        # NOTE: 如果修改脚本，请确保是可重入的，并且因为有版本，需要向后兼容
        set -x
        # 这里放置持久化文件中，实际持久化的版本，如果和预期的版本不一致，会触发重新刷脚本操作
        SCRIPT_VERSION=v0.0.1
        # 1) 检查没有端口则添加
        { ovs-vsctl list-ports {{.OvsBrName}} | grep -q {{.LportName}}; } || \
        ovs-vsctl add-port {{.OvsBrName}} {{.LportName}} -- set interface {{.LportName}} type=internal external_ids:iface-id={{.LportOvnName}}
        # 如果这里还没有，则说明ovs不正常，直接异常退出，不继续执行
        { ovs-vsctl list-ports {{.OvsBrName}} | grep -q {{.LportName}}; } || exit 1

        # 2) 添加ns
        { ip netns | grep -q {{.DgwNs }}; } || ip netns add {{.DgwNs}}
        # 如果到这里还没有ns，则说明os的os添加异常，直接异常退出，不继续执行
        { ip netns | grep -q {{.DgwNs }}; } || exit 1

        # 3) 添加lp
        { ip a show {{.LportName }}; } && ip link set {{.LportName}} netns {{.DgwNs}}
        # 检查lp是否真的加入到了netns里面，如果没有加入，则异常
        { ip netns exec {{.DgwNs}} ip a show {{.LportName }}; } || exit 1
        ip netns exec {{.DgwNs}} ip link set {{.LportName}} address {{.LportMac}}
        ip netns exec {{.DgwNs}} ip link set dev {{.LportName}} up
        ip netns exec {{.DgwNs}} ip link set dev lo up
        ip netns exec {{.DgwNs}} ip addr add {{.LportIp}} dev {{.LportName}}
        # 如果这里lp没有ip，则数据链路不通，直接异常退出，不继续执行
        { ip netns exec {{.DgwNs}} ip a show {{.LportName}} | grep -q {{.LportIp}} ;} || exit 1

        # 4) 添加net1
        { ip a show {{.Net1Veth }} && ip a show {{.Net1 }}; } || ip link add {{.Net1Veth}} type veth peer name {{.Net1}}
        ip link set {{.Net1Veth}} up
        ip link set {{.DgwBrName}} up
        # set master only when port's master is not
        { ip a show master {{.DgwBrName}} | grep -q {{.Net1Veth}}; } || ip link set {{.Net1Veth}} master {{.DgwBrName}}
        # 检查net1veth是否已经加到br上面，如果没有则认为异常
        { ip a show master {{.DgwBrName}} | grep -q {{.Net1Veth}}; } || exit 1
        { ip netns exec {{.DgwNs}} ip a | grep {{.Net1}}; } || ip link set {{.Net1}} netns {{.DgwNs}}
        # 检查是否已经将ne1的ns设置为租户的ns，如果没有则认为异常
        { ip netns exec {{.DgwNs}} ip a | grep {{.Net1}}; } || exit 1
        ip netns exec {{.DgwNs}} ip link set {{.Net1}} up
        ip netns exec {{.DgwNs}} ip link set lo up
        ip netns exec {{.DgwNs}} ip addr add dev {{.Net1}} {{.DgwExternalIp}}
        # 如果这里net1的ip配置不对，则数据链路不通，直接异常退出，不继续执行
        { ip netns exec {{.DgwNs}} ip a show {{.Net1}} | grep -q {{.DgwExternalIp}}; } || exit 1

        # 5) 添加路由
        ip netns exec {{.DgwNs}} ip route add default via {{.DefaultGwInNs}}
        # 如果这里没有路由，直接异常退出，不继续执行
        { ip netns exec {{.DgwNs}} ip route show | grep -q {{.DefaultGwInNs}}; } || exit 1
        if [ "{{.LportGw}}" != "" ]; then
          # 增加策略路由: 从net1进来的查询table 100
          { ip netns exec {{.DgwNs}} ip rule show from all iif {{.Net1}} lookup 100 | grep -q {{.Net1}}; } || \
          { ip netns exec {{.DgwNs}} ip rule add from all iif {{.Net1}} lookup 100; }
          # table 100 增加直连路由: 到DgwExternalCidr的走net1
          { ip netns exec {{.DgwNs}} ip route replace {{.DgwExternalCidr}} dev {{.Net1}} table 100; }
          { ip netns exec {{.DgwNs}} ip route list {{.DgwExternalCidr}} dev {{.Net1}} table 100 | grep -q {{.DgwExternalCidr}}; } || exit 1
          # table 100 增加直连路由: 到容器网络的走lp
          { ip netns exec {{.DgwNs}} ip route replace {{.LportCidr}} dev {{.LportName}} table 100; }
          { ip netns exec {{.DgwNs}} ip route list {{.LportCidr}} dev {{.LportName}} table 100 | grep -q {{.LportCidr}}; } || exit 1
          # table 100 增加默认路由: 默认走容器网关
          { ip netns exec {{.DgwNs}} ip route replace default via {{.LportGw}} table 100; }
          { ip netns exec {{.DgwNs}} ip route list default table 100 | grep -q {{.LportGw}}; } || exit 1
        fi

        # 6) 添加nat规则
        # 没有匹配源ip，所有从lp进来的所有流量都会做snat
        if [ "{{.LportGw}}" != "" ]; then
          { ip netns exec {{.DgwNs}} iptables -t nat -C POSTROUTING -m comment --comment "{{.Nat1Comment}}" -o {{.Net1}} -j MASQUERADE; } ||  \
          ip netns exec {{.DgwNs}} iptables -t nat -I POSTROUTING -m comment --comment "{{.Nat1Comment}}" -o {{.Net1}} -j MASQUERADE
          # 如果没有 iptables 规则，则异常退出
          { ip netns exec {{.DgwNs}} iptables -t nat -C POSTROUTING -m comment --comment "{{.Nat1Comment}}" -o {{.Net1}} -j MASQUERADE; } || exit 1
        else
          { ip netns exec {{.DgwNs}} iptables -t nat -C POSTROUTING -m comment --comment "{{.Nat1Comment}}" -s {{.LportCidr}} -o {{.Net1}} -j MASQUERADE; } ||  \
          ip netns exec {{.DgwNs}} iptables -t nat -I POSTROUTING -m comment --comment "{{.Nat1Comment}}" -s {{.LportCidr}} -o {{.Net1}} -j MASQUERADE
          # 如果没有 iptables 规则，则异常退出
          { ip netns exec {{.DgwNs}} iptables -t nat -C POSTROUTING -m comment --comment "{{.Nat1Comment}}" -s {{.LportCidr}} -o {{.Net1}} -j MASQUERADE; } || exit 1
        fi
      deleteDgwForThisVPC: |-
        #!/bin/bash
        set -x
        # 1) 删除netns
        { ip netns | grep -q {{.DgwNs}}; } && ip netns del {{.DgwNs}}
        # 如果还有ns，则说明os的处理有问题，删除失败
        { ip netns | grep -q {{.DgwNs}}; } && exit 1
        # 如果net以及net-veth因为创建错误在ns外面，则删除ns无法保证环境清理，会导致下次创建有资源残留出错，check一下手动删除
        if [ "{{.Net1}}" != "" ]; then
          { ip a show {{.Net1}}; } && ip link del {{.Net1}};
          { ip a show {{.Net1}}; } && exit 1
        fi
        if [ "{{.Net1Veth}}" != "" ]; then
          { ip a show {{.Net1Veth}}; } && ip link del {{.Net1Veth}};
          { ip a show {{.Net1Veth}}; } && exit 1
        fi

        # 2) 删除ovs中的lp
        # 如果lp因为创建异常在ns外面，则删除ns无法删除lp，check一下手动删除
        { ip a show {{.LportName}}; } && ip link del {{.LportName}};
        { ip a show {{.LportName}}; } && exit 1
        { ovs-vsctl list-ports {{.OvsBrName}} | grep -q {{.LportName}}; } && ovs-vsctl del-port {{.OvsBrName}} {{.LportName}}
        # 如果还能找到lp，则说明前面的没有删除，则异常退出
        { ovs-vsctl list-ports {{.OvsBrName}} | grep -q {{.LportName}}; } && exit 1
        # 如果上面的执行失败，则说明已经找不到lp，则正常退出
        exit 0

    # qos config
    rate_reduce_monitor_period: 20
    rpg_ai_rate: 100
    rpg_byte_reset: 16384
    rpg_min_dec_fac: 1
    rpg_time_reset: 50

    metrics:
      updateInterval: 10
      pushJobName: boson-qos-metrics
      pushgateway: https://tk-pushgateway.cn-sh-01.sensecore.tech/
      sysPath: /sys
      procPath: /proc
      rootfsPath: /
      dataNics:
        # bond1 for compute node
        # bond2 for storage node
        bondName: bond1

    setQosForDataNic: |-
      #!/bin/bash
      set -x

      PF=$(ibdev2netdev | grep "{{.DataNic}}\>" | awk '{print $1}' | head -n 1)

      # .IFDev is slave of bond: eth20/eth21 or eth2/eth3
      mlnx_qos -i {{.IFDev}}

      cma_roce_tos -d $PF -t 106
      # config pfc, prio buffer, and enable dscp
      mlnx_qos -i {{.IFDev}} --pfc 0,0,0,1,0,0,0,0 --trust dscp --prio2buffer 0,0,0,1,0,0,0,0

      ## config traffic class
      mlnx_qos -i {{.IFDev}} --prio_tc 0,1,2,3,4,5,6,7

      ## config transmission algorithm, minimal guaranteed %BW for ets
      mlnx_qos -i {{.IFDev}} --tsa ets,ets,ets,ets,ets,ets,strict,strict --tcbw 20,0,30,50,0,0,0,0

      ## config buffer size
      mlnx_qos -i {{.IFDev}} --buffer_size 130944,130944,0,0,0,0,0,0

      ## config ratelimit
      mlnx_qos -i {{.IFDev}} --ratelimit 0,0,0,0,0,0,6,6

      {
          ## configure all 64 dscp with proper priority
          a=0
          for prio in `seq 0 7`
          do
              for dscp in `seq 0 7`; do
                dscp=$((dscp + a))
                mlnx_qos -i {{.IFDev}} --dscp2prio set,$dscp,$prio
              done
              a=$((a + 8))
          done
      }

      echo 48 >/sys/class/net/{{.IFDev}}/ecn/roce_np/cnp_dscp
      echo 3 >/sys/class/net/{{.IFDev}}/ecn/roce_np/cnp_802p_prio
      echo 1 >/sys/class/net/{{.IFDev}}/ecn/roce_np/enable/3
      echo 1 >/sys/class/net/{{.IFDev}}/ecn/roce_rp/enable/3

      echo {{.Rrmp}} > /sys/class/net/{{.IFDev}}/ecn/roce_rp/rate_reduce_monitor_period
      echo {{.Rar}} > /sys/class/net/{{.IFDev}}/ecn/roce_rp/rpg_ai_rate
      echo {{.Rbr}} > /sys/class/net/{{.IFDev}}/ecn/roce_rp/rpg_byte_reset
      echo {{.Rmdf}} > /sys/class/net/{{.IFDev}}/ecn/roce_rp/rpg_min_dec_fac
      echo {{.Rtr}} > /sys/class/net/{{.IFDev}}/ecn/roce_rp/rpg_time_reset

      # print
      mlnx_qos -i {{.IFDev}}

kind: ConfigMap
metadata:
  name: boson-node-agent-config
  namespace: plat-boson-infra
  labels:
    helm.sh/chart: boson-node-agent-1.16.0
    app.kubernetes.io/name: boson-node-agent
    app.kubernetes.io/instance: boson-node-agent
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-node-agent
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-node-agent/templates/boson-node-agent-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-node-agent
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-node-agent/templates/boson-node-agent-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-node-agent
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-node-agent
subjects:
- kind: ServiceAccount
  name: boson-node-agent
  namespace: plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: boson-node-agent-service
  namespace: plat-boson-infra
  labels:
    app-name: boson-node-agent-service
    helm.sh/chart: boson-node-agent-1.16.0
    app.kubernetes.io/name: boson-node-agent
    app.kubernetes.io/instance: boson-node-agent
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-node-agent
    app.kubernetes.io/part-of: BosonService
spec:
  type: ClusterIP
  selector:
    app-name: boson-node-agent-service
  ports:
  - name: http
    port: 55800
    protocol: TCP
  - name: grpc
    port: 55900
    protocol: TCP
---
# Source: boson-node-agent/templates/boson-node-agent-ds.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: boson-node-agent
  namespace: plat-boson-infra
  labels:
    name: boson-node-agent
    helm.sh/chart: boson-node-agent-1.16.0
    app.kubernetes.io/name: boson-node-agent
    app.kubernetes.io/instance: boson-node-agent
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-node-agent
    app.kubernetes.io/part-of: BosonService
spec:
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 5
    type: RollingUpdate
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app-name: boson-node-agent-service
  template:
    metadata:
      labels:
        app-name: boson-node-agent-service
    spec:
      hostNetwork: true
      hostPID: true
      hostIPC: true
      serviceAccountName: boson-node-agent
      priorityClassName: system-cluster-critical
      affinity:
      tolerations: #设置容忍性
      - operator: Exists
        effect: NoSchedule
      - operator: Exists
        effect: NoExecute
      - operator: Exists
        key: "CriticalAddonsOnly"
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Always
      containers:
      - name: boson-node-agent
        image: "registry.sensetime.com/sensecore-boson/boson-node-agent:v1.19.0-11-g796028b-**************"
        imagePullPolicy: IfNotPresent
        command:
        - /boson/boson-node-agent-daemon
        - -c
        - /boson/etc/daemon.yaml
        securityContext:
          runAsUser: 0
          privileged: true
        ports:
        - name: http
          containerPort: 55800
          protocol: TCP
        - name: grpc
          containerPort: 55900
          protocol: TCP
        resources:
          limits:
            cpu: "1"
            memory: 1Gi
          requests:
            cpu: 50m
            memory: 100Mi
        env:
        - name: KUBE_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_NODE_AGENT_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_NODE_AGENT_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_NODE_AGENT_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        livenessProbe:
          httpGet:
            path: /healthz
            port: 55800
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /readyz
            port: 55800
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        volumeMounts:
        - name: openvswitch
          mountPath: /var/run/openvswitch
        - name: boson
          mountPath: /var/run/boson
        - name: replay-dir
          mountPath: /boson/replay-dir
        - name: daemon-config
          mountPath: /boson/etc/
        - mountPath: /sys
          name: host-sys
        - mountPath: /etc/localtime
          name: localtime
        - name: host-run-netns
          mountPath: /run/netns
          mountPropagation: Bidirectional
      volumes:
      - name: openvswitch
        hostPath:
          path: /var/run/openvswitch
      - name: boson
        hostPath:
          path: /var/run/boson
      - name: host-run-netns
        hostPath:
          path: /run/netns
      - hostPath:
          path: /sys
        name: host-sys
      - hostPath:
          path: /etc/localtime
        name: localtime
      - name: replay-dir
        hostPath:
          path: /var/lib/boson/boson-node-agent/replay-dir
      - name: daemon-config
        configMap:
          name: boson-node-agent-config
---
# Source: boson-node-agent/templates/boson-node-agent-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-node-agent-host-namespaces
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-namespaces
    ruleNames:
    - host-namespaces
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-node-agent-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-node-agent-host-path
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-path
    ruleNames:
    - host-path
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-node-agent-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-node-agent-host-ports
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-host-ports
    ruleNames:
    - host-ports-none
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-node-agent-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-polex.yaml
apiVersion: kyverno.io/v2alpha1
kind: PolicyException
metadata:
  name: boson-node-agent-privileged-containers
  namespace: plat-diamond-pss
spec:
  exceptions:
  - policyName: disallow-privileged-containers
    ruleNames:
    - privileged-containers
  match:
    any:
    - resources:
        kinds:
        - Pod
        names:
        - boson-node-agent-*
        namespaces:
        - plat-boson-infra
---
# Source: boson-node-agent/templates/boson-node-agent-sm.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: boson-node-agent-servicemonitor
  namespace: plat-boson-infra
  labels:
    k8s-app: http
    prometheus: prometheus
    helm.sh/chart: boson-node-agent-1.16.0
    app.kubernetes.io/name: boson-node-agent
    app.kubernetes.io/instance: boson-node-agent
    app.kubernetes.io/version: 1.16.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-node-agent
    app.kubernetes.io/part-of: BosonService
spec:
  jobLabel: k8s-app
  selector:
    matchLabels:
      app-name: boson-node-agent-service
  namespaceSelector:
    matchNames:
    - plat-boson-infra
  endpoints:
  - port: http
    interval: 10s
    honorLabels: true
