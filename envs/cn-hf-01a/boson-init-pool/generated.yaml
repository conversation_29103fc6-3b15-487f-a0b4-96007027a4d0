---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-hf-01a
    pg:
      host: *************
      port: "35108"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ************/24
        gateway: ************
        ips:
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "10.115.101.48"
        - "10.115.101.49"
        - "10.115.101.50"
        - "10.115.101.51"
        - "10.115.101.52"
        - "10.115.101.53"
        - "10.115.101.54"
        - "10.115.101.55"
        - "10.115.101.56"
        - "10.115.101.57"
        - "10.115.101.58"
        - "10.115.101.59"
        - "10.115.101.60"
        - "10.115.101.61"
        - "10.115.101.62"
        - "10.115.101.63"
        - "10.115.101.64"
        - "10.115.101.65"
        - "10.115.101.66"
        - "10.115.101.67"
        - "10.115.101.68"
        - "10.115.101.69"
        - "10.115.101.70"
        - "10.115.101.71"
        - "10.115.101.72"
        - "10.115.101.73"
        - "10.115.101.74"
        - "10.115.101.75"
        - "10.115.101.76"
        - "10.115.101.77"
        - "10.115.101.78"
        - "10.115.101.79"
        - "10.115.101.80"
        - "10.115.101.81"
        - "10.115.101.82"
        - "10.115.101.83"
        - "10.115.101.84"
        - "10.115.101.85"
        - "10.115.101.86"
        - "10.115.101.87"
        - "10.115.101.88"
        - "10.115.101.89"
        - "10.115.101.90"
        - "10.115.101.91"
        - "10.115.101.92"
        - "10.115.101.93"
        - "10.115.101.94"
        - "10.115.101.95"
        - "10.115.101.96"
        - "10.115.101.97"
        - "10.115.101.98"
        - "10.115.101.99"
        - "************00"
        - "************01"
        - "************02"
        - "************03"
        - "************04"
        - "************05"
        - "************06"
        - "************07"
        - "************08"
        - "************09"
        - "************10"
        - "************11"
        - "************12"
        - "************13"
        - "************14"
        - "************15"
        - "************16"
        - "************17"
        - "************18"
        - "************19"
        - "************20"
        - "************21"
        - "************22"
        - "************23"
        - "************24"
        - "************25"
        - "************26"
        - "************27"
        - "************28"
        - "************29"
        - "************30"
        - "************31"
        - "************32"
        - "************33"
        - "************34"
        - "************35"
        - "************36"
        - "************37"
        - "************38"
        - "************39"
        - "************40"
        - "************41"
        - "************42"
        - "************43"
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        - "************55"
        - "************56"
        - "************57"
        - "************58"
        - "************59"
        - "************60"
        - "************61"
        - "************62"
        - "************63"
        - "************64"
        - "************65"
        - "************66"
        - "************67"
        - "************68"
        - "************69"
        - "************70"
        - "************71"
        - "************72"
        - "************73"
        - "************74"
        - "************75"
        - "************76"
        - "************77"
        - "************78"
        - "************79"
        - "************80"
        - "************81"
        - "************82"
        - "************83"
        - "************84"
        - "************85"
        - "************86"
        - "************87"
        - "************88"
        - "************89"
        - "************90"
        - "************91"
        - "************92"
        - "************93"
        - "************94"
        - "************95"
        - "************96"
        - "************97"
        - "************98"
        - "************99"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
      slbs:
        cidr: 0.0.0.0/24
        gateway: *******
        ips:
      eips:
        cidr: ************28/26
        gateway: *************
        ips:
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        - "************55"
        - "************56"
        - "************57"
        - "************58"
        - "************59"
        - "************60"
        - "************61"
        - "************62"
        - "************63"
        - "************64"
        - "************65"
        - "************66"
        - "************67"
        - "************68"
        - "************69"
        - "************70"
        - "************71"
        - "************72"
        - "************73"
        - "************74"
        - "************75"
        - "************76"
        - "************77"
        - "************78"
        - "************79"
        - "************80"
        - "************81"
        - "************82"
        - "************83"
        - "************84"
        - "************85"
        - "************86"
        - "************87"
        - "************88"
        - "************89"
        - "************90"
        - "************91"
        sku: CHINA_MOBILE
      cidr_pools:
        ib:
        - cidr: 10.110.128.0/26
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 2500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.58..10.110.128.62
        - cidr: 10.110.128.64/26
          gateway: 10.110.128.65
          zone_prp: cn-hf-01a
          vni: 2501
          scope: TRAINING
          reserved: 10.110.128.65..10.110.128.73,10.110.128.122..10.110.128.126
        - cidr: 10.110.128.128/26
          gateway: 10.110.128.129
          zone_prp: cn-hf-01a
          vni: 2502
          scope: TRAINING
          reserved: 10.110.128.129..10.110.128.137,10.110.128.186..10.110.128.190
        - cidr: 10.110.128.192/26
          gateway: 10.110.128.193
          zone_prp: cn-hf-01a
          vni: 2503
          scope: TRAINING
          reserved: 10.110.128.193..10.110.128.201,10.110.128.250..10.110.128.254
        - cidr: 10.110.129.0/26
          gateway: 10.110.129.1
          zone_prp: cn-hf-01a
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-hf-01a
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-hf-01a
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-hf-01a
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-hf-01a
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-hf-01a
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-hf-01a
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-hf-01a
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-hf-01a
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-hf-01a
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-hf-01a
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-hf-01a
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-hf-01a
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-hf-01a
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-hf-01a
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-hf-01a
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-hf-01a
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-hf-01a
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-hf-01a
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-hf-01a
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-hf-01a
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-hf-01a
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-hf-01a
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-hf-01a
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-hf-01a
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-hf-01a
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-hf-01a
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-hf-01a
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.120.184.0/26
          gateway: 10.120.184.1
          zone_prp: cn-hf-01a
          vni: 2500
          scope: DATA
          reserved: 10.120.184.1..10.120.184.9,10.120.184.58..10.120.184.62
        - cidr: 10.120.184.64/26
          gateway: 10.120.184.65
          zone_prp: cn-hf-01a
          vni: 2501
          scope: DATA
          reserved: 10.120.184.65..10.120.184.73,10.120.184.122..10.120.184.126
        - cidr: 10.120.184.128/26
          gateway: 10.120.184.129
          zone_prp: cn-hf-01a
          vni: 2502
          scope: DATA
          reserved: 10.120.184.129..10.120.184.137,10.120.184.186..10.120.184.190
        - cidr: 10.120.184.192/26
          gateway: 10.120.184.193
          zone_prp: cn-hf-01a
          vni: 2503
          scope: DATA
          reserved: 10.120.184.193..10.120.184.201,10.120.184.250..10.120.184.254
        - cidr: 10.120.185.0/26
          gateway: 10.120.185.1
          zone_prp: cn-hf-01a
          vni: 2504
          scope: DATA
          reserved: 10.120.185.1..10.120.185.9,10.120.185.58..10.120.185.62
        - cidr: 10.120.185.64/26
          gateway: 10.120.185.65
          zone_prp: cn-hf-01a
          vni: 2505
          scope: DATA
          reserved: 10.120.185.65..10.120.185.73,10.120.185.122..10.120.185.126
        - cidr: 10.120.185.128/26
          gateway: 10.120.185.129
          zone_prp: cn-hf-01a
          vni: 2506
          scope: DATA
          reserved: 10.120.185.129..10.120.185.137,10.120.185.186..10.120.185.190
        - cidr: 10.120.185.192/26
          gateway: 10.120.185.193
          zone_prp: cn-hf-01a
          vni: 2507
          scope: DATA
          reserved: 10.120.185.193..10.120.185.201,10.120.185.250..10.120.185.254
        - cidr: 10.120.186.0/26
          gateway: 10.120.186.1
          zone_prp: cn-hf-01a
          vni: 2508
          scope: DATA
          reserved: 10.120.186.1..10.120.186.9,10.120.186.58..10.120.186.62
        - cidr: 10.120.186.64/26
          gateway: 10.120.186.65
          zone_prp: cn-hf-01a
          vni: 2509
          scope: DATA
          reserved: 10.120.186.65..10.120.186.73,10.120.186.122..10.120.186.126
        - cidr: 10.120.186.128/26
          gateway: 10.120.186.129
          zone_prp: cn-hf-01a
          vni: 2510
          scope: DATA
          reserved: 10.120.186.129..10.120.186.137,10.120.186.186..10.120.186.190
        - cidr: 10.120.186.192/26
          gateway: 10.120.186.193
          zone_prp: cn-hf-01a
          vni: 2511
          scope: DATA
          reserved: 10.120.186.193..10.120.186.201,10.120.186.250..10.120.186.254
        - cidr: 10.120.187.0/26
          gateway: 10.120.187.1
          zone_prp: cn-hf-01a
          vni: 2512
          scope: DATA
          reserved: 10.120.187.1..10.120.187.9,10.120.187.58..10.120.187.62
        - cidr: 10.120.187.64/26
          gateway: 10.120.187.65
          zone_prp: cn-hf-01a
          vni: 2513
          scope: DATA
          reserved: 10.120.187.65..10.120.187.73,10.120.187.122..10.120.187.126
        - cidr: 10.120.187.128/26
          gateway: 10.120.187.129
          zone_prp: cn-hf-01a
          vni: 2514
          scope: DATA
          reserved: 10.120.187.129..10.120.187.137,10.120.187.186..10.120.187.190
        - cidr: 10.120.187.192/26
          gateway: 10.120.187.193
          zone_prp: cn-hf-01a
          vni: 2515
          scope: DATA
          reserved: 10.120.187.193..10.120.187.201,10.120.187.250..10.120.187.254
        - cidr: 10.120.188.0/26
          gateway: 10.120.188.1
          zone_prp: cn-hf-01a
          vni: 2516
          scope: DATA
          reserved: 10.120.188.1..10.120.188.9,10.120.188.58..10.120.188.62
        - cidr: 10.120.188.64/26
          gateway: 10.120.188.65
          zone_prp: cn-hf-01a
          vni: 2517
          scope: DATA
          reserved: 10.120.188.65..10.120.188.73,10.120.188.122..10.120.188.126
        - cidr: 10.120.188.128/26
          gateway: 10.120.188.129
          zone_prp: cn-hf-01a
          vni: 2518
          scope: DATA
          reserved: 10.120.188.129..10.120.188.137,10.120.188.186..10.120.188.190
        - cidr: 10.120.188.192/26
          gateway: 10.120.188.193
          zone_prp: cn-hf-01a
          vni: 2519
          scope: DATA
          reserved: 10.120.188.193..10.120.188.201,10.120.188.250..10.120.188.254
        - cidr: 10.120.189.0/26
          gateway: 10.120.189.1
          zone_prp: cn-hf-01a
          vni: 2520
          scope: DATA
          reserved: 10.120.189.1..10.120.189.9,10.120.189.58..10.120.189.62
        - cidr: 10.120.189.64/26
          gateway: 10.120.189.65
          zone_prp: cn-hf-01a
          vni: 2521
          scope: DATA
          reserved: 10.120.189.65..10.120.189.73,10.120.189.122..10.120.189.126
        - cidr: 10.120.189.128/26
          gateway: 10.120.189.129
          zone_prp: cn-hf-01a
          vni: 2522
          scope: DATA
          reserved: 10.120.189.129..10.120.189.137,10.120.189.186..10.120.189.190
        - cidr: 10.120.189.192/26
          gateway: 10.120.189.193
          zone_prp: cn-hf-01a
          vni: 2523
          scope: DATA
          reserved: 10.120.189.193..10.120.189.201,10.120.189.250..10.120.189.254
        - cidr: 10.120.190.0/26
          gateway: 10.120.190.1
          zone_prp: cn-hf-01a
          vni: 2524
          scope: DATA
          reserved: 10.120.190.1..10.120.190.9,10.120.190.58..10.120.190.62
        - cidr: 10.120.190.64/26
          gateway: 10.120.190.65
          zone_prp: cn-hf-01a
          vni: 2525
          scope: DATA
          reserved: 10.120.190.65..10.120.190.73,10.120.190.122..10.120.190.126
        - cidr: 10.120.190.128/26
          gateway: 10.120.190.129
          zone_prp: cn-hf-01a
          vni: 2526
          scope: DATA
          reserved: 10.120.190.129..10.120.190.137,10.120.190.186..10.120.190.190
        - cidr: 10.120.190.192/26
          gateway: 10.120.190.193
          zone_prp: cn-hf-01a
          vni: 2527
          scope: DATA
          reserved: 10.120.190.193..10.120.190.201,10.120.190.250..10.120.190.254
        - cidr: 10.120.191.0/26
          gateway: 10.120.191.1
          zone_prp: cn-hf-01a
          vni: 2528
          scope: DATA
          reserved: 10.120.191.1..10.120.191.9,10.120.191.58..10.120.191.62
        - cidr: 10.120.191.64/26
          gateway: 10.120.191.65
          zone_prp: cn-hf-01a
          vni: 2529
          scope: DATA
          reserved: 10.120.191.65..10.120.191.73,10.120.191.122..10.120.191.126
        - cidr: 10.120.191.128/26
          gateway: 10.120.191.129
          zone_prp: cn-hf-01a
          vni: 2530
          scope: DATA
          reserved: 10.120.191.129..10.120.191.137,10.120.191.186..10.120.191.190
        - cidr: 10.120.191.192/26
          gateway: 10.120.191.193
          zone_prp: cn-hf-01a
          vni: 2531
          scope: DATA
          reserved: 10.120.191.193..10.120.191.201,10.120.191.250..10.120.191.254
        - cidr: 10.120.120.0/26
          gateway: 10.120.120.1
          zone_prp: cn-hf-01a
          vni: 2500
          scope: SERVICE
          reserved: 10.120.120.1..10.120.120.9,10.120.120.58..10.120.120.62
        - cidr: 10.120.120.64/26
          gateway: 10.120.120.65
          zone_prp: cn-hf-01a
          vni: 2501
          scope: SERVICE
          reserved: 10.120.120.65..10.120.120.73,10.120.120.122..10.120.120.126
        - cidr: 10.120.120.128/26
          gateway: 10.120.120.129
          zone_prp: cn-hf-01a
          vni: 2502
          scope: SERVICE
          reserved: 10.120.120.129..10.120.120.137,10.120.120.186..10.120.120.190
        - cidr: 10.120.120.192/26
          gateway: 10.120.120.193
          zone_prp: cn-hf-01a
          vni: 2503
          scope: SERVICE
          reserved: 10.120.120.193..10.120.120.201,10.120.120.250..10.120.120.254
        - cidr: 10.120.121.0/26
          gateway: 10.120.121.1
          zone_prp: cn-hf-01a
          vni: 2504
          scope: SERVICE
          reserved: 10.120.121.1..10.120.121.9,10.120.121.58..10.120.121.62
        - cidr: 10.120.121.64/26
          gateway: 10.120.121.65
          zone_prp: cn-hf-01a
          vni: 2505
          scope: SERVICE
          reserved: 10.120.121.65..10.120.121.73,10.120.121.122..10.120.121.126
        - cidr: 10.120.121.128/26
          gateway: 10.120.121.129
          zone_prp: cn-hf-01a
          vni: 2506
          scope: SERVICE
          reserved: 10.120.121.129..10.120.121.137,10.120.121.186..10.120.121.190
        - cidr: 10.120.121.192/26
          gateway: 10.120.121.193
          zone_prp: cn-hf-01a
          vni: 2507
          scope: SERVICE
          reserved: 10.120.121.193..10.120.121.201,10.120.121.250..10.120.121.254
        - cidr: 10.120.122.0/26
          gateway: 10.120.122.1
          zone_prp: cn-hf-01a
          vni: 2508
          scope: SERVICE
          reserved: 10.120.122.1..10.120.122.9,10.120.122.58..10.120.122.62
        - cidr: 10.120.122.64/26
          gateway: 10.120.122.65
          zone_prp: cn-hf-01a
          vni: 2509
          scope: SERVICE
          reserved: 10.120.122.65..10.120.122.73,10.120.122.122..10.120.122.126
        - cidr: 10.120.122.128/26
          gateway: 10.120.122.129
          zone_prp: cn-hf-01a
          vni: 2510
          scope: SERVICE
          reserved: 10.120.122.129..10.120.122.137,10.120.122.186..10.120.122.190
        - cidr: 10.120.122.192/26
          gateway: 10.120.122.193
          zone_prp: cn-hf-01a
          vni: 2511
          scope: SERVICE
          reserved: 10.120.122.193..10.120.122.201,10.120.122.250..10.120.122.254
        - cidr: 10.120.123.0/26
          gateway: 10.120.123.1
          zone_prp: cn-hf-01a
          vni: 2512
          scope: SERVICE
          reserved: 10.120.123.1..10.120.123.9,10.120.123.58..10.120.123.62
        - cidr: 10.120.123.64/26
          gateway: 10.120.123.65
          zone_prp: cn-hf-01a
          vni: 2513
          scope: SERVICE
          reserved: 10.120.123.65..10.120.123.73,10.120.123.122..10.120.123.126
        - cidr: 10.120.123.128/26
          gateway: 10.120.123.129
          zone_prp: cn-hf-01a
          vni: 2514
          scope: SERVICE
          reserved: 10.120.123.129..10.120.123.137,10.120.123.186..10.120.123.190
        - cidr: 10.120.123.192/26
          gateway: 10.120.123.193
          zone_prp: cn-hf-01a
          vni: 2515
          scope: SERVICE
          reserved: 10.120.123.193..10.120.123.201,10.120.123.250..10.120.123.254
        - cidr: 10.120.124.0/26
          gateway: 10.120.124.1
          zone_prp: cn-hf-01a
          vni: 2516
          scope: SERVICE
          reserved: 10.120.124.1..10.120.124.9,10.120.124.58..10.120.124.62
        - cidr: 10.120.124.64/26
          gateway: 10.120.124.65
          zone_prp: cn-hf-01a
          vni: 2517
          scope: SERVICE
          reserved: 10.120.124.65..10.120.124.73,10.120.124.122..10.120.124.126
        - cidr: 10.120.124.128/26
          gateway: 10.120.124.129
          zone_prp: cn-hf-01a
          vni: 2518
          scope: SERVICE
          reserved: 10.120.124.129..10.120.124.137,10.120.124.186..10.120.124.190
        - cidr: 10.120.124.192/26
          gateway: 10.120.124.193
          zone_prp: cn-hf-01a
          vni: 2519
          scope: SERVICE
          reserved: 10.120.124.193..10.120.124.201,10.120.124.250..10.120.124.254
        - cidr: 10.120.125.0/26
          gateway: 10.120.125.1
          zone_prp: cn-hf-01a
          vni: 2520
          scope: SERVICE
          reserved: 10.120.125.1..10.120.125.9,10.120.125.58..10.120.125.62
        - cidr: 10.120.125.64/26
          gateway: 10.120.125.65
          zone_prp: cn-hf-01a
          vni: 2521
          scope: SERVICE
          reserved: 10.120.125.65..10.120.125.73,10.120.125.122..10.120.125.126
        - cidr: 10.120.125.128/26
          gateway: 10.120.125.129
          zone_prp: cn-hf-01a
          vni: 2522
          scope: SERVICE
          reserved: 10.120.125.129..10.120.125.137,10.120.125.186..10.120.125.190
        - cidr: 10.120.125.192/26
          gateway: 10.120.125.193
          zone_prp: cn-hf-01a
          vni: 2523
          scope: SERVICE
          reserved: 10.120.125.193..10.120.125.201,10.120.125.250..10.120.125.254
        - cidr: 10.120.126.0/26
          gateway: 10.120.126.1
          zone_prp: cn-hf-01a
          vni: 2524
          scope: SERVICE
          reserved: 10.120.126.1..10.120.126.9,10.120.126.58..10.120.126.62
        - cidr: 10.120.126.64/26
          gateway: 10.120.126.65
          zone_prp: cn-hf-01a
          vni: 2525
          scope: SERVICE
          reserved: 10.120.126.65..10.120.126.73,10.120.126.122..10.120.126.126
        - cidr: 10.120.126.128/26
          gateway: 10.120.126.129
          zone_prp: cn-hf-01a
          vni: 2526
          scope: SERVICE
          reserved: 10.120.126.129..10.120.126.137,10.120.126.186..10.120.126.190
        - cidr: 10.120.126.192/26
          gateway: 10.120.126.193
          zone_prp: cn-hf-01a
          vni: 2527
          scope: SERVICE
          reserved: 10.120.126.193..10.120.126.201,10.120.126.250..10.120.126.254
        - cidr: 10.120.127.0/26
          gateway: 10.120.127.1
          zone_prp: cn-hf-01a
          vni: 2528
          scope: SERVICE
          reserved: 10.120.127.1..10.120.127.9,10.120.127.58..10.120.127.62
        - cidr: 10.120.127.64/26
          gateway: 10.120.127.65
          zone_prp: cn-hf-01a
          vni: 2529
          scope: SERVICE
          reserved: 10.120.127.65..10.120.127.73,10.120.127.122..10.120.127.126
        - cidr: 10.120.127.128/26
          gateway: 10.120.127.129
          zone_prp: cn-hf-01a
          vni: 2530
          scope: SERVICE
          reserved: 10.120.127.129..10.120.127.137,10.120.127.186..10.120.127.190
        - cidr: 10.120.127.192/26
          gateway: 10.120.127.193
          zone_prp: cn-hf-01a
          vni: 2531
          scope: SERVICE
          reserved: 10.120.127.193..10.120.127.201,10.120.127.250..10.120.127.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-hf-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-hf-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-hf-01a
      vpc_default_region: cn-hf-01z
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
