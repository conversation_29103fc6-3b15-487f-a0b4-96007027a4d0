pool_settings:
  cidr_pools:
    # 所有子网的掩码位数
    prefix: 26
    # 每个子网最前的保留 IP 数，本网地址和网关地址也算在其内
    start_reserve: 10
    # 每个子网最后的保留 IP 数，网关地址也算在期内
    end_reserve: 6
    vni_base: 2500
    roce_training_vni_base: 1500
    zone_prp: cn-hf-01a
    ib:
    - scope: TRAINING
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: ************/24
    vlan:
    - scope: DATA
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: **************/24
    - scope: SERVICE
      # type: cidr_c 会根据 start 和 end 以及 prefix 生产对应的子网段
      type: cidr_c
      # 子网 C 段开始地址
      start: ************/24
      # 子网 C 段结束地址
      end: **************/24
    # 专门给容器和裸金属训练网配置的网段
    training:
    - scope: TRAINING
      # type: pf_vlan 为固定网段和prefix的容器overlay网段配置
      type: pf_vlan
      # nic_count: 4 根据配置服务器的训练网卡数配置，一般配置2/4/8三种
      nic_count: 1
    - scope: TRAINING
      # type: bms_vlan 为固定网段和prefix的容器underlay网段配置
      type: bms_vlan
      # nic_count: 4 根据配置服务器的训练网卡数配置，一般配置2/4/8三种
      nic_count: 1
  eips:
    cidr: **************/26
    gateway: *************
    sku: CHINA_MOBILE
    ips:
    - type: range
      start: **************
      end: **************
  nat_gateways:
    cidr: ************/24
    gateway: ************
    ips:
    - type: range
      start: *************
      end: **************
