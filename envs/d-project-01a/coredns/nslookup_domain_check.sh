#!/usr/bin/env bash
set -beou pipefail
cd "$(dirname "${0}")"
# shellcheck source=/dev/null
source comm_libs.sh

if kubectl get svc -n kube-system kube-dns ; then
    IFS=' ' read -r dns_service_name _  dns_servers _ _ <<< "$(kubectl get svc -n kube-system kube-dns --no-headers)"
elif kubectl get svc -n kube-system coredns ; then
    IFS=' ' read -r dns_service_name _  dns_servers _ _ <<< "$(kubectl get svc -n kube-system coredns --no-headers)"
else
    echo "FATAL: No coredns service not found in either kube-dns or coredns"
    exit 1
fi
echo "${dns_service_name}" "${dns_servers}"
# set specific dns server ip, for example ************ for cn-sh-01a,
# please uncomment and update the line below
#dns_servers="************"
#echo "Uses core dns server IP: ${dns_servers}"

for ns in $(kubectl get ns --no-headers -oname | cut -d "/" -f 2); do
    echo "====== ${ns} ======"
    while read -r svc_name svc_ip; do
        if [[ -z "${svc_name}" ]]; then
            continue
        fi
        echo -e "\e[1:m validate ${svc_name}.${ns}.svc.cluster.local \e[0m"
        domain="${svc_name}.${ns}.svc.cluster.local"
        expected_ips="${svc_ip};"
        check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"
    done <<< "$(kubectl get svc -n "${ns}" --no-headers | grep -v "No resources found" | grep -Ev "ClusterIP +None" | awk '{print $1" "$3}')"
done

echo -e "\e[1:m validate sensecore domain \e[0m"
domain="sensecore.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate public domain \e[0m"
domain="baidu.com"
expected_ips="*************;************;"
check_domain_result "${domain}" "${dns_servers}" "${expected_ips}" "skip"

echo "========================================================================"
echo "================== validate CoreDNS configured domains ================="
echo "========================================================================"

echo -e "\e[1:m validate domain kms.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="kms.d.pjlab.org.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain grafana2.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="grafana2.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain registry2.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="registry2.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain onek8s.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="onek8s.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain eye.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="eye.d.pjlab.org.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain ntp.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="ntp.d.pjlab.org.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain nexus.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="nexus.d.pjlab.org.cn"
expected_ips="*************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain compute.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="compute.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain lps.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="lps.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain network-internal.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="network-internal.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain network-oms.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="network-oms.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain network.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="network.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain network-infra.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="network-infra.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain iam.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="iam.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain iam-internal.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="iam-internal.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain finance.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="finance.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain management.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="management.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain messages.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="messages.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain operations.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="operations.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain signin.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="signin.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain opsignin.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="opsignin.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain operations-console.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="operations-console.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain console.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="console.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain sco.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="sco.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain monitor.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="monitor.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain monitor-ingestion.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="monitor-ingestion.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain osui-cms.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="osui-cms.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain osui.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="osui.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain vm.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="vm.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"

echo -e "\e[1:m validate domain starrocks.d.pjlab.org.cn resolve in hosts plugin. \e[0m"
domain="starrocks.d.pjlab.org.cn"
expected_ips="************;"
check_domain_result "${domain}" "${core_dns_servers}" "${expected_ips}" "skip"
