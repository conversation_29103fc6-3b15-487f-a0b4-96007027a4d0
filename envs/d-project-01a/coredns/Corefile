.:53 {
    errors
    health {
        lameduck 5s
    }
    ready
    kubernetes cluster.local in-addr.arpa ip6.arpa {
        pods insecure
        ttl 30
        fallthrough in-addr.arpa ip6.arpa
    }
    prometheus :9153
    hosts {
        ************* kms.d.pjlab.org.cn
        ************ grafana2.d.pjlab.org.cn
        ************ registry2.d.pjlab.org.cn
        ************ onek8s.d.pjlab.org.cn
        ************* eye.d.pjlab.org.cn
        ************* ntp.d.pjlab.org.cn
        ************* nexus.d.pjlab.org.cn
        ************ compute.d.pjlab.org.cn
        ************ lps.d.pjlab.org.cn
        ************ network-internal.d.pjlab.org.cn
        ************ network-oms.d.pjlab.org.cn
        ************ network.d.pjlab.org.cn
        ************ network-infra.d.pjlab.org.cn
        ************ iam.d.pjlab.org.cn
        ************ iam-internal.d.pjlab.org.cn
        ************ finance.d.pjlab.org.cn
        ************ management.d.pjlab.org.cn
        ************ messages.d.pjlab.org.cn
        ************ operations.d.pjlab.org.cn
        ************ signin.d.pjlab.org.cn
        ************ opsignin.d.pjlab.org.cn
        ************ operations-console.d.pjlab.org.cn
        ************ console.d.pjlab.org.cn
        ************ sco.d.pjlab.org.cn
        ************ monitor.d.pjlab.org.cn
        ************ monitor-ingestion.d.pjlab.org.cn
        ************ osui-cms.d.pjlab.org.cn
        ************ osui.d.pjlab.org.cn
        ************ vm.d.pjlab.org.cn
        ************ starrocks.d.pjlab.org.cn
        fallthrough
    }
    forward . /etc/resolv.conf {
        prefer_udp
        max_concurrent 2000
    }
    cache 30 {
        prefetch 5 30m 10%
        serve_stale 24h immediate
    }
    loop
    reload
    loadbalance
}
