---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-sh-01f
    pg:
      host: ************
      port: "61831"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ************/24
        gateway: ************
        ips:
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "10.116.197.48"
        - "10.116.197.49"
        - "10.116.197.50"
        - "10.116.197.51"
        - "10.116.197.52"
        - "10.116.197.53"
        - "10.116.197.54"
        - "10.116.197.55"
        - "10.116.197.56"
        - "10.116.197.57"
        - "10.116.197.58"
        - "10.116.197.59"
        - "10.116.197.60"
        - "10.116.197.61"
        - "10.116.197.62"
        - "10.116.197.63"
        - "10.116.197.64"
        - "10.116.197.65"
        - "10.116.197.66"
        - "10.116.197.67"
        - "10.116.197.68"
        - "10.116.197.69"
        - "10.116.197.70"
        - "10.116.197.71"
        - "10.116.197.72"
        - "10.116.197.73"
        - "10.116.197.74"
        - "10.116.197.75"
        - "10.116.197.76"
        - "10.116.197.77"
        - "10.116.197.78"
        - "10.116.197.79"
        - "10.116.197.80"
        - "10.116.197.81"
        - "10.116.197.82"
        - "10.116.197.83"
        - "10.116.197.84"
        - "10.116.197.85"
        - "10.116.197.86"
        - "10.116.197.87"
        - "10.116.197.88"
        - "10.116.197.89"
        - "10.116.197.90"
        - "10.116.197.91"
        - "10.116.197.92"
        - "10.116.197.93"
        - "10.116.197.94"
        - "10.116.197.95"
        - "10.116.197.96"
        - "10.116.197.97"
        - "10.116.197.98"
        - "10.116.197.99"
        - "************00"
        - "************01"
        - "************02"
        - "************03"
        - "************04"
        - "************05"
        - "************06"
        - "************07"
        - "************08"
        - "************09"
        - "************10"
        - "************11"
        - "************12"
        - "************13"
        - "************14"
        - "************15"
        - "************16"
        - "************17"
        - "************18"
        - "************19"
        - "************20"
        - "************21"
        - "************22"
        - "************23"
        - "************24"
        - "************25"
        - "************26"
        - "************27"
        - "************28"
        - "************29"
        - "************30"
        - "************31"
        - "************32"
        - "************33"
        - "************34"
        - "************35"
        - "************36"
        - "************37"
        - "************38"
        - "************39"
        - "************40"
        - "************41"
        - "************42"
        - "************43"
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        - "************55"
        - "************56"
        - "************57"
        - "************58"
        - "************59"
        - "************60"
        - "************61"
        - "************62"
        - "************63"
        - "************64"
        - "************65"
        - "************66"
        - "************67"
        - "************68"
        - "************69"
        - "************70"
        - "************71"
        - "************72"
        - "************73"
        - "************74"
        - "************75"
        - "************76"
        - "************77"
        - "************78"
        - "************79"
        - "************80"
        - "************81"
        - "************82"
        - "************83"
        - "************84"
        - "************85"
        - "************86"
        - "************87"
        - "************88"
        - "************89"
        - "************90"
        - "************91"
        - "************92"
        - "************93"
        - "************94"
        - "************95"
        - "************96"
        - "************97"
        - "************98"
        - "************99"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
      slbs:
        cidr: ************/23
        gateway: ************
        ips:
        - "************0"
        - "*************"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "*************"
        - "10.116.198.48"
        - "10.116.198.49"
        - "10.116.198.50"
        - "10.116.198.51"
        - "10.116.198.52"
        - "10.116.198.53"
        - "10.116.198.54"
        - "10.116.198.55"
        - "10.116.198.56"
        - "10.116.198.57"
        - "10.116.198.58"
        - "10.116.198.59"
        - "10.116.198.60"
        - "10.116.198.61"
        - "10.116.198.62"
        - "10.116.198.63"
        - "10.116.198.64"
        - "10.116.198.65"
        - "10.116.198.66"
        - "10.116.198.67"
        - "10.116.198.68"
        - "10.116.198.69"
        - "10.116.198.70"
        - "10.116.198.71"
        - "10.116.198.72"
        - "10.116.198.73"
        - "10.116.198.74"
        - "10.116.198.75"
        - "10.116.198.76"
        - "10.116.198.77"
        - "10.116.198.78"
        - "10.116.198.79"
        - "10.116.198.80"
        - "10.116.198.81"
        - "10.116.198.82"
        - "10.116.198.83"
        - "10.116.198.84"
        - "10.116.198.85"
        - "10.116.198.86"
        - "10.116.198.87"
        - "10.116.198.88"
        - "10.116.198.89"
        - "10.116.198.90"
        - "10.116.198.91"
        - "10.116.198.92"
        - "10.116.198.93"
        - "10.116.198.94"
        - "10.116.198.95"
        - "10.116.198.96"
        - "10.116.198.97"
        - "10.116.198.98"
        - "10.116.198.99"
        - "************00"
        - "************01"
        - "************02"
        - "************03"
        - "************04"
        - "************05"
        - "************06"
        - "************07"
        - "************08"
        - "************09"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "************20"
        - "************21"
        - "************22"
        - "************23"
        - "************24"
        - "************25"
        - "************26"
        - "************27"
        - "************28"
        - "************29"
        - "************30"
        - "************31"
        - "************32"
        - "************33"
        - "************34"
        - "************35"
        - "************36"
        - "************37"
        - "************38"
        - "************39"
        - "************40"
        - "************41"
        - "************42"
        - "************43"
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        - "************55"
        - "************56"
        - "************57"
        - "************58"
        - "************59"
        - "************60"
        - "************61"
        - "************62"
        - "************63"
        - "************64"
        - "************65"
        - "************66"
        - "************67"
        - "************68"
        - "************69"
        - "************70"
        - "************71"
        - "************72"
        - "************73"
        - "************74"
        - "************75"
        - "************76"
        - "************77"
        - "************78"
        - "************79"
        - "************80"
        - "************81"
        - "************82"
        - "************83"
        - "************84"
        - "************85"
        - "************86"
        - "************87"
        - "************88"
        - "************89"
        - "************90"
        - "************91"
        - "************92"
        - "************93"
        - "************94"
        - "************95"
        - "************96"
        - "************97"
        - "************98"
        - "************99"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
        - "*************0"
        - "*************1"
        - "*************2"
        - "*************3"
        - "*************4"
        - "*************5"
        - "*************6"
        - "*************7"
        - "*************8"
        - "*************9"
      eips:
        cidr: xxx.xxx.xxx.xxx/xx
        gateway: xxx.xxx.xxx.x
        ips:
        sku: xxx
      cidr_pools:
        ib:
        - cidr: ************/26
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 2500
          scope: TRAINING
          reserved: ************..************,*************..*************
        - cidr: *************/26
          gateway: *************
          zone_prp: cn-sh-01f
          vni: 2501
          scope: TRAINING
          reserved: *************..*************,************22..************26
        - cidr: ************28/26
          gateway: ************29
          zone_prp: cn-sh-01f
          vni: 2502
          scope: TRAINING
          reserved: ************29..************37,************86..************90
        - cidr: ************92/26
          gateway: ************93
          zone_prp: cn-sh-01f
          vni: 2503
          scope: TRAINING
          reserved: ************93..**************,**************..**************
        - cidr: ************/26
          gateway: 10.110.129.1
          zone_prp: cn-sh-01f
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-sh-01f
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-sh-01f
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-sh-01f
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-sh-01f
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-sh-01f
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-sh-01f
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-sh-01f
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-sh-01f
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-sh-01f
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-sh-01f
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-sh-01f
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-sh-01f
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-sh-01f
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-sh-01f
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-sh-01f
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-sh-01f
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-sh-01f
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-sh-01f
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-sh-01f
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-sh-01f
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-sh-01f
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-sh-01f
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-sh-01f
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-sh-01f
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-sh-01f
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-sh-01f
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-sh-01f
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.120.176.0/26
          gateway: 10.120.176.1
          zone_prp: cn-sh-01f
          vni: 2500
          scope: DATA
          reserved: 10.120.176.1..10.120.176.9,10.120.176.58..10.120.176.62
        - cidr: 10.120.176.64/26
          gateway: 10.120.176.65
          zone_prp: cn-sh-01f
          vni: 2501
          scope: DATA
          reserved: 10.120.176.65..10.120.176.73,10.120.176.122..10.120.176.126
        - cidr: 10.120.176.128/26
          gateway: 10.120.176.129
          zone_prp: cn-sh-01f
          vni: 2502
          scope: DATA
          reserved: 10.120.176.129..10.120.176.137,10.120.176.186..10.120.176.190
        - cidr: 10.120.176.192/26
          gateway: 10.120.176.193
          zone_prp: cn-sh-01f
          vni: 2503
          scope: DATA
          reserved: 10.120.176.193..10.120.176.201,10.120.176.250..10.120.176.254
        - cidr: 10.120.177.0/26
          gateway: 10.120.177.1
          zone_prp: cn-sh-01f
          vni: 2504
          scope: DATA
          reserved: 10.120.177.1..10.120.177.9,10.120.177.58..10.120.177.62
        - cidr: 10.120.177.64/26
          gateway: 10.120.177.65
          zone_prp: cn-sh-01f
          vni: 2505
          scope: DATA
          reserved: 10.120.177.65..10.120.177.73,10.120.177.122..10.120.177.126
        - cidr: 10.120.177.128/26
          gateway: 10.120.177.129
          zone_prp: cn-sh-01f
          vni: 2506
          scope: DATA
          reserved: 10.120.177.129..10.120.177.137,10.120.177.186..10.120.177.190
        - cidr: 10.120.177.192/26
          gateway: 10.120.177.193
          zone_prp: cn-sh-01f
          vni: 2507
          scope: DATA
          reserved: 10.120.177.193..10.120.177.201,10.120.177.250..10.120.177.254
        - cidr: 10.120.178.0/26
          gateway: 10.120.178.1
          zone_prp: cn-sh-01f
          vni: 2508
          scope: DATA
          reserved: 10.120.178.1..10.120.178.9,10.120.178.58..10.120.178.62
        - cidr: 10.120.178.64/26
          gateway: 10.120.178.65
          zone_prp: cn-sh-01f
          vni: 2509
          scope: DATA
          reserved: 10.120.178.65..10.120.178.73,10.120.178.122..10.120.178.126
        - cidr: 10.120.178.128/26
          gateway: 10.120.178.129
          zone_prp: cn-sh-01f
          vni: 2510
          scope: DATA
          reserved: 10.120.178.129..10.120.178.137,10.120.178.186..10.120.178.190
        - cidr: 10.120.178.192/26
          gateway: 10.120.178.193
          zone_prp: cn-sh-01f
          vni: 2511
          scope: DATA
          reserved: 10.120.178.193..10.120.178.201,10.120.178.250..10.120.178.254
        - cidr: 10.120.179.0/26
          gateway: 10.120.179.1
          zone_prp: cn-sh-01f
          vni: 2512
          scope: DATA
          reserved: 10.120.179.1..10.120.179.9,10.120.179.58..10.120.179.62
        - cidr: 10.120.179.64/26
          gateway: 10.120.179.65
          zone_prp: cn-sh-01f
          vni: 2513
          scope: DATA
          reserved: 10.120.179.65..10.120.179.73,10.120.179.122..10.120.179.126
        - cidr: 10.120.179.128/26
          gateway: 10.120.179.129
          zone_prp: cn-sh-01f
          vni: 2514
          scope: DATA
          reserved: 10.120.179.129..10.120.179.137,10.120.179.186..10.120.179.190
        - cidr: 10.120.179.192/26
          gateway: 10.120.179.193
          zone_prp: cn-sh-01f
          vni: 2515
          scope: DATA
          reserved: 10.120.179.193..10.120.179.201,10.120.179.250..10.120.179.254
        - cidr: 10.120.180.0/26
          gateway: 10.120.180.1
          zone_prp: cn-sh-01f
          vni: 2516
          scope: DATA
          reserved: 10.120.180.1..10.120.180.9,10.120.180.58..10.120.180.62
        - cidr: 10.120.180.64/26
          gateway: 10.120.180.65
          zone_prp: cn-sh-01f
          vni: 2517
          scope: DATA
          reserved: 10.120.180.65..10.120.180.73,10.120.180.122..10.120.180.126
        - cidr: 10.120.180.128/26
          gateway: 10.120.180.129
          zone_prp: cn-sh-01f
          vni: 2518
          scope: DATA
          reserved: 10.120.180.129..10.120.180.137,10.120.180.186..10.120.180.190
        - cidr: 10.120.180.192/26
          gateway: 10.120.180.193
          zone_prp: cn-sh-01f
          vni: 2519
          scope: DATA
          reserved: 10.120.180.193..10.120.180.201,10.120.180.250..10.120.180.254
        - cidr: 10.120.181.0/26
          gateway: 10.120.181.1
          zone_prp: cn-sh-01f
          vni: 2520
          scope: DATA
          reserved: 10.120.181.1..10.120.181.9,10.120.181.58..10.120.181.62
        - cidr: 10.120.181.64/26
          gateway: 10.120.181.65
          zone_prp: cn-sh-01f
          vni: 2521
          scope: DATA
          reserved: 10.120.181.65..10.120.181.73,10.120.181.122..10.120.181.126
        - cidr: 10.120.181.128/26
          gateway: 10.120.181.129
          zone_prp: cn-sh-01f
          vni: 2522
          scope: DATA
          reserved: 10.120.181.129..10.120.181.137,10.120.181.186..10.120.181.190
        - cidr: 10.120.181.192/26
          gateway: 10.120.181.193
          zone_prp: cn-sh-01f
          vni: 2523
          scope: DATA
          reserved: 10.120.181.193..10.120.181.201,10.120.181.250..10.120.181.254
        - cidr: 10.120.182.0/26
          gateway: 10.120.182.1
          zone_prp: cn-sh-01f
          vni: 2524
          scope: DATA
          reserved: 10.120.182.1..10.120.182.9,10.120.182.58..10.120.182.62
        - cidr: 10.120.182.64/26
          gateway: 10.120.182.65
          zone_prp: cn-sh-01f
          vni: 2525
          scope: DATA
          reserved: 10.120.182.65..10.120.182.73,10.120.182.122..10.120.182.126
        - cidr: 10.120.182.128/26
          gateway: 10.120.182.129
          zone_prp: cn-sh-01f
          vni: 2526
          scope: DATA
          reserved: 10.120.182.129..10.120.182.137,10.120.182.186..10.120.182.190
        - cidr: 10.120.182.192/26
          gateway: 10.120.182.193
          zone_prp: cn-sh-01f
          vni: 2527
          scope: DATA
          reserved: 10.120.182.193..10.120.182.201,10.120.182.250..10.120.182.254
        - cidr: 10.120.183.0/26
          gateway: 10.120.183.1
          zone_prp: cn-sh-01f
          vni: 2528
          scope: DATA
          reserved: 10.120.183.1..10.120.183.9,10.120.183.58..10.120.183.62
        - cidr: 10.120.183.64/26
          gateway: 10.120.183.65
          zone_prp: cn-sh-01f
          vni: 2529
          scope: DATA
          reserved: 10.120.183.65..10.120.183.73,10.120.183.122..10.120.183.126
        - cidr: 10.120.183.128/26
          gateway: 10.120.183.129
          zone_prp: cn-sh-01f
          vni: 2530
          scope: DATA
          reserved: 10.120.183.129..10.120.183.137,10.120.183.186..10.120.183.190
        - cidr: 10.120.183.192/26
          gateway: 10.120.183.193
          zone_prp: cn-sh-01f
          vni: 2531
          scope: DATA
          reserved: 10.120.183.193..10.120.183.201,10.120.183.250..10.120.183.254
        - cidr: 10.120.112.0/26
          gateway: 10.120.112.1
          zone_prp: cn-sh-01f
          vni: 2500
          scope: SERVICE
          reserved: 10.120.112.1..10.120.112.9,10.120.112.58..10.120.112.62
        - cidr: 10.120.112.64/26
          gateway: 10.120.112.65
          zone_prp: cn-sh-01f
          vni: 2501
          scope: SERVICE
          reserved: 10.120.112.65..10.120.112.73,10.120.112.122..10.120.112.126
        - cidr: 10.120.112.128/26
          gateway: 10.120.112.129
          zone_prp: cn-sh-01f
          vni: 2502
          scope: SERVICE
          reserved: 10.120.112.129..10.120.112.137,10.120.112.186..10.120.112.190
        - cidr: 10.120.112.192/26
          gateway: 10.120.112.193
          zone_prp: cn-sh-01f
          vni: 2503
          scope: SERVICE
          reserved: 10.120.112.193..10.120.112.201,10.120.112.250..10.120.112.254
        - cidr: 10.120.113.0/26
          gateway: 10.120.113.1
          zone_prp: cn-sh-01f
          vni: 2504
          scope: SERVICE
          reserved: 10.120.113.1..10.120.113.9,10.120.113.58..10.120.113.62
        - cidr: 10.120.113.64/26
          gateway: 10.120.113.65
          zone_prp: cn-sh-01f
          vni: 2505
          scope: SERVICE
          reserved: 10.120.113.65..10.120.113.73,10.120.113.122..10.120.113.126
        - cidr: 10.120.113.128/26
          gateway: 10.120.113.129
          zone_prp: cn-sh-01f
          vni: 2506
          scope: SERVICE
          reserved: 10.120.113.129..10.120.113.137,10.120.113.186..10.120.113.190
        - cidr: 10.120.113.192/26
          gateway: 10.120.113.193
          zone_prp: cn-sh-01f
          vni: 2507
          scope: SERVICE
          reserved: 10.120.113.193..10.120.113.201,10.120.113.250..10.120.113.254
        - cidr: 10.120.114.0/26
          gateway: 10.120.114.1
          zone_prp: cn-sh-01f
          vni: 2508
          scope: SERVICE
          reserved: 10.120.114.1..10.120.114.9,10.120.114.58..10.120.114.62
        - cidr: 10.120.114.64/26
          gateway: 10.120.114.65
          zone_prp: cn-sh-01f
          vni: 2509
          scope: SERVICE
          reserved: 10.120.114.65..10.120.114.73,10.120.114.122..10.120.114.126
        - cidr: 10.120.114.128/26
          gateway: 10.120.114.129
          zone_prp: cn-sh-01f
          vni: 2510
          scope: SERVICE
          reserved: 10.120.114.129..10.120.114.137,10.120.114.186..10.120.114.190
        - cidr: 10.120.114.192/26
          gateway: 10.120.114.193
          zone_prp: cn-sh-01f
          vni: 2511
          scope: SERVICE
          reserved: 10.120.114.193..10.120.114.201,10.120.114.250..10.120.114.254
        - cidr: 10.120.115.0/26
          gateway: 10.120.115.1
          zone_prp: cn-sh-01f
          vni: 2512
          scope: SERVICE
          reserved: 10.120.115.1..10.120.115.9,10.120.115.58..10.120.115.62
        - cidr: 10.120.115.64/26
          gateway: 10.120.115.65
          zone_prp: cn-sh-01f
          vni: 2513
          scope: SERVICE
          reserved: 10.120.115.65..10.120.115.73,10.120.115.122..10.120.115.126
        - cidr: 10.120.115.128/26
          gateway: 10.120.115.129
          zone_prp: cn-sh-01f
          vni: 2514
          scope: SERVICE
          reserved: 10.120.115.129..10.120.115.137,10.120.115.186..10.120.115.190
        - cidr: 10.120.115.192/26
          gateway: 10.120.115.193
          zone_prp: cn-sh-01f
          vni: 2515
          scope: SERVICE
          reserved: 10.120.115.193..10.120.115.201,10.120.115.250..10.120.115.254
        - cidr: 10.120.116.0/26
          gateway: 10.120.116.1
          zone_prp: cn-sh-01f
          vni: 2516
          scope: SERVICE
          reserved: 10.120.116.1..10.120.116.9,10.120.116.58..10.120.116.62
        - cidr: 10.120.116.64/26
          gateway: 10.120.116.65
          zone_prp: cn-sh-01f
          vni: 2517
          scope: SERVICE
          reserved: 10.120.116.65..10.120.116.73,10.120.116.122..10.120.116.126
        - cidr: 10.120.116.128/26
          gateway: 10.120.116.129
          zone_prp: cn-sh-01f
          vni: 2518
          scope: SERVICE
          reserved: 10.120.116.129..10.120.116.137,10.120.116.186..10.120.116.190
        - cidr: 10.120.116.192/26
          gateway: 10.120.116.193
          zone_prp: cn-sh-01f
          vni: 2519
          scope: SERVICE
          reserved: 10.120.116.193..10.120.116.201,10.120.116.250..10.120.116.254
        - cidr: 10.120.117.0/26
          gateway: 10.120.117.1
          zone_prp: cn-sh-01f
          vni: 2520
          scope: SERVICE
          reserved: 10.120.117.1..10.120.117.9,10.120.117.58..10.120.117.62
        - cidr: 10.120.117.64/26
          gateway: 10.120.117.65
          zone_prp: cn-sh-01f
          vni: 2521
          scope: SERVICE
          reserved: 10.120.117.65..10.120.117.73,10.120.117.122..10.120.117.126
        - cidr: 10.120.117.128/26
          gateway: 10.120.117.129
          zone_prp: cn-sh-01f
          vni: 2522
          scope: SERVICE
          reserved: 10.120.117.129..10.120.117.137,10.120.117.186..10.120.117.190
        - cidr: 10.120.117.192/26
          gateway: 10.120.117.193
          zone_prp: cn-sh-01f
          vni: 2523
          scope: SERVICE
          reserved: 10.120.117.193..10.120.117.201,10.120.117.250..10.120.117.254
        - cidr: 10.120.118.0/26
          gateway: 10.120.118.1
          zone_prp: cn-sh-01f
          vni: 2524
          scope: SERVICE
          reserved: 10.120.118.1..10.120.118.9,10.120.118.58..10.120.118.62
        - cidr: 10.120.118.64/26
          gateway: 10.120.118.65
          zone_prp: cn-sh-01f
          vni: 2525
          scope: SERVICE
          reserved: 10.120.118.65..10.120.118.73,10.120.118.122..10.120.118.126
        - cidr: 10.120.118.128/26
          gateway: 10.120.118.129
          zone_prp: cn-sh-01f
          vni: 2526
          scope: SERVICE
          reserved: 10.120.118.129..10.120.118.137,10.120.118.186..10.120.118.190
        - cidr: 10.120.118.192/26
          gateway: 10.120.118.193
          zone_prp: cn-sh-01f
          vni: 2527
          scope: SERVICE
          reserved: 10.120.118.193..10.120.118.201,10.120.118.250..10.120.118.254
        - cidr: 10.120.119.0/26
          gateway: 10.120.119.1
          zone_prp: cn-sh-01f
          vni: 2528
          scope: SERVICE
          reserved: 10.120.119.1..10.120.119.9,10.120.119.58..10.120.119.62
        - cidr: 10.120.119.64/26
          gateway: 10.120.119.65
          zone_prp: cn-sh-01f
          vni: 2529
          scope: SERVICE
          reserved: 10.120.119.65..10.120.119.73,10.120.119.122..10.120.119.126
        - cidr: 10.120.119.128/26
          gateway: 10.120.119.129
          zone_prp: cn-sh-01f
          vni: 2530
          scope: SERVICE
          reserved: 10.120.119.129..10.120.119.137,10.120.119.186..10.120.119.190
        - cidr: 10.120.119.192/26
          gateway: 10.120.119.193
          zone_prp: cn-sh-01f
          vni: 2531
          scope: SERVICE
          reserved: 10.120.119.193..10.120.119.201,10.120.119.250..10.120.119.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sh-01f
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1500
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1501
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1502
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1503
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1504
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1505
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1506
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1507
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1508
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1509
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1510
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1511
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1512
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1513
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1514
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1515
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1516
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1517
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1518
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1519
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1520
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1521
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1522
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1523
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1524
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1525
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1526
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1527
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1528
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1529
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1530
          scope: TRAINING
          reserved: ************..************,**************..**************
        - cidr: ************/24
          gateway: ************
          zone_prp: cn-sh-01f
          vni: 1531
          scope: TRAINING
          reserved: ************..************,**************..**************

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-sh-01f
      vpc_default_region: cn-sh-01f
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools cidr_pools vlan TRAINING"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
