#!/usr/bin/env bash
set -beou pipefail
cd "$(dirname "${0}")"
# shellcheck source=/dev/null
source comm_libs.sh

function backup_coredns_deploy_volume_items() {
    file_path="coredns-deploy-volume-$(date +%Y-%m-%d_%H-%M-%S)-backup.json"
    kubectl get deployment coredns -n kube-system -o jsonpath="{.spec.template.spec.volumes[0].configMap.items}" | tee "${file_path}"
    if [ -z "$(cat "${file_path}")" ]; then
        echo "No items key found in the coredns configMap volume."
        echo "It's already satisfy requirements. Skip coredns deployment update."
    else
        echo "The current configMap volume keys in the coredns deployment are:"
        cat "${file_path}"
        echo "Backup coredns deployment volume items to ${file_path}"
    fi
}

function restore_coredns_deploy_volume_items() {
    backup_file="${1}"
    read -r -p "Are you sure to restore coredns deployment volume from ${backup_file}? [Y/n]" confirm
    if [[ "${confirm}" == "Y" ]]; then
        kubectl patch deployment coredns -n kube-system --type=json -p "[{'op': 'add', 'path': '/spec/template/spec/volumes/0/configMap/items', 'value': $(cat "${backup_file}")}]"
        echo "Restore coredns deployment volume items from ${backup_file}"
    else
        echo "Skip restore coredns deployment volume items"
    fi
}

function remove_coredns_deploy_volume_items() {
    backup_coredns_deploy_volume_items
    read -r -p "Are you sure to remove coredns deployment volume items ? [Y/n]" confirm
    if [[ "${confirm}" == "Y" ]]; then
        kubectl patch deployment coredns -n kube-system --type=json -p "[{'op': 'remove', 'path': '/spec/template/spec/volumes/0/configMap/items'}]"
        echo "Remove coredns deployment volume items"
    else
        echo "Skip remove coredns deployment volume items"
    fi
}

function print_help() {
    echo "Usage: $0 [backup|restore|remove]"
    echo "    backup: Backup coredns deployment volume items"
    echo "    restore: Restore coredns deployment volume items from backup file"
    echo "    remove: Remove coredns deployment volume items"
}

OPERATION=""
if [[ -n "${1-}" ]]; then
    OPERATION="${1}"
else
    print_help
    exit 1
fi

if [[ "${OPERATION}" == "backup" ]]; then
    backup_coredns_deploy_volume_items
elif [[ "${OPERATION}" == "restore" ]]; then
    if [[ -z "${2-}" ]]; then
        echo "Invalid argument"
        echo "    Usage: $0 restore [backup_file]"
        exit 1
    elif [[ ! -f "${2}" ]]; then
        echo "File not found: ${2}"
        exit 1
    fi
    restore_coredns_deploy_volume_items "${2}"
elif [[ "${OPERATION}" == "remove" ]]; then
    remove_coredns_deploy_volume_items
else
    print_help
    exit 1
fi
