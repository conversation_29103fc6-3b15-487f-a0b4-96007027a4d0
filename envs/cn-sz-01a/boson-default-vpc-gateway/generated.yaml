---
# Source: boson-default-vpc-gateway/templates/cm.yaml
apiVersion: v1
data:
  nameservers: *************
kind: ConfigMap
metadata:
  name: boson-default-vpc-gateway-config
  namespace: plat-lepton-service
  labels:
    helm.sh/chart: boson-default-vpc-gateway-1.11.0
    app.kubernetes.io/name: boson-default-vpc-gateway
    app.kubernetes.io/instance: boson-default-vpc-gateway
    app.kubernetes.io/version: 1.11.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-default-vpc-gateway
    app.kubernetes.io/part-of: BosonService
    boson.sensetime.com/hagw-name: boson-vpc-gw-dummy-hagw
---
# Source: boson-default-vpc-gateway/templates/vpc-nat-gateway.yaml
apiVersion: kubeovn.io/v1
kind: VpcNatGateway
metadata:
  annotations:
    subnetGateway: **********
    boson.sensetime.com/ha-mode: master-standby
    boson.sensetime.com/ha-peer-ip: *************
    boson.sensetime.com/ha-vip: *************
    boson.sensetime.com/ha-monitor: ***********
    boson.sensetime.com/ha-external-vips: *************
  name: boson-default-vpc-gateway-0
  labels:
    helm.sh/chart: boson-default-vpc-gateway-1.11.0
    app.kubernetes.io/name: boson-default-vpc-gateway
    app.kubernetes.io/instance: boson-default-vpc-gateway
    app.kubernetes.io/version: 1.11.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-default-vpc-gateway
    app.kubernetes.io/part-of: BosonService
    boson.sensetime.com/hagw-name: boson-vpc-gw-dummy-hagw
spec:
  vpc: ovn-cluster
  subnet: ovn-default
  # use rip in lanIp
  lanIp: *************
  eips:
  # use rip in eips
  - eipCIDR: ***********62/24
    gateway: ***********
  snatRules:
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  - eip: *************
    internalCIDR: **********/32
  - eip: *************
    internalCIDR: **********/32
  - eip: *************
    internalCIDR: **********/32
  - eip: *************
    internalCIDR: **********/32
  - eip: *************
    internalCIDR: **********/32
  dnatRules:
  - eip: *************
    externalPort: "53"
    internalIp: **********
    internalPort: "53"
    protocol: udp
  - eip: *************
    externalPort: "53"
    internalIp: **********
    internalPort: "53"
    protocol: tcp
  - eip: *************
    externalPort: "11010"
    internalIp: **********
    internalPort: "11010"
    protocol: udp
  - eip: *************
    externalPort: "11010"
    internalIp: **********
    internalPort: "11010"
    protocol: tcp
  - eip: *************
    externalPort: "11011"
    internalIp: **********
    internalPort: "11011"
    protocol: udp
  - eip: *************
    externalPort: "11011"
    internalIp: **********
    internalPort: "11011"
    protocol: tcp
  - eip: *************
    externalPort: "18000"
    internalIp: **********
    internalPort: "18000"
    protocol: udp
  - eip: *************
    externalPort: "18000"
    internalIp: **********
    internalPort: "18000"
    protocol: tcp
  - eip: *************
    externalPort: "52040"
    internalIp: **********
    internalPort: "52040"
    protocol: udp
  - eip: *************
    externalPort: "52040"
    internalIp: **********
    internalPort: "52040"
    protocol: tcp
  - eip: *************
    externalPort: "52090"
    internalIp: **********
    internalPort: "52090"
    protocol: udp
  - eip: *************
    externalPort: "52090"
    internalIp: **********
    internalPort: "52090"
    protocol: tcp
---
# Source: boson-default-vpc-gateway/templates/vpc-nat-gateway.yaml
apiVersion: kubeovn.io/v1
kind: VpcNatGateway
metadata:
  annotations:
    subnetGateway: **********
    boson.sensetime.com/ha-mode: master-standby
    boson.sensetime.com/ha-peer-ip: ***********62
    boson.sensetime.com/ha-vip: *************
    boson.sensetime.com/ha-monitor: ***********
    boson.sensetime.com/ha-external-vips: *************
  name: boson-default-vpc-gateway-1
  labels:
    helm.sh/chart: boson-default-vpc-gateway-1.11.0
    app.kubernetes.io/name: boson-default-vpc-gateway
    app.kubernetes.io/instance: boson-default-vpc-gateway
    app.kubernetes.io/version: 1.11.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-default-vpc-gateway
    app.kubernetes.io/part-of: BosonService
    boson.sensetime.com/hagw-name: boson-vpc-gw-dummy-hagw
spec:
  vpc: ovn-cluster
  subnet: ovn-default
  # use rip in lanIp
  lanIp: *************
  eips:
  # use rip in eips
  - eipCIDR: *************/24
    gateway: ***********
  snatRules:
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  # use vip in snat
  - eip: *************
    internalCIDR: **********/32
  dnatRules:
  - eip: *************
    externalPort: "53"
    internalIp: **********
    internalPort: "53"
    protocol: udp
  - eip: *************
    externalPort: "53"
    internalIp: **********
    internalPort: "53"
    protocol: tcp
  - eip: *************
    externalPort: "11010"
    internalIp: **********
    internalPort: "11010"
    protocol: udp
  - eip: *************
    externalPort: "11010"
    internalIp: **********
    internalPort: "11010"
    protocol: tcp
  - eip: *************
    externalPort: "11011"
    internalIp: **********
    internalPort: "11011"
    protocol: udp
  - eip: *************
    externalPort: "11011"
    internalIp: **********
    internalPort: "11011"
    protocol: tcp
  - eip: *************
    externalPort: "18000"
    internalIp: **********
    internalPort: "18000"
    protocol: udp
  - eip: *************
    externalPort: "18000"
    internalIp: **********
    internalPort: "18000"
    protocol: tcp
  - eip: *************
    externalPort: "52040"
    internalIp: **********
    internalPort: "52040"
    protocol: udp
  - eip: *************
    externalPort: "52040"
    internalIp: **********
    internalPort: "52040"
    protocol: tcp
  - eip: *************
    externalPort: "52090"
    internalIp: **********
    internalPort: "52090"
    protocol: udp
  - eip: *************
    externalPort: "52090"
    internalIp: **********
    internalPort: "52090"
    protocol: tcp
