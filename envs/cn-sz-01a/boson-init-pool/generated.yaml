---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-cm.yaml
apiVersion: v1
data:
  boson-provider.yaml: |-
    env: prod-cn-sz-01a
    pg:
      host: *************
      port: "37321"
      user: boson
      password: xxxxxxxxxxxx
      db: boson_service_v2

    # Provider的初始化地址池参数
    init_pool:
      nat_gateways:
        cidr: ***********/24
        gateway: ***********
        ips:
        - "***********0"
        - "***********1"
        - "***********2"
        - "***********3"
        - "***********4"
        - "***********5"
        - "***********6"
        - "***********7"
        - "***********8"
        - "***********9"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "************"
        - "10.116.37.49"
        - "10.116.37.50"
        - "10.116.37.51"
        - "10.116.37.52"
        - "10.116.37.53"
        - "10.116.37.54"
        - "10.116.37.55"
        - "10.116.37.56"
        - "10.116.37.57"
        - "10.116.37.58"
        - "10.116.37.59"
        - "10.116.37.60"
        - "10.116.37.61"
        - "10.116.37.62"
        - "10.116.37.63"
        - "10.116.37.64"
        - "10.116.37.65"
        - "10.116.37.66"
        - "10.116.37.67"
        - "10.116.37.68"
        - "10.116.37.69"
        - "10.116.37.70"
        - "10.116.37.71"
        - "10.116.37.72"
        - "10.116.37.73"
        - "10.116.37.74"
        - "10.116.37.75"
        - "10.116.37.76"
        - "10.116.37.77"
        - "10.116.37.78"
        - "10.116.37.79"
        - "10.116.37.80"
        - "10.116.37.81"
        - "10.116.37.82"
        - "10.116.37.83"
        - "10.116.37.84"
        - "10.116.37.85"
        - "10.116.37.86"
        - "10.116.37.87"
        - "10.116.37.88"
        - "10.116.37.89"
        - "10.116.37.90"
        - "10.116.37.91"
        - "10.116.37.92"
        - "10.116.37.93"
        - "10.116.37.94"
        - "10.116.37.95"
        - "10.116.37.96"
        - "10.116.37.97"
        - "10.116.37.98"
        - "10.116.37.99"
        - "***********00"
        - "***********01"
        - "***********02"
        - "***********03"
        - "***********04"
        - "***********05"
        - "***********06"
        - "***********07"
        - "***********08"
        - "***********09"
        - "***********10"
        - "***********11"
        - "***********12"
        - "***********13"
        - "***********14"
        - "***********15"
        - "***********16"
        - "***********17"
        - "***********18"
        - "***********19"
        - "***********20"
        - "***********21"
        - "***********22"
        - "***********23"
        - "***********24"
        - "***********25"
        - "***********26"
        - "***********27"
        - "***********28"
        - "***********29"
        - "***********30"
        - "***********31"
        - "***********32"
        - "***********33"
        - "***********34"
        - "***********35"
        - "***********36"
        - "***********37"
        - "***********38"
        - "***********39"
        - "***********40"
        - "***********41"
        - "***********42"
        - "***********43"
        - "***********44"
        - "***********45"
        - "***********46"
        - "***********47"
        - "***********48"
        - "***********49"
        - "***********50"
        - "***********51"
        - "***********52"
        - "***********53"
        - "***********54"
        - "***********55"
        - "***********56"
        - "***********57"
        - "***********58"
        - "***********59"
        - "***********60"
        - "***********61"
        - "***********62"
        - "***********63"
        - "***********64"
        - "***********65"
        - "***********66"
        - "***********67"
        - "***********68"
        - "***********69"
        - "***********70"
        - "***********71"
        - "***********72"
        - "***********73"
        - "***********74"
        - "***********75"
        - "***********76"
        - "***********77"
        - "***********78"
        - "***********79"
        - "***********80"
        - "***********81"
        - "***********82"
        - "***********83"
        - "***********84"
        - "***********85"
        - "***********86"
        - "***********87"
        - "***********88"
        - "***********89"
        - "***********90"
        - "***********91"
        - "***********92"
        - "***********93"
        - "***********94"
        - "***********95"
        - "***********96"
        - "***********97"
        - "***********98"
        - "***********99"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
        - "************0"
        - "************1"
        - "************2"
        - "************3"
        - "************4"
        - "************5"
        - "************6"
        - "************7"
        - "************8"
        - "************9"
      slbs:
        cidr: 0.0.0.0/24
        gateway: *******
        ips:
      eips:
        cidr: **************/26
        gateway: **************
        ips:
        - "************04"
        - "************05"
        - "************06"
        - "************07"
        - "************08"
        - "************09"
        - "************10"
        - "************11"
        - "************12"
        - "************13"
        - "************14"
        - "************15"
        - "************16"
        - "************17"
        - "************18"
        - "************19"
        - "************20"
        - "************21"
        - "************22"
        - "************23"
        - "************24"
        - "************25"
        - "************26"
        - "************27"
        - "************28"
        - "************29"
        - "************30"
        - "************31"
        - "************32"
        - "************33"
        - "************34"
        - "************35"
        - "************36"
        - "************37"
        - "************38"
        - "************39"
        - "************40"
        - "************41"
        - "************42"
        - "************43"
        - "************44"
        - "************45"
        - "************46"
        - "************47"
        - "************48"
        - "************49"
        - "************50"
        - "************51"
        - "************52"
        - "************53"
        - "************54"
        sku: CHINA_TELECOM
      cidr_pools:
        ib:
        - cidr: 10.110.128.0/26
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 2500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.58..10.110.128.62
        - cidr: 10.110.128.64/26
          gateway: 10.110.128.65
          zone_prp: cn-sz-01a
          vni: 2501
          scope: TRAINING
          reserved: 10.110.128.65..10.110.128.73,10.110.128.122..10.110.128.126
        - cidr: 10.110.128.128/26
          gateway: 10.110.128.129
          zone_prp: cn-sz-01a
          vni: 2502
          scope: TRAINING
          reserved: 10.110.128.129..10.110.128.137,10.110.128.186..10.110.128.190
        - cidr: 10.110.128.192/26
          gateway: 10.110.128.193
          zone_prp: cn-sz-01a
          vni: 2503
          scope: TRAINING
          reserved: 10.110.128.193..10.110.128.201,10.110.128.250..10.110.128.254
        - cidr: 10.110.129.0/26
          gateway: 10.110.129.1
          zone_prp: cn-sz-01a
          vni: 2504
          scope: TRAINING
          reserved: 10.110.129.1..10.110.129.9,10.110.129.58..10.110.129.62
        - cidr: 10.110.129.64/26
          gateway: 10.110.129.65
          zone_prp: cn-sz-01a
          vni: 2505
          scope: TRAINING
          reserved: 10.110.129.65..10.110.129.73,10.110.129.122..10.110.129.126
        - cidr: 10.110.129.128/26
          gateway: 10.110.129.129
          zone_prp: cn-sz-01a
          vni: 2506
          scope: TRAINING
          reserved: 10.110.129.129..10.110.129.137,10.110.129.186..10.110.129.190
        - cidr: 10.110.129.192/26
          gateway: 10.110.129.193
          zone_prp: cn-sz-01a
          vni: 2507
          scope: TRAINING
          reserved: 10.110.129.193..10.110.129.201,10.110.129.250..10.110.129.254
        - cidr: 10.110.130.0/26
          gateway: 10.110.130.1
          zone_prp: cn-sz-01a
          vni: 2508
          scope: TRAINING
          reserved: 10.110.130.1..10.110.130.9,10.110.130.58..10.110.130.62
        - cidr: 10.110.130.64/26
          gateway: 10.110.130.65
          zone_prp: cn-sz-01a
          vni: 2509
          scope: TRAINING
          reserved: 10.110.130.65..10.110.130.73,10.110.130.122..10.110.130.126
        - cidr: 10.110.130.128/26
          gateway: 10.110.130.129
          zone_prp: cn-sz-01a
          vni: 2510
          scope: TRAINING
          reserved: 10.110.130.129..10.110.130.137,10.110.130.186..10.110.130.190
        - cidr: 10.110.130.192/26
          gateway: 10.110.130.193
          zone_prp: cn-sz-01a
          vni: 2511
          scope: TRAINING
          reserved: 10.110.130.193..10.110.130.201,10.110.130.250..10.110.130.254
        - cidr: 10.110.131.0/26
          gateway: 10.110.131.1
          zone_prp: cn-sz-01a
          vni: 2512
          scope: TRAINING
          reserved: 10.110.131.1..10.110.131.9,10.110.131.58..10.110.131.62
        - cidr: 10.110.131.64/26
          gateway: 10.110.131.65
          zone_prp: cn-sz-01a
          vni: 2513
          scope: TRAINING
          reserved: 10.110.131.65..10.110.131.73,10.110.131.122..10.110.131.126
        - cidr: 10.110.131.128/26
          gateway: 10.110.131.129
          zone_prp: cn-sz-01a
          vni: 2514
          scope: TRAINING
          reserved: 10.110.131.129..10.110.131.137,10.110.131.186..10.110.131.190
        - cidr: 10.110.131.192/26
          gateway: 10.110.131.193
          zone_prp: cn-sz-01a
          vni: 2515
          scope: TRAINING
          reserved: 10.110.131.193..10.110.131.201,10.110.131.250..10.110.131.254
        - cidr: 10.110.132.0/26
          gateway: 10.110.132.1
          zone_prp: cn-sz-01a
          vni: 2516
          scope: TRAINING
          reserved: 10.110.132.1..10.110.132.9,10.110.132.58..10.110.132.62
        - cidr: 10.110.132.64/26
          gateway: 10.110.132.65
          zone_prp: cn-sz-01a
          vni: 2517
          scope: TRAINING
          reserved: 10.110.132.65..10.110.132.73,10.110.132.122..10.110.132.126
        - cidr: 10.110.132.128/26
          gateway: 10.110.132.129
          zone_prp: cn-sz-01a
          vni: 2518
          scope: TRAINING
          reserved: 10.110.132.129..10.110.132.137,10.110.132.186..10.110.132.190
        - cidr: 10.110.132.192/26
          gateway: 10.110.132.193
          zone_prp: cn-sz-01a
          vni: 2519
          scope: TRAINING
          reserved: 10.110.132.193..10.110.132.201,10.110.132.250..10.110.132.254
        - cidr: 10.110.133.0/26
          gateway: 10.110.133.1
          zone_prp: cn-sz-01a
          vni: 2520
          scope: TRAINING
          reserved: 10.110.133.1..10.110.133.9,10.110.133.58..10.110.133.62
        - cidr: 10.110.133.64/26
          gateway: 10.110.133.65
          zone_prp: cn-sz-01a
          vni: 2521
          scope: TRAINING
          reserved: 10.110.133.65..10.110.133.73,10.110.133.122..10.110.133.126
        - cidr: 10.110.133.128/26
          gateway: 10.110.133.129
          zone_prp: cn-sz-01a
          vni: 2522
          scope: TRAINING
          reserved: 10.110.133.129..10.110.133.137,10.110.133.186..10.110.133.190
        - cidr: 10.110.133.192/26
          gateway: 10.110.133.193
          zone_prp: cn-sz-01a
          vni: 2523
          scope: TRAINING
          reserved: 10.110.133.193..10.110.133.201,10.110.133.250..10.110.133.254
        - cidr: 10.110.134.0/26
          gateway: 10.110.134.1
          zone_prp: cn-sz-01a
          vni: 2524
          scope: TRAINING
          reserved: 10.110.134.1..10.110.134.9,10.110.134.58..10.110.134.62
        - cidr: 10.110.134.64/26
          gateway: 10.110.134.65
          zone_prp: cn-sz-01a
          vni: 2525
          scope: TRAINING
          reserved: 10.110.134.65..10.110.134.73,10.110.134.122..10.110.134.126
        - cidr: 10.110.134.128/26
          gateway: 10.110.134.129
          zone_prp: cn-sz-01a
          vni: 2526
          scope: TRAINING
          reserved: 10.110.134.129..10.110.134.137,10.110.134.186..10.110.134.190
        - cidr: 10.110.134.192/26
          gateway: 10.110.134.193
          zone_prp: cn-sz-01a
          vni: 2527
          scope: TRAINING
          reserved: 10.110.134.193..10.110.134.201,10.110.134.250..10.110.134.254
        - cidr: 10.110.135.0/26
          gateway: 10.110.135.1
          zone_prp: cn-sz-01a
          vni: 2528
          scope: TRAINING
          reserved: 10.110.135.1..10.110.135.9,10.110.135.58..10.110.135.62
        - cidr: 10.110.135.64/26
          gateway: 10.110.135.65
          zone_prp: cn-sz-01a
          vni: 2529
          scope: TRAINING
          reserved: 10.110.135.65..10.110.135.73,10.110.135.122..10.110.135.126
        - cidr: 10.110.135.128/26
          gateway: 10.110.135.129
          zone_prp: cn-sz-01a
          vni: 2530
          scope: TRAINING
          reserved: 10.110.135.129..10.110.135.137,10.110.135.186..10.110.135.190
        - cidr: 10.110.135.192/26
          gateway: 10.110.135.193
          zone_prp: cn-sz-01a
          vni: 2531
          scope: TRAINING
          reserved: 10.110.135.193..10.110.135.201,10.110.135.250..10.110.135.254
        vlan:
        - cidr: 10.119.88.0/26
          gateway: 10.119.88.1
          zone_prp: cn-sz-01a
          vni: 2500
          scope: DATA
          reserved: 10.119.88.1..10.119.88.9,10.119.88.58..10.119.88.62
        - cidr: 10.119.88.64/26
          gateway: 10.119.88.65
          zone_prp: cn-sz-01a
          vni: 2501
          scope: DATA
          reserved: 10.119.88.65..10.119.88.73,10.119.88.122..10.119.88.126
        - cidr: 10.119.88.128/26
          gateway: 10.119.88.129
          zone_prp: cn-sz-01a
          vni: 2502
          scope: DATA
          reserved: 10.119.88.129..10.119.88.137,10.119.88.186..10.119.88.190
        - cidr: 10.119.88.192/26
          gateway: 10.119.88.193
          zone_prp: cn-sz-01a
          vni: 2503
          scope: DATA
          reserved: 10.119.88.193..10.119.88.201,10.119.88.250..10.119.88.254
        - cidr: 10.119.89.0/26
          gateway: 10.119.89.1
          zone_prp: cn-sz-01a
          vni: 2504
          scope: DATA
          reserved: 10.119.89.1..10.119.89.9,10.119.89.58..10.119.89.62
        - cidr: 10.119.89.64/26
          gateway: 10.119.89.65
          zone_prp: cn-sz-01a
          vni: 2505
          scope: DATA
          reserved: 10.119.89.65..10.119.89.73,10.119.89.122..10.119.89.126
        - cidr: 10.119.89.128/26
          gateway: 10.119.89.129
          zone_prp: cn-sz-01a
          vni: 2506
          scope: DATA
          reserved: 10.119.89.129..10.119.89.137,10.119.89.186..10.119.89.190
        - cidr: 10.119.89.192/26
          gateway: 10.119.89.193
          zone_prp: cn-sz-01a
          vni: 2507
          scope: DATA
          reserved: 10.119.89.193..10.119.89.201,10.119.89.250..10.119.89.254
        - cidr: 10.119.90.0/26
          gateway: 10.119.90.1
          zone_prp: cn-sz-01a
          vni: 2508
          scope: DATA
          reserved: 10.119.90.1..10.119.90.9,10.119.90.58..10.119.90.62
        - cidr: 10.119.90.64/26
          gateway: 10.119.90.65
          zone_prp: cn-sz-01a
          vni: 2509
          scope: DATA
          reserved: 10.119.90.65..10.119.90.73,10.119.90.122..10.119.90.126
        - cidr: 10.119.90.128/26
          gateway: 10.119.90.129
          zone_prp: cn-sz-01a
          vni: 2510
          scope: DATA
          reserved: 10.119.90.129..10.119.90.137,10.119.90.186..10.119.90.190
        - cidr: 10.119.90.192/26
          gateway: 10.119.90.193
          zone_prp: cn-sz-01a
          vni: 2511
          scope: DATA
          reserved: 10.119.90.193..10.119.90.201,10.119.90.250..10.119.90.254
        - cidr: 10.119.91.0/26
          gateway: 10.119.91.1
          zone_prp: cn-sz-01a
          vni: 2512
          scope: DATA
          reserved: 10.119.91.1..10.119.91.9,10.119.91.58..10.119.91.62
        - cidr: 10.119.91.64/26
          gateway: 10.119.91.65
          zone_prp: cn-sz-01a
          vni: 2513
          scope: DATA
          reserved: 10.119.91.65..10.119.91.73,10.119.91.122..10.119.91.126
        - cidr: 10.119.91.128/26
          gateway: 10.119.91.129
          zone_prp: cn-sz-01a
          vni: 2514
          scope: DATA
          reserved: 10.119.91.129..10.119.91.137,10.119.91.186..10.119.91.190
        - cidr: 10.119.91.192/26
          gateway: 10.119.91.193
          zone_prp: cn-sz-01a
          vni: 2515
          scope: DATA
          reserved: 10.119.91.193..10.119.91.201,10.119.91.250..10.119.91.254
        - cidr: 10.119.92.0/26
          gateway: 10.119.92.1
          zone_prp: cn-sz-01a
          vni: 2516
          scope: DATA
          reserved: 10.119.92.1..10.119.92.9,10.119.92.58..10.119.92.62
        - cidr: 10.119.92.64/26
          gateway: 10.119.92.65
          zone_prp: cn-sz-01a
          vni: 2517
          scope: DATA
          reserved: 10.119.92.65..10.119.92.73,10.119.92.122..10.119.92.126
        - cidr: 10.119.92.128/26
          gateway: 10.119.92.129
          zone_prp: cn-sz-01a
          vni: 2518
          scope: DATA
          reserved: 10.119.92.129..10.119.92.137,10.119.92.186..10.119.92.190
        - cidr: 10.119.92.192/26
          gateway: 10.119.92.193
          zone_prp: cn-sz-01a
          vni: 2519
          scope: DATA
          reserved: 10.119.92.193..10.119.92.201,10.119.92.250..10.119.92.254
        - cidr: 10.119.93.0/26
          gateway: 10.119.93.1
          zone_prp: cn-sz-01a
          vni: 2520
          scope: DATA
          reserved: 10.119.93.1..10.119.93.9,10.119.93.58..10.119.93.62
        - cidr: 10.119.93.64/26
          gateway: 10.119.93.65
          zone_prp: cn-sz-01a
          vni: 2521
          scope: DATA
          reserved: 10.119.93.65..10.119.93.73,10.119.93.122..10.119.93.126
        - cidr: 10.119.93.128/26
          gateway: 10.119.93.129
          zone_prp: cn-sz-01a
          vni: 2522
          scope: DATA
          reserved: 10.119.93.129..10.119.93.137,10.119.93.186..10.119.93.190
        - cidr: 10.119.93.192/26
          gateway: 10.119.93.193
          zone_prp: cn-sz-01a
          vni: 2523
          scope: DATA
          reserved: 10.119.93.193..10.119.93.201,10.119.93.250..10.119.93.254
        - cidr: 10.119.94.0/26
          gateway: 10.119.94.1
          zone_prp: cn-sz-01a
          vni: 2524
          scope: DATA
          reserved: 10.119.94.1..10.119.94.9,10.119.94.58..10.119.94.62
        - cidr: 10.119.94.64/26
          gateway: 10.119.94.65
          zone_prp: cn-sz-01a
          vni: 2525
          scope: DATA
          reserved: 10.119.94.65..10.119.94.73,10.119.94.122..10.119.94.126
        - cidr: 10.119.94.128/26
          gateway: 10.119.94.129
          zone_prp: cn-sz-01a
          vni: 2526
          scope: DATA
          reserved: 10.119.94.129..10.119.94.137,10.119.94.186..10.119.94.190
        - cidr: 10.119.94.192/26
          gateway: 10.119.94.193
          zone_prp: cn-sz-01a
          vni: 2527
          scope: DATA
          reserved: 10.119.94.193..10.119.94.201,10.119.94.250..10.119.94.254
        - cidr: 10.119.95.0/26
          gateway: 10.119.95.1
          zone_prp: cn-sz-01a
          vni: 2528
          scope: DATA
          reserved: 10.119.95.1..10.119.95.9,10.119.95.58..10.119.95.62
        - cidr: 10.119.95.64/26
          gateway: 10.119.95.65
          zone_prp: cn-sz-01a
          vni: 2529
          scope: DATA
          reserved: 10.119.95.65..10.119.95.73,10.119.95.122..10.119.95.126
        - cidr: 10.119.95.128/26
          gateway: 10.119.95.129
          zone_prp: cn-sz-01a
          vni: 2530
          scope: DATA
          reserved: 10.119.95.129..10.119.95.137,10.119.95.186..10.119.95.190
        - cidr: 10.119.95.192/26
          gateway: 10.119.95.193
          zone_prp: cn-sz-01a
          vni: 2531
          scope: DATA
          reserved: 10.119.95.193..10.119.95.201,10.119.95.250..10.119.95.254
        - cidr: 10.119.120.0/26
          gateway: 10.119.120.1
          zone_prp: cn-sz-01a
          vni: 2500
          scope: SERVICE
          reserved: 10.119.120.1..10.119.120.9,10.119.120.58..10.119.120.62
        - cidr: 10.119.120.64/26
          gateway: 10.119.120.65
          zone_prp: cn-sz-01a
          vni: 2501
          scope: SERVICE
          reserved: 10.119.120.65..10.119.120.73,10.119.120.122..10.119.120.126
        - cidr: 10.119.120.128/26
          gateway: 10.119.120.129
          zone_prp: cn-sz-01a
          vni: 2502
          scope: SERVICE
          reserved: 10.119.120.129..10.119.120.137,10.119.120.186..10.119.120.190
        - cidr: 10.119.120.192/26
          gateway: 10.119.120.193
          zone_prp: cn-sz-01a
          vni: 2503
          scope: SERVICE
          reserved: 10.119.120.193..10.119.120.201,10.119.120.250..10.119.120.254
        - cidr: 10.119.121.0/26
          gateway: 10.119.121.1
          zone_prp: cn-sz-01a
          vni: 2504
          scope: SERVICE
          reserved: 10.119.121.1..10.119.121.9,10.119.121.58..10.119.121.62
        - cidr: 10.119.121.64/26
          gateway: 10.119.121.65
          zone_prp: cn-sz-01a
          vni: 2505
          scope: SERVICE
          reserved: 10.119.121.65..10.119.121.73,10.119.121.122..10.119.121.126
        - cidr: 10.119.121.128/26
          gateway: 10.119.121.129
          zone_prp: cn-sz-01a
          vni: 2506
          scope: SERVICE
          reserved: 10.119.121.129..10.119.121.137,10.119.121.186..10.119.121.190
        - cidr: 10.119.121.192/26
          gateway: 10.119.121.193
          zone_prp: cn-sz-01a
          vni: 2507
          scope: SERVICE
          reserved: 10.119.121.193..10.119.121.201,10.119.121.250..10.119.121.254
        - cidr: 10.119.122.0/26
          gateway: 10.119.122.1
          zone_prp: cn-sz-01a
          vni: 2508
          scope: SERVICE
          reserved: 10.119.122.1..10.119.122.9,10.119.122.58..10.119.122.62
        - cidr: 10.119.122.64/26
          gateway: 10.119.122.65
          zone_prp: cn-sz-01a
          vni: 2509
          scope: SERVICE
          reserved: 10.119.122.65..10.119.122.73,10.119.122.122..10.119.122.126
        - cidr: 10.119.122.128/26
          gateway: 10.119.122.129
          zone_prp: cn-sz-01a
          vni: 2510
          scope: SERVICE
          reserved: 10.119.122.129..10.119.122.137,10.119.122.186..10.119.122.190
        - cidr: 10.119.122.192/26
          gateway: 10.119.122.193
          zone_prp: cn-sz-01a
          vni: 2511
          scope: SERVICE
          reserved: 10.119.122.193..10.119.122.201,10.119.122.250..10.119.122.254
        - cidr: 10.119.123.0/26
          gateway: 10.119.123.1
          zone_prp: cn-sz-01a
          vni: 2512
          scope: SERVICE
          reserved: 10.119.123.1..10.119.123.9,10.119.123.58..10.119.123.62
        - cidr: 10.119.123.64/26
          gateway: 10.119.123.65
          zone_prp: cn-sz-01a
          vni: 2513
          scope: SERVICE
          reserved: 10.119.123.65..10.119.123.73,10.119.123.122..10.119.123.126
        - cidr: 10.119.123.128/26
          gateway: 10.119.123.129
          zone_prp: cn-sz-01a
          vni: 2514
          scope: SERVICE
          reserved: 10.119.123.129..10.119.123.137,10.119.123.186..10.119.123.190
        - cidr: 10.119.123.192/26
          gateway: 10.119.123.193
          zone_prp: cn-sz-01a
          vni: 2515
          scope: SERVICE
          reserved: 10.119.123.193..10.119.123.201,10.119.123.250..10.119.123.254
        - cidr: 10.119.124.0/26
          gateway: 10.119.124.1
          zone_prp: cn-sz-01a
          vni: 2516
          scope: SERVICE
          reserved: 10.119.124.1..10.119.124.9,10.119.124.58..10.119.124.62
        - cidr: 10.119.124.64/26
          gateway: 10.119.124.65
          zone_prp: cn-sz-01a
          vni: 2517
          scope: SERVICE
          reserved: 10.119.124.65..10.119.124.73,10.119.124.122..10.119.124.126
        - cidr: 10.119.124.128/26
          gateway: 10.119.124.129
          zone_prp: cn-sz-01a
          vni: 2518
          scope: SERVICE
          reserved: 10.119.124.129..10.119.124.137,10.119.124.186..10.119.124.190
        - cidr: 10.119.124.192/26
          gateway: 10.119.124.193
          zone_prp: cn-sz-01a
          vni: 2519
          scope: SERVICE
          reserved: 10.119.124.193..10.119.124.201,10.119.124.250..10.119.124.254
        - cidr: 10.119.125.0/26
          gateway: 10.119.125.1
          zone_prp: cn-sz-01a
          vni: 2520
          scope: SERVICE
          reserved: 10.119.125.1..10.119.125.9,10.119.125.58..10.119.125.62
        - cidr: 10.119.125.64/26
          gateway: 10.119.125.65
          zone_prp: cn-sz-01a
          vni: 2521
          scope: SERVICE
          reserved: 10.119.125.65..10.119.125.73,10.119.125.122..10.119.125.126
        - cidr: 10.119.125.128/26
          gateway: 10.119.125.129
          zone_prp: cn-sz-01a
          vni: 2522
          scope: SERVICE
          reserved: 10.119.125.129..10.119.125.137,10.119.125.186..10.119.125.190
        - cidr: 10.119.125.192/26
          gateway: 10.119.125.193
          zone_prp: cn-sz-01a
          vni: 2523
          scope: SERVICE
          reserved: 10.119.125.193..10.119.125.201,10.119.125.250..10.119.125.254
        - cidr: 10.119.126.0/26
          gateway: 10.119.126.1
          zone_prp: cn-sz-01a
          vni: 2524
          scope: SERVICE
          reserved: 10.119.126.1..10.119.126.9,10.119.126.58..10.119.126.62
        - cidr: 10.119.126.64/26
          gateway: 10.119.126.65
          zone_prp: cn-sz-01a
          vni: 2525
          scope: SERVICE
          reserved: 10.119.126.65..10.119.126.73,10.119.126.122..10.119.126.126
        - cidr: 10.119.126.128/26
          gateway: 10.119.126.129
          zone_prp: cn-sz-01a
          vni: 2526
          scope: SERVICE
          reserved: 10.119.126.129..10.119.126.137,10.119.126.186..10.119.126.190
        - cidr: 10.119.126.192/26
          gateway: 10.119.126.193
          zone_prp: cn-sz-01a
          vni: 2527
          scope: SERVICE
          reserved: 10.119.126.193..10.119.126.201,10.119.126.250..10.119.126.254
        - cidr: 10.119.127.0/26
          gateway: 10.119.127.1
          zone_prp: cn-sz-01a
          vni: 2528
          scope: SERVICE
          reserved: 10.119.127.1..10.119.127.9,10.119.127.58..10.119.127.62
        - cidr: 10.119.127.64/26
          gateway: 10.119.127.65
          zone_prp: cn-sz-01a
          vni: 2529
          scope: SERVICE
          reserved: 10.119.127.65..10.119.127.73,10.119.127.122..10.119.127.126
        - cidr: 10.119.127.128/26
          gateway: 10.119.127.129
          zone_prp: cn-sz-01a
          vni: 2530
          scope: SERVICE
          reserved: 10.119.127.129..10.119.127.137,10.119.127.186..10.119.127.190
        - cidr: 10.119.127.192/26
          gateway: 10.119.127.193
          zone_prp: cn-sz-01a
          vni: 2531
          scope: SERVICE
          reserved: 10.119.127.193..10.119.127.201,10.119.127.250..10.119.127.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.0.0/20
          gateway: 10.110.0.1
          zone_prp: cn-sz-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.0.1..10.110.0.9,10.110.15.250..10.110.15.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1500
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1501
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1502
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1503
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1504
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1505
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1506
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1507
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1508
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1509
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1510
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1511
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1512
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1513
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1514
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1515
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1516
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1517
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1518
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1519
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1520
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1521
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1522
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1523
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1524
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1525
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1526
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1527
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1528
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1529
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1530
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254
        - cidr: 10.110.128.0/24
          gateway: 10.110.128.1
          zone_prp: cn-sz-01a
          vni: 1531
          scope: TRAINING
          reserved: 10.110.128.1..10.110.128.9,10.110.128.250..10.110.128.254

    # Provider的默认参数
    boson_default:
      vpc_default_az: cn-sz-01a
      vpc_default_region: cn-sz-01z
      # init-job 不使用 dgw 配置，但 config 代码校验需要字段存在，因此放个假数据
      dgw:
        enable: true
        policy_cidr: "10.xxx.xxx.0/24"
kind: ConfigMap
metadata:
  name: boson-init-pool-job-config
  namespace: plat-boson-service
  labels:
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: boson-init-pool
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/provider-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: boson-init-pool
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool
subjects:
- kind: ServiceAccount
  name: boson-init-pool
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/provider-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: boson-init-pool-job
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-job
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
spec:
  activeDeadlineSeconds: 3600
  backoffLimit: 1
  completions: 1
  parallelism: 1
  template:
    metadata:
    spec:
      serviceAccount: boson-init-pool
      volumes:
      - configMap:
          name: boson-init-pool-job-config
          items:
          - key: boson-provider.yaml
            path: path/to/boson-provider.yaml
        name: config
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
          - labelSelector:
              matchExpressions:
              - key: app
                operator: In
                values:
                - boson-init-pool
            topologyKey: kubernetes.io/hostname
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool
        image: "registry.sensetime.com/sensecore-boson/boson-toolbox:v1.19.0-14-g33c25f3-20241126102211"
        command:
        - sh
        - -c
        - "./syncTables && ./initPools"
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
        env:
        - name: BOSON_PROVIDER_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: BOSON_PROVIDER_POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: BOSON_PROVIDER_POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: BOSON_PROVIDER_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        volumeMounts:
        - name: config
          mountPath: /boson-toolbox/boson-provider.yaml
          subPath: path/to/boson-provider.yaml
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
rules:
- apiGroups:
  - '*'
  resources:
  - '*'
  verbs:
  - '*'
- nonResourceURLs:
  - '*'
  verbs:
  - '*'
---
# Source: boson-init-pool/templates/pre-post-hook-sa.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install,post-upgrade,post-install
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  name: boson-init-pool-hook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: boson-init-pool-hook
subjects:
- kind: ServiceAccount
  name: boson-init-pool-hook
  namespace: plat-boson-service
---
# Source: boson-init-pool/templates/pre-hook.yaml
apiVersion: batch/v1
# kind can be any type(job/depoy/daemonset etc)
# here we use job, before
kind: Job
metadata:
  name: boson-init-pool-pre-hook
  namespace: plat-boson-service
  labels:
    name: boson-init-pool-pre-hook
    helm.sh/chart: boson-init-pool-1.19.0
    app.kubernetes.io/name: boson-init-pool
    app.kubernetes.io/instance: boson-init-pool
    app.kubernetes.io/version: 1.19.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: boson-init-pool
    app.kubernetes.io/part-of: BosonService
  annotations:
    # These "helm.sh/hook*" annotations define this resource work as a hook.
    "helm.sh/hook": pre-upgrade,pre-install
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation
spec:
  backoffLimit: 1
  completions: 1
  parallelism: 1
  # set ttl for job pod deletion of 3600*24*7
  ttlSecondsAfterFinished: 604800
  template:
    metadata:
      labels:
        job-name: boson-init-pool-pre-hook
    spec:
      serviceAccount: boson-init-pool-hook
      affinity:
      tolerations: #设置容忍性
      - key: diamond.sensetime.com/role-k8s-master
        effect: NoExecute
        operator: Equal
        value: "enabled"
      - key: diamond.sensetime.com/role-business-app
        effect: NoSchedule
        operator: Equal
        value: "sensecore"
      - key: diamond.sensetime.com/belong-resource-prp
        effect: NoSchedule
        operator: Exists
      imagePullSecrets:
      - name: sensecore-boson
      restartPolicy: Never
      containers:
      - name: boson-init-pool-pre-hook
        image: "registry.sensetime.com/sensecore-boson/boson-assistant:multiarch-v1.8.0-1-g2b3dabe-**************"
        command:
        - /bin/bash
        - -c
        - |
          echo "check existing init-pool job. cmd: kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname"
          resource_name=$(kubectl -n plat-boson-service get job -l name=boson-init-pool-job -oname)
          if [[ "${resource_name}" == "" ]]; then
              echo "no existing init-pool job, check complete"
          else
              echo "init-pool job '${resource_name}' exists, deleting it. cmd: kubectl -n plat-boson-service delete ${resource_name}"
              if kubectl -n plat-boson-service delete ${resource_name}; then
                  echo "deleted existing init-pool job '${resource_name}'"
              else
                  echo "Error: delete existing init-pool job '${resource_name}' failed"
                  exit 1
              fi
          fi

          echo "Configure vpc-nat-gw bms.vlan ip rules to enable bms access underlay via net1 in vpc-nat-gw"
          echo "Add ip rule only if bms.vlan interface exists and no rule configure on it."
          vpc_gw_pods=$(kubectl -n kube-system get pods -l ovn.kubernetes.io/vpc-nat-gw=true -oname)
          echo ""
          for vpc_gw_pod in $vpc_gw_pods; do
              echo "Check vpc-nat-gw $vpc_gw_pod for bms.vlan ip rule"
              if kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*"; then
                  if_name=$(kubectl -n kube-system exec "$vpc_gw_pod" -- bash -c "ls -d /sys/class/net/bms.vlan*")
                  if_name=$(basename "${if_name}")
                  echo "found bms.vlan interface: ${if_name} in vpc-nat-gw ${vpc_gw_pod}"

                  bms_rule=$(kubectl -n kube-system exec "${vpc_gw_pod}" -- bash -c "ip rule | grep bms.vlan" | awk -F "\t" '{print $2}')
                  if [[ -n ${bms_rule} ]]; then
                      echo "Found bms.vlan ip rule exist from bms_rule ${bms_rule} in vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
                  else
                      echo "bms.vlan doesn't have ip rule in vpc-nat-gw ${vpc_gw_pod} . Add ip rule for if_name ${if_name} in vpc-nat-gw ${vpc_gw_pod}"
                      echo "kubectl -n kube-system exec '${vpc_gw_pod}' -- echo \"ip rule add iif ${if_name} lookup 100\""
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule add iif "${if_name}" lookup 100
                      kubectl -n kube-system exec "$vpc_gw_pod" -- ip rule | grep bms.vlan
                  fi
              else
                  echo "No bms.vlan interface found for vpc-nat-gw ${vpc_gw_pod} . Skip adding ip rule for bms.vlan"
              fi
              echo ""
          done
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
