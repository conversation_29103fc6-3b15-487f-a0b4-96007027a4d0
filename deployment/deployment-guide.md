# Boson Service 部署

<!-- TOC -->

- [基本信息](#基本信息)
    - [域名](#域名)
    - [基础环境](#基础环境)
    - [中间件依赖](#中间件依赖)
- [准备工作](#准备工作)
    - [确认当前环境](#确认当前环境)
    - [创建 Boson 依赖的 Diamond TK 集群 namespace](#创建-boson-依赖的-diamond-tk-集群-namespace)
    - [创建 Boson 镜像依赖的 Docker-Registry](#创建-boson-镜像依赖的-docker-registry)
    - [PostgreSql DB 创建](#postgresql-db-创建)
    - [RocketMQ Topic 初始化](#rocketmq-topic-初始化)
    - [NATGateway IP 申请](#natgateway-ip-申请)
    - [EIP 申请](#eip-申请)
- [Helm Chart 仓库配置](#helm-chart-仓库配置)
    - [添加 Helm Chart 镜像仓](#添加-helm-chart-镜像仓)
    - [拉取服务 Chart 包](#拉取服务-chart-包)
    - [推送服务 Chart 包](#推送服务-chart-包)
- [配置节点亲和性](#配置节点亲和性)
- [Appendix](#appendix)
    - [数据库准备](#数据库准备)
        - [DB 创建](#db-创建)
        - [数据表初始化](#数据表初始化)
        - [数据池的准备](#数据池的准备)
    - [RocketMQ Topic 创建](#rocketmq-topic-创建)

<!-- /TOC -->

## 基本信息
### 域名

域名 | 环境 | 解析地址
--- | --- | ---
network.cn-sh-01.sensecoreapi.cn | 生产.cn环境 | **************、**************、**************
network-internal.cn-sh-01.sensecoreapi.cn | 生产.cn环境 | ***********、***********
sdn-internal.cn-sh-01.sensecoreapi.cn | 生产.cn环境 | ***********、***********
--- | --- | ---
network.cn-sh-01.sensecoreapi.tech | 集成测试.tech环境 | 10.111.38.26
network-internal.cn-sh-01.sensecoreapi.tech | 集成测试.tech环境 | 10.111.38.29、10.111.38.30
sdn-internal.cn-sh-01.sensecoreapi.tech | 集成测试.tech环境 | 10.111.38.29、10.111.38.30
--- | --- | ---
network.cn-sh-01.sensecoreapi.dev | 开发验证.dev环境 | 10.142.52.247
network-internal.cn-sh-01.sensecoreapi.dev | 开发验证.dev环境 | 10.111.38.44
sdn-internal.cn-sh-01.sensecoreapi.dev | 开发验证.dev环境 | 10.111.38.44

### 基础环境

此文档相关初始化基于 docker v1.18.9+，kubectl v1.21.5，helm v3+

1. 验证环境机器安装 docker,kubectl,helm 并确认版本
2. 将 tk 集群的 /etc/kubernetes/admin.conf 文件重命名到本地 ~/.kube/config
3. `kubectl get node` 验证本地能连上 tk 集群

### 中间件依赖

在 kernel k8s 部署中间件，部署方式及部署后的连接信息获取 @匡赫鑫

中间件 | 版本 | CPU | 内存 | 硬盘
--- | --- | --- | --- | ---
PostgreSql | v11.9 | 0.5C | 1G | 100G
RocketMQ | v4.9.3 | 0.5C | 1G | 100G
Redis (暂无) | v6.2.7 | 0.5C | 1G | 100G

## 准备工作

### 确认当前环境

```bash
# 查看 k8s 版本
kubectl version
# Client Version: version.Info{Major:"1", Minor:"21", GitVersion:"v1.21.5", GitCommit:"aea7bbadd2fc0cd689de94a54e5b7b758869d691", GitTreeState:"clean", BuildDate:"2021-09-15T21:10:45Z", GoVersion:"go1.16.8", Compiler:"gc", Platform:"linux/amd64"}
# Server Version: version.Info{Major:"1", Minor:"21", GitVersion:"v1.21.5", GitCommit:"aea7bbadd2fc0cd689de94a54e5b7b758869d691", GitTreeState:"clean", BuildDate:"2021-09-15T21:04:16Z", GoVersion:"go1.16.8", Compiler:"gc", Platform:"linux/amd64"}

# 查看 k8s 节点个数是否为预期
kubectl get node

# 确认 helm 版本 > v3.5.0
helm3 version
# version.BuildInfo{Version:"v3.5.0", GitCommit:"32c22239423b3b4ba6706d450bd044baffdcf9e6", GitTreeState:"clean", GoVersion:"go1.15.6"}
```

### 创建 Boson 依赖的 Diamond TK 集群 namespace

```bash
kubectl create ns plat-boson-service
```
### 创建 Boson 镜像依赖的 Docker-Registry

```bash
kubectl create ns plat-boson-service

kubectl create -n plat-boson-service secret docker-registry sensecore-boson --docker-server=registry.sensetime.com --docker-username=<user_name> --docker-password=<user_password> --docker-email=<user_email>
```

### PostgreSql DB 创建

远程连接 pg

```bash
psql -U postgres -h <pg_host> -p <pg_port>
```

创建 DB boson_service_v2

```bash
create database boson_service_v2;
```

### RocketMQ Topic 初始化

创建 topic: (Topic 名 by @hueryang)

- sensetime-core-network-vpc-v1-cn-shanghai-01z
- sensetime-core-network-eip-v1-cn-shanghai-01a
- sensetime-core-rm-resource-state-sync
- sensetime-core-resource-operation-result

### NATGateway IP 申请

跟 @吴永金 申请 10 个与物理机相同网段的 `IPs`，及 ip 所属 `cidr` 及 cidr `gateway ip`，对应 `values.yaml` 里 `global.bosonProvider.init_pool.nat_gateways`

```yaml
global:
  bosonProvider:
    init_pool:
      nat_gateways:
        cidr: <cidr>
        gateway: <cidr gateway>
        ips:
        - <ip1>
        - <ip2>
```

### EIP 申请

跟 @吴永金 申请 5 个公网出口 IP，对应 `values.yaml` 里 `global.bosonProvider.init_pool.eips`

```yaml
global:
  bosonProvider:
    init_pool:
      eips:
        ips:
        - <ip1>
        - <ip2>
```

## Helm Chart 仓库配置

### 添加 Helm Chart 镜像仓

```bash
# 添加网络 sensecore-boson 镜像仓
helm3 repo add --username <username> --password <password> boson https://registry.sensetime.com/chartrepo/sensecore-boson

# 添加离线部署仓
helm3 repo add --username <username> --password <password> private https://registry.sensetime.com/chartrepo/private-delivery

# 添加济南城投私有化仓
helm3 repo add --username <username> --password <password> cn-jn-ct https://registry.sensetime.com/chartrepo/sensecore-cnjn-ct

```

### 拉取服务 Chart 包

以下命令仅为示例，具体命令行，参见各服务 Helm Chart 中的 README.md 文件的部署部分。

```bash
helm3 repo list

helm3 repo update

# pull latest chart version
helm3 pull boson/<boson-service-name>
# pull specific chart version
helm3 pull boson/<boson-service-name> --version x.y.z

tar -xzvf <boson-service-name>-x.y.z.tgz
```

### 推送服务 Chart 包

推送 chart 需要接触 helm 的 cm-push plugin

```bash
# 安装 cm-push
helm plugin install https://github.com/chartmuseum/helm-push

# 推送 chart
helm3 cm-push boson-net-controller_vx.y.z.tgz boson
```

## 配置节点亲和性

根据当前环境的 node labels 信息配置 `value.yaml` 中的变量 `global.nodeSelectorTerms` 的包含 rdma 网络的节点label。节点的 labels 信息可以通过 `kubectl get nodes --show-labels` 查看。

并根据当前环境的 node taints 信息配置变量`global.tolerations`。节点的 labels 信息可以通过 `kubectl describe nodes | grep -A 4 Taints` 查看 taint 配置。以下仅为配置示例，请根据实际情况配置。

```yaml
global:
  nodeSelectorTerms:
  - key: hardware-type
    values:
    - NVIDIAGPU
  - key: diamond.sensetime.com/role-business-app
    values:
    - sensecore
  tolerations:
  - key: diamond.sensetime.com/role-business-app
    value: sensecore
  - key: diamond.sensetime.com/role-business-acp
    value: enabled
  - key: diamond.sensetime.com/role-business-cci
    value: enabled
  - key: diamond.sensetime.com/role-business-ailab
    value: enabled
  - key: diamond.sensetime.com/role-k8s-master
    value: enabled
```

## Appendix

### 数据库准备

#### DB 创建

```
# 用容器启动一个初始化工具容器
docker run --rm -it --entrypoint bash registry.sensetime.com/elementary/boson-dev/boson-toolbox

# 远程连接 pg，连接信息根据环境配置
psql -U postgres -h ************ -p 51684

# 查看当前 DB 列表
\l

# 创建 DB，DB 名自定义
create database boson_xxx;
```

#### 数据表初始化

```
# 用容器启动一个初始化工具容器
docker run --rm -it --entrypoint bash registry.sensetime.com/elementary/boson-dev/boson-toolbox

# 配置 DB 相关信息并保存
vi boson-provider.yaml

# 初始化数据表
./syncTables

# 远程连接 pg，连接信息根据环境配置
psql -U postgres -h ************ -p 51684

# 进入 DB
\c boson_xxx

# 查看初始化的数据表
\d

```

#### 数据池的准备

- VpcNatGateway 池 + Cidr 池

    1. 申请一定数量可分配的 External Gateway IP，工单可参考：

    ![natgwip](./images/natgwip.png)

    2. 初始化 nat_gateway_external_ip_pools, cidr_pools 表

    ```
    # 用容器启动一个初始化工具容器
    docker run --rm -it --entrypoint bash registry.sensetime.com/elementary/boson-dev/boson-toolbox

    # 配置 DB 相关信息并保存
    vi boson-provider.yaml

    # 初始化 nat_gateway_external_ip_pools, cidr_pools 表
    # 第一个参数为申请到的 IP 对应的 CIDR，第二个参数为 CIDR 的网关，第三个参数为英文逗号分隔的 IPs
    ./initGwIPs ***********/22 *********** ***********07,*************,************

    # 远程连接 pg，连接信息根据环境配置
    psql -U postgres -h ************ -p 51684

    # 进入 DB
    \c boson_xxx

    # 查看初始化的数据
    select * from nat_gateway_external_ip_pools;
    select * from cidr_pools;
    ```

- EIP 池：TBD
- VLan 池：TBD

### RocketMQ Topic 创建

```
# 用容器启动一个初始化工具容器
docker run --rm -it --entrypoint bash registry.sensetime.com/elementary/boson-dev/boson-toolbox

# 配置 RocketMQ 包括 自定义 Topic 和 Group 的相关信息并保存
vi boson-provider.yaml

# 使用 boson-provider.yaml 中配置的 topic，rmAckTopic，bossAckTopic 创建到 RocketMQ
./createTopics

```
