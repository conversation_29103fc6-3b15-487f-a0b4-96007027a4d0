# 给已部署的环境新增可用的 NATGatewayIPs

## 申请可用 NATGatewayIPs

跟 @吴永金 申请，可用 IPs, CIDR, GatewayIP

## 使用 boson 提供的工具导入到 DB

1. 获取 boson 工具镜像

```
docker pull registry.sensetime.com/elementary/boson-dev/boson-toolbox
```

2. 启动并进入容器

```
docker run --rm -it --entrypoint bash registry.sensetime.com/elementary/boson-dev/boson-toolbox
```

3. 配置 boson-provider.yaml 中 pg 及 NATGatewayIPs 相关信息

```
vi boson-provider.yaml
...
pg:
  host: "************"
  port: "51684"
  user: "postgres"
  password: ""
  db: "boson_xxx"
...
init_pool:
  nat_gateways:
    cidr: xx.xx.xx.xx/xx
    gateway: xx.xx.xx.x
    ips:
    - xx.xx.xx.xx
    - xx.xx.xx.xx
...
```

4. 执行命令导入 DB

```
./initPools
```

5. 进入 DB 确认 IP 被正确加入到 nat_gateway_external_ip_pools 表
