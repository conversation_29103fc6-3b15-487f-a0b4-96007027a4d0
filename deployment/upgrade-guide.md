# Boson Service 升级

## 基于 Diamond UI 升级

1. 配置 /etc/hosts

************ d4s-hetao.sensetime.com

2. 登陆 https://d4s-hetao.sensetime.com

3. 选择已部署应用-应用

  ![upgrade1.png](./images/upgrade1.png)

4. 搜索 boson

  ![upgrade2.png](./images/upgrade2.png)

5. 点击 操作-升级-选择最新版本

  ![upgrade3.png](./images/upgrade3.png)
  ![upgrade4.png](./images/upgrade4.png)

6. 从 https://gitlab.bj.sensetime.com/elementary/sre/sensecore-values/-/tree/boson-values-4-tech/TK/boson 获取最新的 values.yaml 贴到文本框并添加 pgsql.password 配置

  ![upgrade5.png](./images/upgrade5.png)
  ![upgrade6.png](./images/upgrade6.png)
  ![upgrade7.png](./images/upgrade7.png)
  ![upgrade8.png](./images/upgrade8.png)

7. 点击确定等待服务升级完成

  ![upgrade9.png](./images/upgrade9.png)

8. 查看服务是否正常启动

  ![upgrade10.png](./images/upgrade10.png)
  ![upgrade11.png](./images/upgrade11.png)
